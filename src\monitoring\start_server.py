"""
Simple server starter to avoid indentation issues
"""

import http.server
import socketserver
import json
from pathlib import Path

class SimpleHandler(http.server.SimpleHTTPRequestHandler):
    def do_GET(self):
        if self.path == '/':
            self.serve_dashboard()
        elif self.path == '/api/dashboard':
            self.serve_api_data()
        else:
            super().do_GET()
    
    def serve_dashboard(self):
        """Serve the dashboard HTML"""
        try:
            html_file = Path("dashboard.html")
            if html_file.exists():
                with open(html_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                self.send_response(200)
                self.send_header('Content-type', 'text/html; charset=utf-8')
                self.end_headers()
                self.wfile.write(content.encode('utf-8'))
            else:
                self.send_error(404, "Dashboard not found")
        except Exception as e:
            self.send_error(500, f"Error: {e}")
    
    def serve_api_data(self):
        """Serve the API data"""
        try:
            json_file = Path("../../reports/unified_monitoring_dashboard.json")
            if json_file.exists():
                with open(json_file, 'r') as f:
                    data = json.load(f)
                
                self.send_response(200)
                self.send_header('Content-type', 'application/json')
                self.send_header('Access-Control-Allow-Origin', '*')
                self.end_headers()
                
                json_data = json.dumps(data)
                self.wfile.write(json_data.encode('utf-8'))
                
                print(f"✅ Served dashboard data with {len(data.get('live_games', []))} games")
            else:
                self.send_error(404, "Data not found")
        except Exception as e:
            self.send_error(500, f"Error: {e}")

def start_server(port=8080):
    """Start the simple server"""
    print(f"🏀 Starting WNBA Dashboard Server on port {port}...")
    
    with socketserver.TCPServer(("", port), SimpleHandler) as httpd:
        print(f"🚀 Server running at http://localhost:{port}")
        print(f"📊 Real WNBA games: MIN @ CHI, GSV @ LVA")
        print(f"🔴 Press Ctrl+C to stop")
        
        try:
            httpd.serve_forever()
        except KeyboardInterrupt:
            print("\n⏹️ Server stopped")

if __name__ == "__main__":
    start_server()
