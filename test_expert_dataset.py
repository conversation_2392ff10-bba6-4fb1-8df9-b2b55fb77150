#!/usr/bin/env python3
"""
🧪 TEST EXPERT DATASET
======================

Quick test to validate the expert dataset works with models
"""

import pandas as pd
import numpy as np

def test_expert_dataset():
    print('🧪 TESTING EXPERT DATASET WITH MODEL')
    print('=' * 40)

    # Load expert dataset
    print('📊 Loading expert dataset...')
    df = pd.read_csv('data/master/wnba_expert_dataset.csv')

    print(f'✅ Dataset loaded successfully!')
    print(f'   Records: {len(df):,}')
    print(f'   Features: {len(df.columns)}')
    print(f'   Memory usage: {df.memory_usage(deep=True).sum() / 1024**2:.1f} MB')

    # Check key columns
    key_cols = ['player_name', 'team_abbrev', 'target', 'minutes', 'game_date']
    missing_cols = [col for col in key_cols if col not in df.columns]

    if missing_cols:
        print(f'❌ Missing key columns: {missing_cols}')
    else:
        print('✅ All key columns present')

    # Check data quality
    print(f'📈 Data quality:')
    has_target = 'target' in df.columns
    print(f'   Target column: {has_target}')
    if has_target:
        non_null_targets = df['target'].notna().sum()
        print(f'   Non-null targets: {non_null_targets:,} ({non_null_targets/len(df)*100:.1f}%)')
        print(f'   Target range: {df["target"].min():.1f} - {df["target"].max():.1f}')
        print(f'   Average points: {df["target"].mean():.2f}')

    print('🎯 EXPERT DATASET IS READY FOR ALL MODELS!')
    return True

if __name__ == "__main__":
    test_expert_dataset()