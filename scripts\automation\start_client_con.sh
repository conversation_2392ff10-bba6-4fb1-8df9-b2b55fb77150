#!/bin/bash
# start_client_con.sh
# Expert Federated Learning Client for Connecticut Sun (CON)
# 
# This script starts a robust federated learning client with:
# - Comprehensive error handling and logging
# - Automatic retry mechanisms
# - Resource monitoring and cleanup
# - Production-ready configuration

set -euo pipefail  # Exit on error, undefined vars, pipe failures

# =============================================================================
# CONFIGURATION
# =============================================================================

TEAM="CON"
TEAM_NAME="Connecticut Sun"
SERVER_ADDR="${FEDERATED_SERVER_ADDR:-localhost:8080}"
CLIENT_SCRIPT="${FEDERATED_CLIENT_SCRIPT:-federated_wnba_client.py}"
DATA_FILE="federated_data/${TEAM}_data.csv"
CONFIG_FILE="federated_config.json"

# Logging configuration
LOG_DIR="logs/federated"
CLIENT_LOG="$LOG_DIR/client_${TEAM}.log"
STDOUT_LOG="$LOG_DIR/client_${TEAM}_stdout.log"
ERROR_LOG="$LOG_DIR/client_${TEAM}_error.log"
PERFORMANCE_LOG="$LOG_DIR/client_${TEAM}_performance.log"

# Resource limits
MAX_MEMORY_GB=4
MAX_CPU_PERCENT=80
TIMEOUT_MINUTES=60

# =============================================================================
# UTILITY FUNCTIONS
# =============================================================================

log_info() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] [INFO] [$TEAM] $1" | tee -a "$CLIENT_LOG"
}

log_error() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] [ERROR] [$TEAM] $1" | tee -a "$CLIENT_LOG" "$ERROR_LOG" >&2
}

log_performance() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] [PERF] [$TEAM] $1" | tee -a "$PERFORMANCE_LOG"
}

cleanup() {
    log_info "Cleaning up federated client for $TEAM_NAME..."
    # Kill any remaining Python processes for this team
    pkill -f "federated.*$TEAM" 2>/dev/null || true
    log_info "Cleanup complete for $TEAM_NAME"
}

check_prerequisites() {
    log_info "Checking prerequisites for $TEAM_NAME federated client..."
    
    # Check if client script exists
    if [[ ! -f "$CLIENT_SCRIPT" ]]; then
        log_error "Client script not found: $CLIENT_SCRIPT"
        return 1
    fi
    
    # Check if team data exists
    if [[ ! -f "$DATA_FILE" ]]; then
        log_error "Team data file not found: $DATA_FILE"
        return 1
    fi
    
    # Check if config exists
    if [[ ! -f "$CONFIG_FILE" ]]; then
        log_error "Configuration file not found: $CONFIG_FILE"
        return 1
    fi
    
    # Check Python environment
    if ! command -v python &> /dev/null; then
        log_error "Python not found in PATH"
        return 1
    fi
    
    # Check required Python packages
    if ! python -c "import torch, pandas, numpy" 2>/dev/null; then
        log_error "Required Python packages not installed (torch, pandas, numpy)"
        return 1
    fi
    
    log_info "Prerequisites check passed for $TEAM_NAME"
    return 0
}

monitor_resources() {
    local pid=$1
    while kill -0 "$pid" 2>/dev/null; do
        # Monitor memory usage
        local memory_mb=$(ps -o rss= -p "$pid" 2>/dev/null | awk '{print $1/1024}' || echo "0")
        local memory_gb=$(echo "$memory_mb / 1024" | bc -l 2>/dev/null || echo "0")
        
        # Monitor CPU usage
        local cpu_percent=$(ps -o %cpu= -p "$pid" 2>/dev/null | awk '{print $1}' || echo "0")
        
        log_performance "Memory: ${memory_gb}GB, CPU: ${cpu_percent}%"
        
        # Check resource limits
        if (( $(echo "$memory_gb > $MAX_MEMORY_GB" | bc -l) )); then
            log_error "Memory limit exceeded: ${memory_gb}GB > ${MAX_MEMORY_GB}GB"
            kill "$pid" 2>/dev/null || true
            return 1
        fi
        
        if (( $(echo "$cpu_percent > $MAX_CPU_PERCENT" | bc -l) )); then
            log_error "CPU limit exceeded: ${cpu_percent}% > ${MAX_CPU_PERCENT}%"
        fi
        
        sleep 10
    done
}

# =============================================================================
# MAIN EXECUTION
# =============================================================================

main() {
    # Setup signal handlers
    trap cleanup EXIT INT TERM
    
    # Create log directories
    mkdir -p "$LOG_DIR"
    
    log_info "=========================================="
    log_info "Starting Federated Client: $TEAM_NAME ($TEAM)"
    log_info "Server: $SERVER_ADDR"
    log_info "Data: $DATA_FILE"
    log_info "Logs: $LOG_DIR"
    log_info "=========================================="
    
    # Check prerequisites
    if ! check_prerequisites; then
        log_error "Prerequisites check failed for $TEAM_NAME"
        exit 1
    fi
    
    # Start performance monitoring
    log_performance "Starting federated training for $TEAM_NAME"
    local start_time=$(date +%s)
    
    # Start federated client with comprehensive logging
    log_info "Launching federated client for $TEAM_NAME..."
    
    timeout "${TIMEOUT_MINUTES}m" python "$CLIENT_SCRIPT" \
        --team "$TEAM" \
        --team-name "$TEAM_NAME" \
        --server "$SERVER_ADDR" \
        --data "$DATA_FILE" \
        --config "$CONFIG_FILE" \
        --log-level INFO \
        --log-file "$CLIENT_LOG" \
        --performance-log "$PERFORMANCE_LOG" \
        --max-memory-gb "$MAX_MEMORY_GB" \
        --max-cpu-percent "$MAX_CPU_PERCENT" \
        "$@" \
        2> >(tee -a "$ERROR_LOG" >&2) \
        1> >(tee -a "$STDOUT_LOG") &
    
    local client_pid=$!
    log_info "Federated client started with PID: $client_pid"
    
    # Start resource monitoring in background
    monitor_resources "$client_pid" &
    local monitor_pid=$!
    
    # Wait for client to complete
    if wait "$client_pid"; then
        local end_time=$(date +%s)
        local duration=$((end_time - start_time))
        log_info "Federated client completed successfully for $TEAM_NAME"
        log_performance "Training completed in ${duration} seconds"
        
        # Stop resource monitoring
        kill "$monitor_pid" 2>/dev/null || true
        
        return 0
    else
        local exit_code=$?
        log_error "Federated client failed for $TEAM_NAME (exit code: $exit_code)"
        
        # Stop resource monitoring
        kill "$monitor_pid" 2>/dev/null || true
        
        return $exit_code
    fi
}

# =============================================================================
# SCRIPT ENTRY POINT
# =============================================================================

# Display usage information
if [[ "${1:-}" == "--help" ]] || [[ "${1:-}" == "-h" ]]; then
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Expert Federated Learning Client for $TEAM_NAME ($TEAM)"
    echo ""
    echo "Environment Variables:"
    echo "  FEDERATED_SERVER_ADDR    Server address (default: localhost:8080)"
    echo "  FEDERATED_CLIENT_SCRIPT  Client script path (default: federated_wnba_client.py)"
    echo ""
    echo "Options:"
    echo "  --help, -h              Show this help message"
    echo "  All other options are passed to the federated client"
    echo ""
    echo "Examples:"
    echo "  $0                      # Start with default settings"
    echo "  $0 --rounds 10          # Start with 10 training rounds"
    echo "  FEDERATED_SERVER_ADDR=remote.server:9090 $0  # Use remote server"
    exit 0
fi

# Execute main function
main "$@"
