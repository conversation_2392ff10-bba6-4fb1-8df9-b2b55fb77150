#!/usr/bin/env python3
"""
🏀 WNBA NOISE REDUCTION & FEATURE SELECTION SYSTEM
================================================

Advanced noise reduction techniques for sports prediction models:
✅ Statistical outlier detection and removal
✅ Feature importance analysis and selection
✅ Correlation analysis and multicollinearity removal
✅ Label noise detection (garbage time, injuries, etc.)
✅ Temporal consistency validation
✅ Data quality scoring and filtering

Addresses noise in 637-feature WNBA dataset for better model performance.
"""

import pandas as pd
import numpy as np
import json
import logging
from pathlib import Path
from typing import Dict, List, Tuple, Any, Optional
from sklearn.feature_selection import SelectKBest, f_regression, mutual_info_regression
from sklearn.preprocessing import StandardScaler
from sklearn.ensemble import RandomForestRegressor
from sklearn.decomposition import PCA
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class WNBANoiseReductionSystem:
    """
    Comprehensive noise reduction system for WNBA prediction models
    
    Features:
    - Statistical outlier detection
    - Feature importance ranking
    - Multicollinearity removal
    - Label noise detection
    - Data quality assessment
    """
    
    def __init__(self, target_features: int = 200):
        self.target_features = target_features  # Reduce from 637 to ~200 best features
        self.outlier_threshold = 3.0  # Z-score threshold
        self.correlation_threshold = 0.95  # Remove highly correlated features
        self.importance_threshold = 0.001  # Minimum feature importance
        
        logger.info("🔍 WNBA Noise Reduction System Initialized")
        logger.info(f"🎯 Target features: {target_features} (from 637)")
        logger.info(f"📊 Outlier threshold: {self.outlier_threshold} std devs")
        logger.info(f"🔗 Correlation threshold: {self.correlation_threshold}")
    
    def detect_statistical_outliers(self, df: pd.DataFrame, feature_cols: List[str]) -> pd.DataFrame:
        """
        Detect and remove statistical outliers using multiple methods
        """
        logger.info("🔍 Detecting statistical outliers...")
        
        initial_count = len(df)
        outlier_mask = pd.Series(False, index=df.index)
        
        # Method 1: Z-score outliers
        for col in feature_cols:
            if col in df.columns:
                z_scores = np.abs(stats.zscore(df[col].fillna(0)))
                col_outliers = z_scores > self.outlier_threshold
                outlier_mask |= col_outliers
                
                if col_outliers.sum() > 0:
                    logger.info(f"   📊 {col}: {col_outliers.sum()} outliers (z-score > {self.outlier_threshold})")
        
        # Method 2: IQR outliers for target variable
        if 'target' in df.columns:
            Q1 = df['target'].quantile(0.25)
            Q3 = df['target'].quantile(0.75)
            IQR = Q3 - Q1
            lower_bound = Q1 - 1.5 * IQR
            upper_bound = Q3 + 1.5 * IQR
            
            target_outliers = (df['target'] < lower_bound) | (df['target'] > upper_bound)
            outlier_mask |= target_outliers
            
            logger.info(f"   🎯 Target outliers (IQR): {target_outliers.sum()}")
        
        # Remove outliers
        clean_df = df[~outlier_mask].copy()
        removed_count = initial_count - len(clean_df)
        
        logger.info(f"✅ Outlier removal: {removed_count:,} samples removed ({removed_count/initial_count*100:.1f}%)")
        logger.info(f"📊 Clean dataset: {len(clean_df):,} samples remaining")
        
        return clean_df
    
    def detect_label_noise(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Detect and flag label noise specific to basketball
        """
        logger.info("🏀 Detecting basketball-specific label noise...")
        
        noise_flags = pd.Series(False, index=df.index)
        
        # Flag 1: Garbage time detection (very high/low scores)
        if 'target' in df.columns:
            # Extreme scoring performances (likely garbage time or data errors)
            extreme_high = df['target'] > 40  # 40+ points is extremely rare in WNBA
            extreme_low = df['target'] < 0   # Negative points (data error)
            
            garbage_time = extreme_high | extreme_low
            noise_flags |= garbage_time
            
            logger.info(f"   🗑️ Garbage time/extreme scores: {garbage_time.sum()}")
        
        # Flag 2: Inconsistent minutes vs points
        if 'target' in df.columns and any('minutes' in col.lower() for col in df.columns):
            minutes_cols = [col for col in df.columns if 'minutes' in col.lower()]
            if minutes_cols:
                minutes_col = minutes_cols[0]
                # High points with very low minutes (likely data error)
                inconsistent = (df['target'] > 20) & (df[minutes_col] < 10)
                noise_flags |= inconsistent
                
                logger.info(f"   ⏰ Minutes/points inconsistency: {inconsistent.sum()}")
        
        # Flag 3: Missing critical data
        critical_cols = ['player_name', 'team_abbrev', 'year']
        for col in critical_cols:
            if col in df.columns:
                missing = df[col].isna()
                noise_flags |= missing
                
                if missing.sum() > 0:
                    logger.info(f"   ❌ Missing {col}: {missing.sum()}")
        
        # Remove noisy labels
        clean_df = df[~noise_flags].copy()
        removed_count = len(df) - len(clean_df)
        
        logger.info(f"✅ Label noise removal: {removed_count:,} samples removed")
        logger.info(f"📊 Clean labels: {len(clean_df):,} samples remaining")
        
        return clean_df
    
    def remove_multicollinearity(self, df: pd.DataFrame, feature_cols: List[str]) -> List[str]:
        """
        Remove highly correlated features to reduce multicollinearity
        """
        logger.info("🔗 Removing multicollinear features...")
        
        # Calculate correlation matrix
        feature_data = df[feature_cols].fillna(0)
        corr_matrix = feature_data.corr().abs()
        
        # Find highly correlated pairs
        upper_triangle = corr_matrix.where(
            np.triu(np.ones(corr_matrix.shape), k=1).astype(bool)
        )
        
        # Find features to remove
        to_remove = []
        for column in upper_triangle.columns:
            if any(upper_triangle[column] > self.correlation_threshold):
                correlated_features = upper_triangle.index[upper_triangle[column] > self.correlation_threshold].tolist()
                to_remove.extend(correlated_features)
                logger.info(f"   🔗 {column} highly correlated with: {correlated_features}")
        
        # Remove duplicates and create clean feature list
        to_remove = list(set(to_remove))
        clean_features = [col for col in feature_cols if col not in to_remove]
        
        logger.info(f"✅ Multicollinearity removal: {len(to_remove)} features removed")
        logger.info(f"📊 Remaining features: {len(clean_features)} (from {len(feature_cols)})")
        
        return clean_features
    
    def select_best_features(self, df: pd.DataFrame, feature_cols: List[str]) -> List[str]:
        """
        Select best features using multiple methods
        """
        logger.info(f"🎯 Selecting top {self.target_features} features...")
        
        X = df[feature_cols].fillna(0)
        y = df['target']
        
        # Method 1: Random Forest feature importance
        rf = RandomForestRegressor(n_estimators=100, random_state=42, n_jobs=-1)
        rf.fit(X, y)
        rf_importance = pd.Series(rf.feature_importances_, index=feature_cols)
        
        # Method 2: Mutual information
        mi_scores = mutual_info_regression(X, y, random_state=42)
        mi_importance = pd.Series(mi_scores, index=feature_cols)
        
        # Method 3: F-statistic
        f_scores, _ = f_regression(X, y)
        f_importance = pd.Series(f_scores, index=feature_cols)
        
        # Combine scores (weighted average)
        combined_scores = (
            0.5 * rf_importance / rf_importance.max() +
            0.3 * mi_importance / mi_importance.max() +
            0.2 * f_importance / f_importance.max()
        )
        
        # Select top features
        top_features = combined_scores.nlargest(self.target_features).index.tolist()
        
        logger.info(f"✅ Feature selection complete:")
        logger.info(f"   🌟 Top 10 features: {top_features[:10]}")
        logger.info(f"   📊 Selected: {len(top_features)} features")
        
        # Save feature importance rankings
        feature_rankings = {
            'random_forest': rf_importance.sort_values(ascending=False).to_dict(),
            'mutual_information': mi_importance.sort_values(ascending=False).to_dict(),
            'f_statistic': f_importance.sort_values(ascending=False).to_dict(),
            'combined_score': combined_scores.sort_values(ascending=False).to_dict(),
            'selected_features': top_features
        }
        
        rankings_path = Path('models/feature_rankings.json')
        with open(rankings_path, 'w') as f:
            json.dump(feature_rankings, f, indent=2)
        
        logger.info(f"📋 Feature rankings saved: {rankings_path}")
        
        return top_features
    
    def assess_data_quality(self, df: pd.DataFrame, feature_cols: List[str]) -> Dict[str, Any]:
        """
        Comprehensive data quality assessment
        """
        logger.info("📊 Assessing data quality...")
        
        quality_metrics = {
            'total_samples': len(df),
            'total_features': len(feature_cols),
            'missing_data_percentage': df[feature_cols].isna().sum().sum() / (len(df) * len(feature_cols)) * 100,
            'duplicate_rows': df.duplicated().sum(),
            'target_distribution': {
                'mean': float(df['target'].mean()),
                'std': float(df['target'].std()),
                'min': float(df['target'].min()),
                'max': float(df['target'].max()),
                'skewness': float(df['target'].skew())
            }
        }
        
        # Feature-level quality
        feature_quality = {}
        for col in feature_cols[:20]:  # Check first 20 features
            if col in df.columns:
                feature_quality[col] = {
                    'missing_percentage': float(df[col].isna().sum() / len(df) * 100),
                    'unique_values': int(df[col].nunique()),
                    'variance': float(df[col].var()) if df[col].dtype in ['int64', 'float64'] else 0
                }
        
        quality_metrics['feature_quality_sample'] = feature_quality
        
        logger.info(f"📊 Data Quality Summary:")
        logger.info(f"   📈 Samples: {quality_metrics['total_samples']:,}")
        logger.info(f"   🔧 Features: {quality_metrics['total_features']}")
        logger.info(f"   ❌ Missing data: {quality_metrics['missing_data_percentage']:.2f}%")
        logger.info(f"   🔄 Duplicates: {quality_metrics['duplicate_rows']}")
        logger.info(f"   🎯 Target mean: {quality_metrics['target_distribution']['mean']:.2f}")
        
        return quality_metrics
    
    def apply_noise_reduction(self, df: pd.DataFrame, feature_cols: List[str]) -> Tuple[pd.DataFrame, List[str], Dict[str, Any]]:
        """
        Apply comprehensive noise reduction pipeline
        """
        logger.info("🚀 STARTING COMPREHENSIVE NOISE REDUCTION")
        logger.info("=" * 60)
        
        initial_samples = len(df)
        initial_features = len(feature_cols)
        
        # Step 1: Remove statistical outliers
        df_clean = self.detect_statistical_outliers(df, feature_cols)
        
        # Step 2: Remove label noise
        df_clean = self.detect_label_noise(df_clean)
        
        # Step 3: Remove multicollinear features
        clean_features = self.remove_multicollinearity(df_clean, feature_cols)
        
        # Step 4: Select best features
        best_features = self.select_best_features(df_clean, clean_features)
        
        # Step 5: Assess final data quality
        quality_metrics = self.assess_data_quality(df_clean, best_features)
        
        # Summary
        final_samples = len(df_clean)
        final_features = len(best_features)
        
        summary = {
            'initial_samples': initial_samples,
            'final_samples': final_samples,
            'samples_removed': initial_samples - final_samples,
            'sample_retention_rate': final_samples / initial_samples,
            'initial_features': initial_features,
            'final_features': final_features,
            'features_removed': initial_features - final_features,
            'feature_reduction_rate': (initial_features - final_features) / initial_features,
            'quality_metrics': quality_metrics,
            'selected_features': best_features
        }
        
        logger.info("✅ NOISE REDUCTION COMPLETE")
        logger.info("=" * 60)
        logger.info(f"📊 Sample reduction: {initial_samples:,} → {final_samples:,} ({summary['sample_retention_rate']*100:.1f}% retained)")
        logger.info(f"🔧 Feature reduction: {initial_features} → {final_features} ({summary['feature_reduction_rate']*100:.1f}% reduction)")
        logger.info(f"🎯 Expected: Better model performance, reduced overfitting")
        
        return df_clean, best_features, summary

def test_noise_reduction():
    """Test the noise reduction system"""
    
    logger.info("🧪 Testing WNBA Noise Reduction System...")
    
    # Load master dataset
    data_path = Path("data/master/wnba_definitive_master_dataset_FIXED.csv")
    if not data_path.exists():
        logger.error("❌ Master dataset not found!")
        return
    
    # Load sample data
    df = pd.read_csv(data_path, nrows=1000)  # Test with 1000 samples
    
    # Define feature columns
    exclude_cols = [
        'target', 'player_name', 'team_abbrev', 'game_id', 'game_date', 
        'year', 'player_id', 'team_id', 'season', 'SEASON_ID', 'GAME_DATE',
        'collection_date'
    ]
    feature_cols = [col for col in df.columns if col not in exclude_cols]
    
    # Initialize noise reduction system
    noise_reducer = WNBANoiseReductionSystem(target_features=100)  # Reduce to 100 for testing
    
    # Apply noise reduction
    clean_df, best_features, summary = noise_reducer.apply_noise_reduction(df, feature_cols)
    
    logger.info("🎉 Noise reduction test completed successfully!")
    return clean_df, best_features, summary

if __name__ == "__main__":
    test_noise_reduction()
