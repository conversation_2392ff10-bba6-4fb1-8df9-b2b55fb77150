#!/usr/bin/env python3
"""
🎯 TRAIN CORRECT WNBA MODELS - PROFESSIONAL RETRAINING
====================================================

Train the ACTUAL models from our codebase:
1. PlayerPointsModel (Enhanced base model)
2. HybridPlayerPointsModel (Tabular + GNN) 
3. MultiTaskPlayerModel (Multi-task learning)
4. BayesianPlayerModel (Uncertainty quantification)
5. FederatedPlayerModel (Federated learning)

Target: <2.0 MAE professional accuracy
"""

import pandas as pd
import numpy as np
import torch
import torch.nn as nn
import pytorch_lightning as pl
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler, RobustScaler
from sklearn.metrics import mean_absolute_error
import json
from datetime import datetime, timedelta
from pathlib import Path
import sys
import warnings
warnings.filterwarnings('ignore')

# Add src to path for our actual models
sys.path.append('src')
sys.path.append('src/models')

# Import our ACTUAL models
from models.modern_player_points_model import (
    PlayerPointsModel,
    HybridPlayerPointsModel, 
    MultiTaskPlayerModel,
    BayesianPlayerModel,
    FederatedPlayerModel,
    PlayerPointsDataset
)

class CorrectModelTrainer:
    """Train our actual WNBA models properly"""
    
    def __init__(self):
        """Initialize the correct model trainer"""
        print("🎯 TRAIN CORRECT WNBA MODELS - PROFESSIONAL RETRAINING")
        print("=" * 65)
        print("🔧 Training our ACTUAL models from the codebase")
        print("🎯 Target: <2.0 MAE professional accuracy")
        
    def load_and_prepare_wnba_data(self):
        """Load and prepare WNBA data properly"""
        try:
            print("\n📊 Loading WNBA data...")
            
            # Load the master dataset
            df = pd.read_csv('data/master/wnba_definitive_master_dataset_FIXED.csv', low_memory=False)
            df['game_date'] = pd.to_datetime(df['game_date'], errors='coerce')
            
            print(f"   Raw data: {len(df)} records, {len(df.columns)} columns")
            
            # Get target variable
            if 'points' in df.columns:
                target_col = 'points'
            elif 'target' in df.columns:
                target_col = 'target'
            else:
                print("❌ No target column found")
                return None
            
            # Clean data properly
            print("   🧹 Cleaning WNBA data...")
            
            # Remove invalid targets (reasonable WNBA range)
            df = df[
                (df[target_col].notna()) & 
                (df[target_col] >= 0) & 
                (df[target_col] <= 50) &  # WNBA single-game range
                (df['game_date'].notna())
            ].copy()
            
            print(f"   After cleaning: {len(df)} records")
            
            # Feature selection for WNBA
            print("   🎯 Selecting WNBA features...")
            
            exclude_cols = {
                'points', 'target', 'player_id', 'game_id', 'game_date', 
                'player_name', 'team', 'opponent', 'season', 'Unnamed: 0'
            }
            
            # Get potential features
            potential_features = [col for col in df.columns if col not in exclude_cols]
            
            # Select high-quality features
            wnba_features = []
            for col in potential_features:
                try:
                    # Convert to numeric
                    df[col] = pd.to_numeric(df[col], errors='coerce')
                    
                    if df[col].dtype in ['float64', 'int64']:
                        # Check data quality
                        missing_pct = df[col].isnull().sum() / len(df)
                        unique_values = df[col].nunique()
                        
                        # Keep high-quality WNBA features
                        if missing_pct < 0.5 and unique_values > 1:  # <50% missing, has variance
                            wnba_features.append(col)
                            
                            # Reasonable feature count for WNBA
                            if len(wnba_features) >= 80:
                                break
                except:
                    continue
            
            print(f"   Selected {len(wnba_features)} WNBA features")
            
            # Fill missing values
            for col in wnba_features:
                median_val = df[col].median()
                if pd.isna(median_val):
                    median_val = 0.0
                df[col] = df[col].fillna(median_val)
            
            # Create feature matrix
            X = df[wnba_features].values.astype(np.float32)
            y = df[target_col].values.astype(np.float32)
            
            # Create role IDs (simplified: based on minutes played if available)
            role_ids = np.ones(len(df), dtype=int)  # Default to rotation players
            if 'minutes' in df.columns or 'min' in df.columns:
                min_col = 'minutes' if 'minutes' in df.columns else 'min'
                minutes = df[min_col].fillna(0)
                # Elite: >25 min, Rotation: 10-25 min, Bench: <10 min
                role_ids = np.where(minutes > 25, 2, np.where(minutes > 10, 1, 0))
            
            # Remove extreme outliers
            print("   🔧 Removing extreme outliers...")
            
            # Target outliers
            target_q1, target_q99 = np.percentile(y, [1, 99])
            outlier_mask = (y >= target_q1) & (y <= target_q99)
            
            X = X[outlier_mask]
            y = y[outlier_mask]
            role_ids = role_ids[outlier_mask]
            
            print(f"   Final data: {X.shape[0]} samples, {X.shape[1]} features")
            print(f"   Target stats: mean={np.mean(y):.1f}, std={np.std(y):.1f}, range=[{np.min(y):.1f}, {np.max(y):.1f}]")
            print(f"   Role distribution: Elite={np.sum(role_ids==2)}, Rotation={np.sum(role_ids==1)}, Bench={np.sum(role_ids==0)}")
            
            return X, y, role_ids, wnba_features
            
        except Exception as e:
            print(f"❌ Error preparing WNBA data: {e}")
            return None
    
    def create_train_test_split(self, X, y, role_ids):
        """Create proper train/test split for WNBA"""
        print("\n🔄 Creating train/test split...")
        
        # Stratified split by target ranges
        y_bins = pd.cut(y, bins=5, labels=False)
        
        X_train, X_test, y_train, y_test, role_train, role_test = train_test_split(
            X, y, role_ids,
            test_size=0.2, 
            random_state=42,
            stratify=y_bins
        )
        
        print(f"   Train: {len(X_train)} samples")
        print(f"   Test: {len(X_test)} samples")
        print(f"   Train target: mean={np.mean(y_train):.1f}, std={np.std(y_train):.1f}")
        print(f"   Test target: mean={np.mean(y_test):.1f}, std={np.std(y_test):.1f}")
        
        return X_train, X_test, y_train, y_test, role_train, role_test
    
    def scale_features(self, X_train, X_test):
        """Scale features properly"""
        print("\n📏 Scaling features...")
        
        # Use StandardScaler for neural networks
        scaler = StandardScaler()
        
        X_train_scaled = scaler.fit_transform(X_train)
        X_test_scaled = scaler.transform(X_test)
        
        print(f"   Scaled train: mean={np.mean(X_train_scaled):.3f}, std={np.mean(np.std(X_train_scaled, axis=0)):.3f}")
        print(f"   Scaled test: mean={np.mean(X_test_scaled):.3f}, std={np.mean(np.std(X_test_scaled, axis=0)):.3f}")
        
        return X_train_scaled, X_test_scaled, scaler
    
    def create_data_loaders(self, X_train, X_test, y_train, y_test, role_train, role_test):
        """Create PyTorch data loaders"""
        print("\n📦 Creating data loaders...")
        
        # Create datasets
        train_dataset = PlayerPointsDataset(X_train, y_train, role_train)
        test_dataset = PlayerPointsDataset(X_test, y_test, role_test)
        
        # Create data loaders
        train_loader = torch.utils.data.DataLoader(
            train_dataset,
            batch_size=64,
            shuffle=True,
            num_workers=0,
            pin_memory=True
        )
        
        test_loader = torch.utils.data.DataLoader(
            test_dataset,
            batch_size=64,
            shuffle=False,
            num_workers=0,
            pin_memory=True
        )
        
        print(f"   Train loader: {len(train_loader)} batches")
        print(f"   Test loader: {len(test_loader)} batches")
        
        return train_loader, test_loader
    
    def train_enhanced_model(self, train_loader, test_loader, input_dim):
        """Train the Enhanced PlayerPointsModel"""
        print("\n🤖 Training Enhanced PlayerPointsModel...")
        
        # Create model
        model = PlayerPointsModel(
            input_dim=input_dim,
            dropout=0.3,
            learning_rate=1e-3,
            use_role_embedding=True
        )
        
        # Setup trainer
        trainer = pl.Trainer(
            max_epochs=50,
            accelerator='auto',
            devices=1,
            callbacks=[
                pl.callbacks.EarlyStopping(
                    monitor='val_loss',
                    patience=10,
                    mode='min'
                ),
                pl.callbacks.ModelCheckpoint(
                    monitor='val_loss',
                    mode='min',
                    save_top_k=1,
                    filename='enhanced_model_{epoch:02d}_{val_loss:.4f}',
                    dirpath='models/enhanced_model'
                )
            ],
            enable_progress_bar=True
        )
        
        # Train
        trainer.fit(model, train_loader, test_loader)
        
        # Get best model
        best_model_path = trainer.checkpoint_callback.best_model_path
        best_model = PlayerPointsModel.load_from_checkpoint(best_model_path)
        best_model.eval()
        
        print(f"   ✅ Enhanced model trained!")
        print(f"   📁 Best model: {best_model_path}")
        
        return best_model, best_model_path
    
    def train_multitask_model(self, train_loader, test_loader, input_dim):
        """Train the MultiTask model"""
        print("\n🎯 Training MultiTaskPlayerModel...")
        
        # Create model
        model = MultiTaskPlayerModel(
            input_dim=input_dim,
            dropout=0.3,
            learning_rate=5e-4,
            use_role_embedding=True
        )
        
        # Setup trainer
        trainer = pl.Trainer(
            max_epochs=50,
            accelerator='auto',
            devices=1,
            callbacks=[
                pl.callbacks.EarlyStopping(
                    monitor='val_loss',
                    patience=10,
                    mode='min'
                ),
                pl.callbacks.ModelCheckpoint(
                    monitor='val_loss',
                    mode='min',
                    save_top_k=1,
                    filename='multitask_model_{epoch:02d}_{val_loss:.4f}',
                    dirpath='models/multitask_model'
                )
            ],
            enable_progress_bar=True
        )
        
        # Train
        trainer.fit(model, train_loader, test_loader)
        
        # Get best model
        best_model_path = trainer.checkpoint_callback.best_model_path
        best_model = MultiTaskPlayerModel.load_from_checkpoint(best_model_path)
        best_model.eval()
        
        print(f"   ✅ MultiTask model trained!")
        print(f"   📁 Best model: {best_model_path}")
        
        return best_model, best_model_path
    
    def train_bayesian_model(self, train_loader, test_loader, input_dim):
        """Train the Bayesian model"""
        print("\n🎲 Training BayesianPlayerModel...")
        
        # Create model
        model = BayesianPlayerModel(
            input_dim=input_dim,
            dropout=0.3,
            learning_rate=1e-3,
            use_role_embedding=True
        )
        
        # Setup trainer
        trainer = pl.Trainer(
            max_epochs=50,
            accelerator='auto',
            devices=1,
            callbacks=[
                pl.callbacks.EarlyStopping(
                    monitor='val_loss',
                    patience=10,
                    mode='min'
                ),
                pl.callbacks.ModelCheckpoint(
                    monitor='val_loss',
                    mode='min',
                    save_top_k=1,
                    filename='bayesian_model_{epoch:02d}_{val_loss:.4f}',
                    dirpath='models/bayesian_model'
                )
            ],
            enable_progress_bar=True
        )
        
        # Train
        trainer.fit(model, train_loader, test_loader)
        
        # Get best model
        best_model_path = trainer.checkpoint_callback.best_model_path
        best_model = BayesianPlayerModel.load_from_checkpoint(best_model_path)
        best_model.eval()
        
        print(f"   ✅ Bayesian model trained!")
        print(f"   📁 Best model: {best_model_path}")
        
        return best_model, best_model_path
    
    def test_model_thoroughly(self, model, test_loader, model_name):
        """Test model thoroughly"""
        print(f"\n🧪 Testing {model_name} thoroughly...")
        
        model.eval()
        all_predictions = []
        all_targets = []
        
        with torch.no_grad():
            for batch in test_loader:
                if len(batch) == 3:
                    x, y, role_ids = batch
                    if hasattr(model, 'forward') and 'role_ids' in model.forward.__code__.co_varnames:
                        predictions = model(x, role_ids)
                    else:
                        predictions = model(x)
                else:
                    x, y = batch
                    predictions = model(x)
                
                # Handle different output types
                if isinstance(predictions, dict):
                    predictions = predictions['points']
                
                all_predictions.extend(predictions.cpu().numpy())
                all_targets.extend(y.cpu().numpy())
        
        all_predictions = np.array(all_predictions)
        all_targets = np.array(all_targets)
        
        # Calculate metrics
        mae = mean_absolute_error(all_targets, all_predictions)
        rmse = np.sqrt(np.mean((all_predictions - all_targets) ** 2))
        mape = np.mean(np.abs((all_targets - all_predictions) / np.maximum(all_targets, 1))) * 100
        correlation = np.corrcoef(all_predictions, all_targets)[0, 1]
        
        print(f"   📊 {model_name} Results:")
        print(f"      🎯 MAE: {mae:.3f} points")
        print(f"      📈 RMSE: {rmse:.3f} points")
        print(f"      📊 MAPE: {mape:.1f}%")
        print(f"      🔗 Correlation: {correlation:.3f}")
        print(f"      ⚖️ Avg Target: {np.mean(all_targets):.1f} points")
        print(f"      🔮 Avg Predicted: {np.mean(all_predictions):.1f} points")
        
        # Assessment
        if mae < 2.0:
            status = "🏆 EXCELLENT - Professional grade!"
        elif mae < 3.0:
            status = "✅ GOOD - Production ready"
        elif mae < 4.0:
            status = "⚠️ ACCEPTABLE - Needs monitoring"
        else:
            status = "❌ POOR - Requires improvement"
        
        print(f"      📊 Status: {status}")
        
        return {
            'mae': mae,
            'rmse': rmse,
            'mape': mape,
            'correlation': correlation,
            'status': status,
            'predictions': all_predictions.tolist(),
            'targets': all_targets.tolist()
        }
    
    def run_correct_training(self):
        """Run the complete correct model training"""
        print("🚀 Starting correct WNBA model training...")
        
        # Step 1: Load and prepare data
        data = self.load_and_prepare_wnba_data()
        if data is None:
            return False
        
        X, y, role_ids, features = data
        
        # Step 2: Create train/test split
        X_train, X_test, y_train, y_test, role_train, role_test = self.create_train_test_split(X, y, role_ids)
        
        # Step 3: Scale features
        X_train_scaled, X_test_scaled, scaler = self.scale_features(X_train, X_test)
        
        # Step 4: Create data loaders
        train_loader, test_loader = self.create_data_loaders(
            X_train_scaled, X_test_scaled, y_train, y_test, role_train, role_test
        )
        
        # Step 5: Train models
        input_dim = X_train_scaled.shape[1]
        trained_models = {}
        
        # Train Enhanced Model
        enhanced_model, enhanced_path = self.train_enhanced_model(train_loader, test_loader, input_dim)
        enhanced_results = self.test_model_thoroughly(enhanced_model, test_loader, "Enhanced")
        trained_models['enhanced'] = {
            'model': enhanced_model,
            'path': enhanced_path,
            'results': enhanced_results
        }
        
        # Train MultiTask Model
        multitask_model, multitask_path = self.train_multitask_model(train_loader, test_loader, input_dim)
        multitask_results = self.test_model_thoroughly(multitask_model, test_loader, "MultiTask")
        trained_models['multitask'] = {
            'model': multitask_model,
            'path': multitask_path,
            'results': multitask_results
        }
        
        # Train Bayesian Model
        bayesian_model, bayesian_path = self.train_bayesian_model(train_loader, test_loader, input_dim)
        bayesian_results = self.test_model_thoroughly(bayesian_model, test_loader, "Bayesian")
        trained_models['bayesian'] = {
            'model': bayesian_model,
            'path': bayesian_path,
            'results': bayesian_results
        }
        
        # Step 6: Save results
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        training_summary = {
            'timestamp': datetime.now().isoformat(),
            'input_dim': input_dim,
            'features': features,
            'training_samples': len(X_train),
            'test_samples': len(X_test),
            'scaler_params': {
                'mean': scaler.mean_.tolist(),
                'scale': scaler.scale_.tolist()
            },
            'models': {
                name: {
                    'path': info['path'],
                    'mae': info['results']['mae'],
                    'rmse': info['results']['rmse'],
                    'correlation': info['results']['correlation'],
                    'status': info['results']['status']
                } for name, info in trained_models.items()
            }
        }
        
        summary_path = f"correct_models_training_summary_{timestamp}.json"
        with open(summary_path, 'w') as f:
            json.dump(training_summary, f, indent=2, default=float)
        
        print(f"\n💾 Training summary saved to: {summary_path}")
        
        # Final assessment
        best_model = min(trained_models.keys(), key=lambda k: trained_models[k]['results']['mae'])
        best_mae = trained_models[best_model]['results']['mae']
        
        print(f"\n🏆 CORRECT MODELS TRAINING COMPLETE!")
        print("=" * 50)
        print(f"🥇 Best Model: {best_model.upper()} (MAE: {best_mae:.3f})")
        
        for name, info in sorted(trained_models.items(), key=lambda x: x[1]['results']['mae']):
            mae = info['results']['mae']
            status = info['results']['status']
            print(f"   {name.upper()}: {mae:.3f} MAE - {status}")
        
        if best_mae < 2.0:
            print(f"\n🎉 SUCCESS: Professional-grade WNBA models achieved!")
            print(f"   ✅ Ready for production deployment")
        elif best_mae < 4.0:
            print(f"\n✅ GOOD: Solid WNBA models trained!")
            print(f"   ✅ Acceptable for production use")
        else:
            print(f"\n⚠️ NEEDS IMPROVEMENT: Models require optimization")
        
        return best_mae < 4.0


def main():
    """Main function"""
    trainer = CorrectModelTrainer()
    success = trainer.run_correct_training()
    
    if success:
        print("\n✅ CORRECT WNBA MODELS TRAINING COMPLETED!")
        print("🏀 Professional WNBA prediction models ready!")
    else:
        print("\n❌ Correct models training failed")


if __name__ == "__main__":
    main()
