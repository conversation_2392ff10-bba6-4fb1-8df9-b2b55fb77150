#!/usr/bin/env python3
"""
Odds API Roster Manager for WNBA Teams
Automatically fetches and updates team rosters using the Odds API
"""

import requests
import json
import pandas as pd
from pathlib import Path
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
import time

class OddsAPIRosterManager:
    """Manages WNBA team rosters using the Odds API"""
    
    def __init__(self, api_key: str):
        """
        Initialize the Odds API Roster Manager
        
        Args:
            api_key: Your Odds API key
        """
        self.api_key = api_key
        self.base_url = "https://api.the-odds-api.com/v4"
        self.sport = "basketball_wnba"
        self.session = requests.Session()
        
        # Load real mappings
        self.real_mappings = self._load_real_mappings()
        
        # WNBA team mapping (API names to our abbreviations)
        self.team_name_mapping = {
            'Atlanta Dream': 'ATL',
            'Chicago Sky': 'CHI', 
            'Connecticut Sun': 'CON',
            'Dallas Wings': 'DAL',
            'Golden State Valkyries': 'GSV',
            'Indiana Fever': 'IND',
            'Los Angeles Sparks': 'LAS',
            'Las Vegas Aces': 'LV',
            'Minnesota Lynx': 'MIN',
            'New York Liberty': 'NYL',
            'Phoenix Mercury': 'PHO',
            'Seattle Storm': 'SEA',
            'Washington Mystics': 'WAS'
        }
        
        print(f"✅ Odds API Roster Manager initialized")
        print(f"   API Key: {api_key[:8]}...")
        print(f"   Real mappings loaded: {len(self.real_mappings['players'])} players")
    
    def _load_real_mappings(self) -> Dict[str, Any]:
        """Load real WNBA mappings"""
        mappings_dir = Path("consolidated_wnba/mappings")
        
        if not mappings_dir.exists():
            raise FileNotFoundError("Real mappings not found! Run create_real_mappings.py first.")
        
        with open(mappings_dir / 'real_team_mappings.json', 'r') as f:
            team_mappings = json.load(f)
        
        with open(mappings_dir / 'real_player_mappings.json', 'r') as f:
            player_mappings = json.load(f)
        
        with open(mappings_dir / 'player_name_to_id.json', 'r') as f:
            player_name_to_id = json.load(f)
        
        return {
            'teams': team_mappings,
            'players': player_mappings,
            'name_to_id': player_name_to_id
        }
    
    def check_api_usage(self) -> Dict[str, Any]:
        """Check current API usage"""
        url = f"{self.base_url}/sports"
        params = {'apiKey': self.api_key}
        
        try:
            response = self.session.get(url, params=params)
            
            # Get usage from headers
            usage_info = {
                'requests_used': response.headers.get('x-requests-used', 'Unknown'),
                'requests_remaining': response.headers.get('x-requests-remaining', 'Unknown'),
                'status_code': response.status_code
            }
            
            print(f"📊 API Usage: {usage_info['requests_used']} used, {usage_info['requests_remaining']} remaining")
            return usage_info
            
        except Exception as e:
            print(f"❌ Error checking API usage: {e}")
            return {}
    
    def get_wnba_teams(self) -> List[Dict[str, Any]]:
        """Get list of WNBA teams from Odds API"""
        url = f"{self.base_url}/sports/{self.sport}/odds"
        params = {
            'apiKey': self.api_key,
            'regions': 'us',
            'markets': 'h2h',
            'oddsFormat': 'decimal'
        }
        
        try:
            response = self.session.get(url, params=params)
            response.raise_for_status()
            
            games = response.json()
            teams = set()
            
            for game in games:
                if 'bookmakers' in game and len(game['bookmakers']) > 0:
                    teams.add(game['home_team'])
                    teams.add(game['away_team'])
            
            team_list = [{'name': team, 'abbrev': self.team_name_mapping.get(team, 'UNK')} 
                        for team in sorted(teams)]
            
            print(f"✅ Found {len(team_list)} WNBA teams from Odds API")
            return team_list
            
        except Exception as e:
            print(f"❌ Error fetching WNBA teams: {e}")
            return []
    
    def get_team_roster_from_games(self, team_name: str, days_back: int = 30) -> List[Dict[str, Any]]:
        """
        Extract team roster from recent game data
        Note: Odds API doesn't have direct roster endpoints, so we infer from games
        """
        url = f"{self.base_url}/sports/{self.sport}/odds"
        params = {
            'apiKey': self.api_key,
            'regions': 'us',
            'markets': 'h2h'
        }
        
        try:
            response = self.session.get(url, params=params)
            response.raise_for_status()
            
            games = response.json()
            team_games = [g for g in games if g['home_team'] == team_name or g['away_team'] == team_name]
            
            print(f"📅 Found {len(team_games)} recent games for {team_name}")
            
            # For now, return placeholder roster based on our real mappings
            team_abbrev = self.team_name_mapping.get(team_name, 'UNK')
            roster = self._get_roster_from_mappings(team_abbrev)
            
            return roster
            
        except Exception as e:
            print(f"❌ Error fetching roster for {team_name}: {e}")
            return []
    
    def _get_roster_from_mappings(self, team_abbrev: str) -> List[Dict[str, Any]]:
        """Get current roster from our real mappings"""
        roster = []
        
        for player_id, player_info in self.real_mappings['players'].items():
            if player_info.get('team_abbrev') == team_abbrev:
                # Check if player is active in 2025
                years = player_info.get('years', [])
                if 2025 in years:
                    roster.append({
                        'player_id': int(player_id),
                        'name': player_info['name'],
                        'position': player_info.get('primary_position', 'Unknown'),
                        'team_abbrev': team_abbrev,
                        'status': 'active',
                        'source': 'real_mappings'
                    })
        
        return roster
    
    def sync_all_rosters(self) -> Dict[str, List[Dict[str, Any]]]:
        """Sync rosters for all WNBA teams"""
        print(f"\n🔄 SYNCING ALL WNBA ROSTERS")
        print(f"=" * 50)
        
        # Check API usage first
        self.check_api_usage()
        
        # Get teams
        teams = self.get_wnba_teams()
        all_rosters = {}
        
        for team in teams:
            team_name = team['name']
            team_abbrev = team['abbrev']
            
            print(f"\n📋 Syncing roster for {team_name} ({team_abbrev})...")
            
            # Get roster (currently from mappings, can be enhanced with API data)
            roster = self.get_team_roster_from_games(team_name)
            all_rosters[team_abbrev] = roster
            
            print(f"   ✅ {len(roster)} players found")
            
            # Rate limiting
            time.sleep(0.5)
        
        # Save updated rosters
        self._save_updated_rosters(all_rosters)
        
        print(f"\n✅ ROSTER SYNC COMPLETE")
        print(f"   Teams synced: {len(all_rosters)}")
        print(f"   Total players: {sum(len(roster) for roster in all_rosters.values())}")
        
        return all_rosters
    
    def _save_updated_rosters(self, rosters: Dict[str, List[Dict[str, Any]]]) -> None:
        """Save updated rosters to file"""
        output_dir = Path("consolidated_wnba/mappings")
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # Save as JSON
        with open(output_dir / 'current_rosters.json', 'w') as f:
            json.dump(rosters, f, indent=2)
        
        # Save as CSV for easy viewing
        all_players = []
        for team_abbrev, roster in rosters.items():
            for player in roster:
                player_data = player.copy()
                player_data['team'] = team_abbrev
                all_players.append(player_data)
        
        df = pd.DataFrame(all_players)
        df.to_csv(output_dir / 'current_rosters.csv', index=False)
        
        print(f"💾 Rosters saved to {output_dir}/")
    
    def get_player_by_name(self, player_name: str) -> Optional[Dict[str, Any]]:
        """Find player by name in current rosters"""
        player_name_lower = player_name.lower()
        
        # Check in real mappings first
        player_id = self.real_mappings['name_to_id'].get(player_name_lower)
        if player_id and str(player_id) in self.real_mappings['players']:
            return self.real_mappings['players'][str(player_id)]
        
        return None
    
    def predict_team_lineup(self, team_abbrev: str, game_date: str = None) -> List[Dict[str, Any]]:
        """Predict starting lineup for a team"""
        roster = self._get_roster_from_mappings(team_abbrev)
        
        # For now, return all active players
        # This can be enhanced with injury reports, recent performance, etc.
        active_players = [p for p in roster if p['status'] == 'active']
        
        print(f"🏀 {team_abbrev} predicted lineup: {len(active_players)} players")
        return active_players

if __name__ == "__main__":
    # Test the Odds API Roster Manager
    API_KEY = "6b834a837e6c14b85f25949449bb2296"
    
    manager = OddsAPIRosterManager(API_KEY)
    
    # Test API connection
    manager.check_api_usage()
    
    # Sync all rosters
    rosters = manager.sync_all_rosters()
    
    # Test player lookup
    test_players = ["A'ja Wilson", "Breanna Stewart", "Tiffany Hayes"]
    for player_name in test_players:
        player_info = manager.get_player_by_name(player_name)
        if player_info:
            print(f"✅ Found {player_name}: {player_info['team_abbrev']} - {player_info.get('primary_position', 'Unknown')}")
        else:
            print(f"❌ {player_name} not found")
