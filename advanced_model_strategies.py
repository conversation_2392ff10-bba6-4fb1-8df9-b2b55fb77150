#!/usr/bin/env python3
"""
🎯 ADVANCED MODEL STRATEGIES FOR WNBA PREDICTION
===============================================

Professional-grade model enhancements based on variance analysis:
1. Model Confidence Calibration with uncertainty quantification
2. Volatility-Aware Ensemble Voting with dynamic weighting
3. Backtest Bias Correction with tier-weighted evaluation
4. Outlier Handling Mode with risk-aware predictions
5. Elite Scorer Profile Enrichment with behavioral embeddings
6. Pretraining Phase with Role Targets for context awareness

These techniques are used by professional analytics firms and NBA/WNBA teams.

Author: WNBA Analytics Team
Date: 2025-07-12
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Any, Optional
from sklearn.ensemble import RandomForestRegressor
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
import warnings
warnings.filterwarnings('ignore')

# ============================================================================
# 1. MODEL CONFIDENCE CALIBRATION
# ============================================================================

class ConfidenceCalibratedModel(nn.Module):
    """
    Model with built-in confidence calibration using high-variance player analysis

    Predicts both point estimates and uncertainty intervals using:
    - Quantile regression for prediction intervals
    - Bayesian dropout for epistemic uncertainty
    - Volatility-aware confidence scoring
    """

    def __init__(self, input_dim: int, hidden_dims: List[int] = [512, 256, 128, 64]):
        super().__init__()

        self.input_dim = input_dim
        self.hidden_dims = hidden_dims

        # Main prediction network
        layers = []
        prev_dim = input_dim

        for hidden_dim in hidden_dims:
            layers.extend([
                nn.Linear(prev_dim, hidden_dim),
                nn.BatchNorm1d(hidden_dim),
                nn.ReLU(),
                nn.Dropout(0.2)  # Bayesian dropout for uncertainty
            ])
            prev_dim = hidden_dim

        self.backbone = nn.Sequential(*layers)

        # Multiple output heads for quantile regression
        self.point_head = nn.Linear(prev_dim, 1)  # Point estimate
        self.lower_quantile_head = nn.Linear(prev_dim, 1)  # 5th percentile
        self.upper_quantile_head = nn.Linear(prev_dim, 1)  # 95th percentile
        self.confidence_head = nn.Linear(prev_dim, 1)  # Confidence score

        # Volatility embedding for player-specific uncertainty
        self.volatility_embedding = nn.Embedding(3, 32)  # Low, Medium, High volatility
        self.volatility_fusion = nn.Linear(prev_dim + 32, prev_dim)

        print(f"🎯 ConfidenceCalibratedModel initialized with {sum(p.numel() for p in self.parameters()):,} parameters")

    def forward(self, x: torch.Tensor, volatility_tier: torch.Tensor = None,
                training: bool = True) -> Dict[str, torch.Tensor]:
        """
        Forward pass with confidence calibration

        Args:
            x: Input features [batch_size, input_dim]
            volatility_tier: Player volatility tier (0=Low, 1=Medium, 2=High)
            training: Whether in training mode (affects dropout)

        Returns:
            Dict with point prediction, confidence intervals, and uncertainty
        """

        # Enable/disable dropout based on training flag
        if training:
            self.train()
        else:
            self.eval()

        # Backbone feature extraction
        features = self.backbone(x)

        # Incorporate volatility information if available
        if volatility_tier is not None:
            vol_embed = self.volatility_embedding(volatility_tier)
            features_with_vol = torch.cat([features, vol_embed], dim=1)
            features = self.volatility_fusion(features_with_vol)
            features = F.relu(features)

        # Multiple predictions for uncertainty quantification
        point_pred = self.point_head(features)
        lower_quantile = self.lower_quantile_head(features)
        upper_quantile = self.upper_quantile_head(features)
        confidence = torch.sigmoid(self.confidence_head(features))

        # Ensure quantile ordering (lower < point < upper)
        lower_quantile = point_pred - F.softplus(lower_quantile)
        upper_quantile = point_pred + F.softplus(upper_quantile)

        return {
            'point_prediction': point_pred,
            'lower_bound': lower_quantile,
            'upper_bound': upper_quantile,
            'confidence': confidence,
            'prediction_interval': upper_quantile - lower_quantile,
            'features': features  # For ensemble use
        }

    def predict_with_uncertainty(self, x: torch.Tensor, volatility_tier: torch.Tensor = None,
                                n_samples: int = 100) -> Dict[str, torch.Tensor]:
        """
        Predict with Monte Carlo dropout for uncertainty estimation
        """
        # For single samples, use eval mode to avoid batch norm issues
        if x.shape[0] == 1:
            self.eval()
            # Disable batch norm and use dropout only
            for module in self.modules():
                if isinstance(module, nn.BatchNorm1d):
                    module.eval()
                elif isinstance(module, nn.Dropout):
                    module.train()
        else:
            self.train()  # Enable dropout for uncertainty sampling

        predictions = []
        confidences = []

        with torch.no_grad():
            for _ in range(n_samples):
                output = self.forward(x, volatility_tier, training=(x.shape[0] > 1))
                predictions.append(output['point_prediction'])
                confidences.append(output['confidence'])

        predictions = torch.stack(predictions, dim=0)  # [n_samples, batch_size, 1]
        confidences = torch.stack(confidences, dim=0)

        # Calculate uncertainty statistics
        mean_pred = predictions.mean(dim=0)
        std_pred = predictions.std(dim=0)
        mean_confidence = confidences.mean(dim=0)

        # Epistemic uncertainty (model uncertainty)
        epistemic_uncertainty = std_pred

        # Aleatoric uncertainty (data uncertainty) - from confidence head
        aleatoric_uncertainty = 1.0 - mean_confidence

        # Total uncertainty
        total_uncertainty = epistemic_uncertainty + aleatoric_uncertainty

        return {
            'mean_prediction': mean_pred,
            'epistemic_uncertainty': epistemic_uncertainty,
            'aleatoric_uncertainty': aleatoric_uncertainty,
            'total_uncertainty': total_uncertainty,
            'prediction_std': std_pred,
            'confidence': mean_confidence
        }

class QuantileLoss(nn.Module):
    """Quantile loss for training confidence intervals"""

    def __init__(self, quantiles: List[float] = [0.05, 0.5, 0.95]):
        super().__init__()
        self.quantiles = quantiles

    def forward(self, predictions: torch.Tensor, targets: torch.Tensor, quantile: float) -> torch.Tensor:
        """
        Calculate quantile loss

        Args:
            predictions: Predicted quantile values
            targets: True target values
            quantile: Quantile level (e.g., 0.05 for 5th percentile)
        """
        errors = targets - predictions
        loss = torch.max(quantile * errors, (quantile - 1) * errors)
        return loss.mean()

class ConfidenceCalibrationTrainer:
    """Trainer for confidence calibrated models"""

    def __init__(self, model: ConfidenceCalibratedModel, device: str = 'cpu'):
        self.model = model.to(device)
        self.device = device
        self.quantile_loss = QuantileLoss()

    def train_step(self, x: torch.Tensor, y: torch.Tensor,
                   volatility_tier: torch.Tensor = None) -> Dict[str, float]:
        """Single training step with multiple loss components"""

        self.model.train()

        # Forward pass
        outputs = self.model(x, volatility_tier, training=True)

        # Multiple loss components
        point_loss = F.mse_loss(outputs['point_prediction'], y)
        lower_loss = self.quantile_loss(outputs['lower_bound'], y, 0.05)
        upper_loss = self.quantile_loss(outputs['upper_bound'], y, 0.95)

        # Confidence calibration loss
        # High confidence should correlate with low prediction error
        pred_errors = torch.abs(outputs['point_prediction'] - y)
        confidence_loss = F.mse_loss(outputs['confidence'], 1.0 / (1.0 + pred_errors))

        # Interval coverage loss (encourage proper interval width)
        interval_width = outputs['prediction_interval']
        coverage = ((y >= outputs['lower_bound']) & (y <= outputs['upper_bound'])).float()
        coverage_loss = F.mse_loss(coverage, torch.full_like(coverage, 0.9))  # Target 90% coverage

        # Combined loss
        total_loss = (point_loss +
                     0.3 * lower_loss +
                     0.3 * upper_loss +
                     0.2 * confidence_loss +
                     0.1 * coverage_loss)

        return {
            'total_loss': total_loss.item(),
            'point_loss': point_loss.item(),
            'lower_loss': lower_loss.item(),
            'upper_loss': upper_loss.item(),
            'confidence_loss': confidence_loss.item(),
            'coverage_loss': coverage_loss.item()
        }

# ============================================================================
# 2. VOLATILITY-AWARE ENSEMBLE VOTING
# ============================================================================

class VolatilityAwareEnsemble:
    """
    Ensemble that dynamically weights models based on player volatility

    Routes predictions through different models based on volatility segments:
    - Low volatility: Standard models with high weight
    - Medium volatility: Balanced ensemble
    - High volatility: Bayesian/uncertainty models with high weight
    """

    def __init__(self, models: Dict[str, nn.Module], volatility_weights: Dict[str, Dict[str, float]] = None):
        self.models = models

        # Default volatility-based weights
        if volatility_weights is None:
            self.volatility_weights = {
                'low': {
                    'standard': 0.6,
                    'bayesian': 0.2,
                    'temporal': 0.2
                },
                'medium': {
                    'standard': 0.4,
                    'bayesian': 0.4,
                    'temporal': 0.2
                },
                'high': {
                    'standard': 0.2,
                    'bayesian': 0.6,
                    'temporal': 0.2
                }
            }
        else:
            self.volatility_weights = volatility_weights

        print(f"🎯 VolatilityAwareEnsemble initialized with {len(models)} models")

    def predict(self, x: torch.Tensor, volatility_tiers: List[str],
                return_individual: bool = False) -> Dict[str, torch.Tensor]:
        """
        Make predictions with volatility-aware weighting

        Args:
            x: Input features
            volatility_tiers: List of volatility tiers ('low', 'medium', 'high') for each sample
            return_individual: Whether to return individual model predictions
        """

        batch_size = x.shape[0]
        individual_predictions = {}

        # Get predictions from all models
        for model_name, model in self.models.items():
            model.eval()
            with torch.no_grad():
                if hasattr(model, 'predict_with_uncertainty'):
                    pred = model.predict_with_uncertainty(x)
                    individual_predictions[model_name] = pred['mean_prediction']
                else:
                    pred = model(x)
                    if isinstance(pred, dict):
                        individual_predictions[model_name] = pred.get('point_prediction', pred.get('prediction', list(pred.values())[0]))
                    else:
                        individual_predictions[model_name] = pred

        # Volatility-aware weighted ensemble
        ensemble_predictions = torch.zeros(batch_size, 1)

        for i, volatility_tier in enumerate(volatility_tiers):
            weights = self.volatility_weights.get(volatility_tier, self.volatility_weights['medium'])

            weighted_pred = 0.0
            total_weight = 0.0

            for model_name, weight in weights.items():
                if model_name in individual_predictions:
                    weighted_pred += weight * individual_predictions[model_name][i]
                    total_weight += weight

            if total_weight > 0:
                ensemble_predictions[i] = weighted_pred / total_weight
            else:
                # Fallback to simple average
                ensemble_predictions[i] = torch.mean(torch.stack([pred[i] for pred in individual_predictions.values()]))

        result = {
            'ensemble_prediction': ensemble_predictions,
            'volatility_tiers': volatility_tiers
        }

        if return_individual:
            result['individual_predictions'] = individual_predictions

        return result

    def update_weights(self, volatility_tier: str, model_performances: Dict[str, float]):
        """
        Update ensemble weights based on recent model performance

        Args:
            volatility_tier: Volatility tier to update
            model_performances: Dict of model_name -> performance_score (lower is better)
        """

        # Convert performance scores to weights (inverse relationship)
        total_inverse_perf = sum(1.0 / max(perf, 0.1) for perf in model_performances.values())

        new_weights = {}
        for model_name, perf in model_performances.items():
            new_weights[model_name] = (1.0 / max(perf, 0.1)) / total_inverse_perf

        self.volatility_weights[volatility_tier] = new_weights

        print(f"📊 Updated weights for {volatility_tier} volatility:")
        for model, weight in new_weights.items():
            print(f"   {model}: {weight:.3f}")

# ============================================================================
# 3. BACKTEST BIAS CORRECTION
# ============================================================================

class TierWeightedEvaluator:
    """
    Evaluator with tier-weighted scoring that reflects importance, not volume

    Addresses bias toward easy predictions (bench/rotation players)
    by weighting evaluation based on strategic importance
    """

    def __init__(self, tier_weights: Dict[str, float] = None, minute_weighted: bool = True):
        # Default weights emphasize elite players and rotation players
        if tier_weights is None:
            self.tier_weights = {
                'Elite': 0.5,     # 50% weight despite being ~10% of data
                'Rotation': 0.4,  # 40% weight for ~60% of data
                'Bench': 0.1      # 10% weight for ~30% of data
            }
        else:
            self.tier_weights = tier_weights

        self.minute_weighted = minute_weighted

        print(f"🎯 TierWeightedEvaluator initialized:")
        for tier, weight in self.tier_weights.items():
            print(f"   {tier}: {weight:.1%} weight")

    def calculate_weighted_mae(self, predictions: np.ndarray, targets: np.ndarray,
                              tiers: np.ndarray, minutes: np.ndarray = None) -> Dict[str, float]:
        """
        Calculate tier-weighted MAE that reflects strategic importance

        Args:
            predictions: Model predictions
            targets: True targets
            tiers: Player tiers ('Elite', 'Rotation', 'Bench')
            minutes: Minutes played (for minute-weighting)
        """

        results = {}

        # Calculate MAE for each tier
        tier_maes = {}
        tier_counts = {}

        for tier in ['Elite', 'Rotation', 'Bench']:
            tier_mask = tiers == tier
            if tier_mask.sum() > 0:
                tier_preds = predictions[tier_mask]
                tier_targets = targets[tier_mask]

                if self.minute_weighted and minutes is not None:
                    tier_minutes = minutes[tier_mask]
                    # Weight errors by minutes played (more minutes = more important)
                    weights = tier_minutes / tier_minutes.max()
                    weighted_errors = np.abs(tier_preds - tier_targets) * weights
                    tier_mae = weighted_errors.sum() / weights.sum()
                else:
                    tier_mae = np.mean(np.abs(tier_preds - tier_targets))

                tier_maes[tier] = tier_mae
                tier_counts[tier] = tier_mask.sum()
                results[f'{tier.lower()}_mae'] = tier_mae
                results[f'{tier.lower()}_count'] = int(tier_mask.sum())

        # Calculate tier-weighted overall MAE
        weighted_mae = 0.0
        total_weight = 0.0

        for tier, mae in tier_maes.items():
            weight = self.tier_weights.get(tier, 0.0)
            weighted_mae += weight * mae
            total_weight += weight

        if total_weight > 0:
            weighted_mae /= total_weight

        # Standard MAE for comparison
        standard_mae = np.mean(np.abs(predictions - targets))

        results.update({
            'weighted_mae': weighted_mae,
            'standard_mae': standard_mae,
            'bias_correction': weighted_mae - standard_mae,
            'tier_maes': tier_maes,
            'tier_counts': tier_counts
        })

        return results

    def evaluate_model_bias(self, predictions: np.ndarray, targets: np.ndarray,
                           tiers: np.ndarray) -> Dict[str, Any]:
        """
        Evaluate model bias across different player tiers
        """

        bias_analysis = {}

        for tier in ['Elite', 'Rotation', 'Bench']:
            tier_mask = tiers == tier
            if tier_mask.sum() > 0:
                tier_preds = predictions[tier_mask]
                tier_targets = targets[tier_mask]

                # Bias metrics
                mean_error = np.mean(tier_preds - tier_targets)
                mae = np.mean(np.abs(tier_preds - tier_targets))
                rmse = np.sqrt(np.mean((tier_preds - tier_targets) ** 2))

                # Correlation
                correlation = np.corrcoef(tier_preds, tier_targets)[0, 1]

                bias_analysis[tier] = {
                    'mean_error': mean_error,
                    'mae': mae,
                    'rmse': rmse,
                    'correlation': correlation,
                    'sample_size': int(tier_mask.sum()),
                    'bias_direction': 'OVERESTIMATE' if mean_error > 0 else 'UNDERESTIMATE'
                }

        return bias_analysis

# ============================================================================
# 4. OUTLIER HANDLING MODE
# ============================================================================

class OutlierAwarePredictor:
    """
    Prediction system with outlier detection and risk-aware routing

    Features:
    - Real-time outlier detection during inference
    - Conservative model routing for high-risk predictions
    - Uncertainty-based warnings and alerts
    """

    def __init__(self, models: Dict[str, nn.Module], volatility_thresholds: Dict[str, float] = None):
        self.models = models

        # Default volatility thresholds for outlier detection
        if volatility_thresholds is None:
            self.volatility_thresholds = {
                'Elite': 5.0,     # Elite players can have higher variance
                'Rotation': 3.0,  # Rotation players moderate variance
                'Bench': 2.0      # Bench players should be more predictable
            }
        else:
            self.volatility_thresholds = volatility_thresholds

        self.outlier_history = []

        print(f"🚨 OutlierAwarePredictor initialized with risk thresholds:")
        for tier, threshold in self.volatility_thresholds.items():
            print(f"   {tier}: {threshold:.1f} pts")

    def predict_with_outlier_detection(self, x: torch.Tensor, player_tiers: List[str],
                                     player_names: List[str] = None) -> Dict[str, Any]:
        """
        Make predictions with real-time outlier detection and risk assessment
        """

        batch_size = x.shape[0]
        results = {
            'predictions': [],
            'risk_levels': [],
            'outlier_flags': [],
            'confidence_scores': [],
            'warnings': [],
            'model_routes': []
        }

        for i in range(batch_size):
            sample_x = x[i:i+1]
            tier = player_tiers[i]
            player_name = player_names[i] if player_names else f"Player_{i}"

            # Get predictions from multiple models
            model_predictions = {}
            uncertainties = {}

            for model_name, model in self.models.items():
                model.eval()
                with torch.no_grad():
                    if hasattr(model, 'predict_with_uncertainty'):
                        # Use eval mode for single sample to avoid batch norm issues
                        pred_result = model.predict_with_uncertainty(sample_x, n_samples=10)
                        model_predictions[model_name] = pred_result['mean_prediction'].item()
                        uncertainties[model_name] = pred_result['total_uncertainty'].item()
                    else:
                        pred = model(sample_x, training=False)  # Explicitly use eval mode
                        if isinstance(pred, dict):
                            model_predictions[model_name] = pred.get('point_prediction', pred.get('prediction', list(pred.values())[0])).item()
                        else:
                            model_predictions[model_name] = pred.item()
                        uncertainties[model_name] = 0.0  # No uncertainty available

            # Analyze prediction consistency
            pred_values = list(model_predictions.values())
            pred_std = np.std(pred_values)
            pred_mean = np.mean(pred_values)

            # Outlier detection
            threshold = self.volatility_thresholds.get(tier, 3.0)
            is_outlier = pred_std > threshold

            # Risk assessment
            avg_uncertainty = np.mean(list(uncertainties.values()))

            if is_outlier and avg_uncertainty > 2.0:
                risk_level = 'HIGH'
                warning = f"⚠️ HIGH RISK: High prediction variance ({pred_std:.1f}) and uncertainty ({avg_uncertainty:.1f})"
                # Route to conservative model
                model_route = 'conservative'
                final_prediction = np.percentile(pred_values, 25)  # Use 25th percentile for safety

            elif is_outlier:
                risk_level = 'MEDIUM'
                warning = f"⚠️ MEDIUM RISK: High prediction variance ({pred_std:.1f})"
                model_route = 'ensemble'
                final_prediction = pred_mean

            elif avg_uncertainty > 2.0:
                risk_level = 'MEDIUM'
                warning = f"⚠️ MEDIUM RISK: High uncertainty ({avg_uncertainty:.1f})"
                model_route = 'uncertainty_aware'
                final_prediction = pred_mean

            else:
                risk_level = 'LOW'
                warning = None
                model_route = 'standard'
                final_prediction = pred_mean

            # Confidence score (inverse of risk)
            confidence_score = 1.0 / (1.0 + pred_std + avg_uncertainty)

            # Store results
            results['predictions'].append(final_prediction)
            results['risk_levels'].append(risk_level)
            results['outlier_flags'].append(is_outlier)
            results['confidence_scores'].append(confidence_score)
            results['warnings'].append(warning)
            results['model_routes'].append(model_route)

            # Log outlier for history
            if is_outlier:
                self.outlier_history.append({
                    'player': player_name,
                    'tier': tier,
                    'prediction_std': pred_std,
                    'uncertainty': avg_uncertainty,
                    'risk_level': risk_level,
                    'timestamp': pd.Timestamp.now()
                })

        return results

    def get_outlier_summary(self) -> Dict[str, Any]:
        """Get summary of recent outlier detections"""

        if not self.outlier_history:
            return {'total_outliers': 0, 'message': 'No outliers detected'}

        df = pd.DataFrame(self.outlier_history)

        summary = {
            'total_outliers': len(df),
            'outliers_by_tier': df['tier'].value_counts().to_dict(),
            'outliers_by_risk': df['risk_level'].value_counts().to_dict(),
            'avg_prediction_std': df['prediction_std'].mean(),
            'avg_uncertainty': df['uncertainty'].mean(),
            'recent_outliers': df.tail(5)[['player', 'tier', 'risk_level']].to_dict('records')
        }

        return summary

# ============================================================================
# 5. ELITE SCORER PROFILE ENRICHMENT
# ============================================================================

class EliteScorerProfiler:
    """
    Advanced profiling system for elite scorers with behavioral embeddings

    Features:
    - Hand-curated annotations for key events
    - Behavioral embeddings for playstyle analysis
    - Matchup dependency modeling
    - Performance pattern recognition
    """

    def __init__(self):
        # Elite scorer profiles with known characteristics
        self.elite_profiles = {
            "A'ja Wilson": {
                'playstyle': 'dominant_post',
                'volatility_factors': ['matchup_dependent', 'foul_trouble'],
                'peak_performance_triggers': ['home_games', 'national_tv'],
                'injury_history': ['ankle_2023'],
                'coaching_changes': [],
                'behavioral_traits': ['clutch_performer', 'consistent_effort']
            },
            "Breanna Stewart": {
                'playstyle': 'versatile_scorer',
                'volatility_factors': ['three_point_variance', 'pace_dependent'],
                'peak_performance_triggers': ['playoff_atmosphere', 'rivalry_games'],
                'injury_history': ['achilles_2019'],
                'coaching_changes': ['seattle_to_ny_2023'],
                'behavioral_traits': ['momentum_player', 'team_leader']
            },
            "Diana Taurasi": {
                'playstyle': 'veteran_shooter',
                'volatility_factors': ['age_related_rest', 'shot_selection'],
                'peak_performance_triggers': ['big_moments', 'veteran_leadership'],
                'injury_history': ['back_issues_2022'],
                'coaching_changes': [],
                'behavioral_traits': ['clutch_gene', 'experience_factor']
            }
        }

        # Behavioral embedding dimensions
        self.behavioral_embeddings = self._create_behavioral_embeddings()

        print(f"🌟 EliteScorerProfiler initialized with {len(self.elite_profiles)} elite profiles")

    def _create_behavioral_embeddings(self) -> Dict[str, torch.Tensor]:
        """Create behavioral embeddings for different playstyles and traits"""

        embeddings = {}

        # Playstyle embeddings
        playstyle_map = {
            'dominant_post': torch.tensor([1.0, 0.8, 0.3, 0.6, 0.9]),  # [power, consistency, range, clutch, physicality]
            'versatile_scorer': torch.tensor([0.7, 0.6, 0.9, 0.8, 0.5]),
            'veteran_shooter': torch.tensor([0.5, 0.9, 0.8, 1.0, 0.3])
        }

        # Behavioral trait embeddings
        trait_map = {
            'clutch_performer': torch.tensor([0.2, 0.1, 0.9, 0.8]),  # [variance, predictability, pressure, leadership]
            'consistent_effort': torch.tensor([0.1, 0.9, 0.6, 0.7]),
            'momentum_player': torch.tensor([0.8, 0.4, 0.7, 0.8]),
            'team_leader': torch.tensor([0.3, 0.7, 0.8, 0.9])
        }

        embeddings.update(playstyle_map)
        embeddings.update(trait_map)

        return embeddings

    def get_player_profile_features(self, player_name: str, game_context: Dict[str, Any] = None) -> torch.Tensor:
        """
        Get enriched profile features for a player

        Args:
            player_name: Name of the player
            game_context: Context about the current game (opponent, venue, etc.)

        Returns:
            Enriched feature tensor incorporating behavioral embeddings
        """

        if player_name not in self.elite_profiles:
            # Return neutral embedding for non-elite players
            return torch.zeros(20)  # 20-dimensional neutral embedding

        profile = self.elite_profiles[player_name]

        # Base behavioral embedding
        playstyle_embed = self.behavioral_embeddings.get(profile['playstyle'], torch.zeros(5))

        # Aggregate trait embeddings
        trait_embeds = []
        for trait in profile['behavioral_traits']:
            trait_embeds.append(self.behavioral_embeddings.get(trait, torch.zeros(4)))

        if trait_embeds:
            avg_trait_embed = torch.stack(trait_embeds).mean(dim=0)
        else:
            avg_trait_embed = torch.zeros(4)

        # Context-aware adjustments
        context_adjustments = torch.zeros(5)

        if game_context:
            # Home game boost for players who perform better at home
            if game_context.get('is_home') and 'home_games' in profile['peak_performance_triggers']:
                context_adjustments[0] = 0.3  # Performance boost

            # National TV boost
            if game_context.get('national_tv') and 'national_tv' in profile['peak_performance_triggers']:
                context_adjustments[1] = 0.2

            # Rivalry game boost
            if game_context.get('rivalry') and 'rivalry_games' in profile['peak_performance_triggers']:
                context_adjustments[2] = 0.25

            # Rest impact for older players
            if game_context.get('rest_days', 0) < 1 and 'age_related_rest' in profile['volatility_factors']:
                context_adjustments[3] = -0.2  # Fatigue penalty

            # Pace dependency
            if game_context.get('pace_factor', 1.0) > 1.1 and 'pace_dependent' in profile['volatility_factors']:
                context_adjustments[4] = 0.15  # Pace boost

        # Injury/health adjustments
        health_adjustments = torch.zeros(3)
        if game_context and game_context.get('injury_concern'):
            health_adjustments[0] = -0.3  # Performance reduction
            health_adjustments[1] = 0.4   # Increased uncertainty

        # Coaching change adjustments
        coaching_adjustments = torch.zeros(3)
        if profile['coaching_changes']:
            coaching_adjustments[0] = 0.1   # Slight uncertainty increase
            coaching_adjustments[1] = -0.05 # Slight performance reduction initially

        # Combine all embeddings
        combined_embedding = torch.cat([
            playstyle_embed,      # 5 dims
            avg_trait_embed,      # 4 dims
            context_adjustments,  # 5 dims
            health_adjustments,   # 3 dims
            coaching_adjustments  # 3 dims
        ])  # Total: 20 dims

        return combined_embedding

    def analyze_matchup_dependency(self, player_name: str, opponent_stats: Dict[str, float]) -> Dict[str, float]:
        """
        Analyze how a player's performance depends on opponent characteristics
        """

        if player_name not in self.elite_profiles:
            return {'matchup_factor': 1.0, 'confidence': 0.5}

        profile = self.elite_profiles[player_name]
        matchup_analysis = {}

        # Analyze based on playstyle
        if profile['playstyle'] == 'dominant_post':
            # Post players affected by opponent interior defense
            opp_blocks = opponent_stats.get('blocks_per_game', 4.0)
            opp_interior_defense = opponent_stats.get('interior_defense_rating', 100.0)

            # Strong interior defense hurts post players more
            interior_factor = max(0.7, 1.0 - (opp_blocks - 4.0) * 0.05)
            defense_factor = max(0.8, 1.0 - (opp_interior_defense - 100.0) * 0.002)

            matchup_analysis['interior_factor'] = interior_factor
            matchup_analysis['defense_factor'] = defense_factor
            matchup_analysis['matchup_factor'] = (interior_factor + defense_factor) / 2

        elif profile['playstyle'] == 'versatile_scorer':
            # Versatile scorers less affected by specific defensive schemes
            opp_defense_rating = opponent_stats.get('defensive_rating', 100.0)
            matchup_analysis['matchup_factor'] = max(0.85, 1.0 - (opp_defense_rating - 100.0) * 0.001)

        elif profile['playstyle'] == 'veteran_shooter':
            # Veteran shooters affected by perimeter defense
            opp_three_defense = opponent_stats.get('three_point_defense', 35.0)
            perimeter_factor = max(0.8, 1.0 - (35.0 - opp_three_defense) * 0.01)
            matchup_analysis['matchup_factor'] = perimeter_factor

        # Confidence based on historical matchup data availability
        matchup_analysis['confidence'] = 0.8 if len(profile['volatility_factors']) > 1 else 0.6

        return matchup_analysis

# ============================================================================
# 6. PRETRAINING PHASE WITH ROLE TARGETS
# ============================================================================

class RoleAwarePretrainer:
    """
    Pretraining system that learns player roles before point prediction

    Uses role prediction as soft attention guide for point prediction model.
    Embeds game-context and depth chart awareness directly into the model.
    """

    def __init__(self, input_dim: int, role_embedding_dim: int = 64):
        self.input_dim = input_dim
        self.role_embedding_dim = role_embedding_dim

        # Role classification model
        self.role_classifier = nn.Sequential(
            nn.Linear(input_dim, 256),
            nn.BatchNorm1d(256),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(256, 128),
            nn.BatchNorm1d(128),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(128, 3)  # 3 roles: Bench, Rotation, Elite
        )

        # Role embedding layer
        self.role_embeddings = nn.Embedding(3, role_embedding_dim)

        print(f"🎯 RoleAwarePretrainer initialized with {role_embedding_dim}D role embeddings")

    def pretrain_role_classifier(self, X: torch.Tensor, role_labels: torch.Tensor,
                                epochs: int = 50) -> Dict[str, List[float]]:
        """
        Pretrain the role classification model

        Args:
            X: Input features [N, input_dim]
            role_labels: Role labels [N] (0=Bench, 1=Rotation, 2=Elite)
            epochs: Number of training epochs
        """

        optimizer = torch.optim.Adam(self.role_classifier.parameters(), lr=0.001)
        criterion = nn.CrossEntropyLoss()

        history = {'loss': [], 'accuracy': []}

        print(f"🚀 Pretraining role classifier for {epochs} epochs...")

        for epoch in range(epochs):
            self.role_classifier.train()

            # Forward pass
            role_logits = self.role_classifier(X)
            loss = criterion(role_logits, role_labels)

            # Backward pass
            optimizer.zero_grad()
            loss.backward()
            optimizer.step()

            # Calculate accuracy
            with torch.no_grad():
                predictions = torch.argmax(role_logits, dim=1)
                accuracy = (predictions == role_labels).float().mean()

            history['loss'].append(loss.item())
            history['accuracy'].append(accuracy.item())

            if (epoch + 1) % 10 == 0:
                print(f"   Epoch {epoch+1}/{epochs}: Loss={loss.item():.4f}, Acc={accuracy.item():.3f}")

        print(f"✅ Role classifier pretraining complete. Final accuracy: {history['accuracy'][-1]:.3f}")

        return history

    def get_role_guided_features(self, X: torch.Tensor) -> Dict[str, torch.Tensor]:
        """
        Get role-guided features for point prediction

        Returns:
            Dict with original features, role predictions, and role embeddings
        """

        self.role_classifier.eval()

        with torch.no_grad():
            # Get role predictions
            role_logits = self.role_classifier(X)
            role_probs = F.softmax(role_logits, dim=1)
            role_predictions = torch.argmax(role_logits, dim=1)

            # Get role embeddings
            role_embeds = self.role_embeddings(role_predictions)

            # Soft role embeddings (weighted by probabilities)
            soft_role_embeds = torch.zeros_like(role_embeds)
            for i in range(3):  # 3 roles
                role_embed_i = self.role_embeddings(torch.tensor([i]))
                soft_role_embeds += role_probs[:, i:i+1] * role_embed_i

        return {
            'original_features': X,
            'role_logits': role_logits,
            'role_probs': role_probs,
            'role_predictions': role_predictions,
            'hard_role_embeddings': role_embeds,
            'soft_role_embeddings': soft_role_embeds
        }

class RoleGuidedPointPredictor(nn.Module):
    """
    Point prediction model that uses pretrained role information as attention guide
    """

    def __init__(self, input_dim: int, role_embedding_dim: int = 64,
                 hidden_dims: List[int] = [512, 256, 128]):
        super().__init__()

        self.input_dim = input_dim
        self.role_embedding_dim = role_embedding_dim

        # Feature fusion layer (combines original features with role embeddings)
        self.feature_fusion = nn.Linear(input_dim + role_embedding_dim, input_dim)

        # Role attention mechanism (ensure embed_dim is divisible by num_heads)
        attention_dim = input_dim if input_dim % 8 == 0 else ((input_dim // 8) + 1) * 8
        self.attention_projection = nn.Linear(input_dim, attention_dim) if attention_dim != input_dim else nn.Identity()

        self.role_attention = nn.MultiheadAttention(
            embed_dim=attention_dim,
            num_heads=8,
            dropout=0.1,
            batch_first=True
        )

        # Main prediction network
        layers = []
        prev_dim = input_dim

        for hidden_dim in hidden_dims:
            layers.extend([
                nn.Linear(prev_dim, hidden_dim),
                nn.BatchNorm1d(hidden_dim),
                nn.ReLU(),
                nn.Dropout(0.2)
            ])
            prev_dim = hidden_dim

        self.backbone = nn.Sequential(*layers)
        self.output_head = nn.Linear(prev_dim, 1)

        print(f"🎯 RoleGuidedPointPredictor initialized with role-aware attention")

    def forward(self, original_features: torch.Tensor,
                role_embeddings: torch.Tensor) -> Dict[str, torch.Tensor]:
        """
        Forward pass with role-guided attention

        Args:
            original_features: Original input features [batch_size, input_dim]
            role_embeddings: Role embeddings [batch_size, role_embedding_dim]
        """

        batch_size = original_features.shape[0]

        # Fuse original features with role embeddings
        fused_features = torch.cat([original_features, role_embeddings], dim=1)
        fused_features = self.feature_fusion(fused_features)
        fused_features = F.relu(fused_features)

        # Project to attention dimension if needed
        attention_features = self.attention_projection(fused_features)
        attention_orig = self.attention_projection(original_features)

        # Role-guided attention
        # Use role embeddings as queries, original features as keys and values
        query = attention_features.unsqueeze(1)  # [batch_size, 1, attention_dim]
        key = attention_orig.unsqueeze(1)  # [batch_size, 1, attention_dim]
        value = attention_orig.unsqueeze(1)  # [batch_size, 1, attention_dim]

        attended_features, attention_weights = self.role_attention(query, key, value)
        attended_features = attended_features.squeeze(1)  # [batch_size, attention_dim]

        # Project back to input_dim if needed
        if hasattr(self.attention_projection, 'weight'):  # It's a Linear layer
            attended_features = attended_features[:, :self.input_dim]  # Truncate to original dim

        # Main prediction network
        features = self.backbone(attended_features)
        prediction = self.output_head(features)

        return {
            'prediction': prediction,
            'attended_features': attended_features,
            'attention_weights': attention_weights.squeeze(1),  # [batch_size, 1]
            'fused_features': fused_features
        }

# ============================================================================
# 7. DEMONSTRATION AND INTEGRATION
# ============================================================================

def demonstrate_advanced_strategies():
    """Demonstrate all advanced model strategies"""

    print("🎯 ADVANCED MODEL STRATEGIES DEMONSTRATION")
    print("=" * 70)
    print("🚀 Professional-grade enhancements for WNBA prediction system")
    print()

    # Mock data for demonstration
    batch_size = 30  # Use 30 for clean division by 3
    input_dim = 50

    # Generate mock features and targets
    X = torch.randn(batch_size, input_dim)
    y = torch.randn(batch_size, 1) * 5 + 10  # Points around 10 ± 5

    # Mock player information (10 of each tier)
    player_tiers = ['Elite'] * 10 + ['Rotation'] * 10 + ['Bench'] * 10
    volatility_tiers = ['high'] * 10 + ['medium'] * 10 + ['low'] * 10
    player_names = [f"Player_{i}" for i in range(batch_size)]

    # 1. Confidence Calibration Demo
    print("1️⃣ CONFIDENCE CALIBRATION")
    print("-" * 40)

    confidence_model = ConfidenceCalibratedModel(input_dim)
    volatility_tier_tensor = torch.tensor([2] * 10 + [1] * 10 + [0] * 10)  # 2=High, 1=Med, 0=Low

    # Forward pass
    conf_output = confidence_model(X, volatility_tier_tensor)
    print(f"✅ Confidence model output keys: {list(conf_output.keys())}")
    print(f"   Point predictions: {conf_output['point_prediction'][:3].flatten()}")
    print(f"   Confidence scores: {conf_output['confidence'][:3].flatten()}")
    print(f"   Prediction intervals: {conf_output['prediction_interval'][:3].flatten()}")

    # Uncertainty estimation
    uncertainty_output = confidence_model.predict_with_uncertainty(X[:5], volatility_tier_tensor[:5], n_samples=20)
    print(f"   Epistemic uncertainty: {uncertainty_output['epistemic_uncertainty'][:3].flatten()}")
    print()

    # 2. Volatility-Aware Ensemble Demo
    print("2️⃣ VOLATILITY-AWARE ENSEMBLE")
    print("-" * 40)

    # Create mock models
    models = {
        'standard': confidence_model,
        'bayesian': confidence_model,  # In practice, would be different models
        'temporal': confidence_model
    }

    ensemble = VolatilityAwareEnsemble(models)
    ensemble_output = ensemble.predict(X, volatility_tiers, return_individual=True)

    print(f"✅ Ensemble predictions shape: {ensemble_output['ensemble_prediction'].shape}")
    print(f"   Sample predictions: {ensemble_output['ensemble_prediction'][:3].flatten()}")
    print(f"   Individual models: {list(ensemble_output['individual_predictions'].keys())}")
    print()

    # 3. Tier-Weighted Evaluation Demo
    print("3️⃣ TIER-WEIGHTED EVALUATION")
    print("-" * 40)

    evaluator = TierWeightedEvaluator()

    # Mock predictions and evaluation
    mock_predictions = ensemble_output['ensemble_prediction'].numpy().flatten()
    mock_targets = y.numpy().flatten()
    mock_tiers = np.array(player_tiers)
    mock_minutes = np.random.uniform(10, 35, batch_size)

    eval_results = evaluator.calculate_weighted_mae(mock_predictions, mock_targets, mock_tiers, mock_minutes)

    print(f"✅ Weighted MAE: {eval_results['weighted_mae']:.3f}")
    print(f"   Standard MAE: {eval_results['standard_mae']:.3f}")
    print(f"   Bias correction: {eval_results['bias_correction']:.3f}")

    for tier in ['Elite', 'Rotation', 'Bench']:
        if f'{tier.lower()}_mae' in eval_results:
            print(f"   {tier} MAE: {eval_results[f'{tier.lower()}_mae']:.3f} ({eval_results[f'{tier.lower()}_count']} samples)")
    print()

    # 4. Outlier Handling Demo
    print("4️⃣ OUTLIER HANDLING")
    print("-" * 40)

    outlier_predictor = OutlierAwarePredictor(models)
    outlier_results = outlier_predictor.predict_with_outlier_detection(X, player_tiers, player_names)

    print(f"✅ Risk-aware predictions completed")
    print(f"   High risk predictions: {outlier_results['risk_levels'].count('HIGH')}")
    print(f"   Medium risk predictions: {outlier_results['risk_levels'].count('MEDIUM')}")
    print(f"   Low risk predictions: {outlier_results['risk_levels'].count('LOW')}")
    print(f"   Outliers detected: {sum(outlier_results['outlier_flags'])}")

    # Show warnings for high-risk predictions
    for i, warning in enumerate(outlier_results['warnings']):
        if warning and i < 3:  # Show first 3 warnings
            print(f"   {warning}")
    print()

    # 5. Elite Scorer Profiling Demo
    print("5️⃣ ELITE SCORER PROFILING")
    print("-" * 40)

    profiler = EliteScorerProfiler()

    # Demo with A'ja Wilson
    game_context = {
        'is_home': True,
        'national_tv': True,
        'pace_factor': 1.15,
        'rest_days': 2
    }

    profile_features = profiler.get_player_profile_features("A'ja Wilson", game_context)
    print(f"✅ A'ja Wilson profile features: {profile_features.shape}")
    print(f"   Feature sample: {profile_features[:5]}")

    # Matchup analysis
    opponent_stats = {
        'blocks_per_game': 5.2,
        'interior_defense_rating': 105.0,
        'defensive_rating': 102.0
    }

    matchup_analysis = profiler.analyze_matchup_dependency("A'ja Wilson", opponent_stats)
    print(f"   Matchup factor: {matchup_analysis['matchup_factor']:.3f}")
    print(f"   Analysis confidence: {matchup_analysis['confidence']:.3f}")
    print()

    # 6. Role Pretraining Demo
    print("6️⃣ ROLE PRETRAINING")
    print("-" * 40)

    role_pretrainer = RoleAwarePretrainer(input_dim)

    # Create mock role labels
    role_labels = torch.tensor([2] * 10 + [1] * 10 + [0] * 10)  # 2=Elite, 1=Rotation, 0=Bench

    # Quick pretraining demo (5 epochs for speed)
    pretrain_history = role_pretrainer.pretrain_role_classifier(X, role_labels, epochs=5)
    print(f"✅ Role pretraining completed. Final accuracy: {pretrain_history['accuracy'][-1]:.3f}")

    # Get role-guided features
    role_features = role_pretrainer.get_role_guided_features(X)
    print(f"   Role predictions shape: {role_features['role_predictions'].shape}")
    print(f"   Role embeddings shape: {role_features['soft_role_embeddings'].shape}")

    # Role-guided point predictor
    role_guided_model = RoleGuidedPointPredictor(input_dim)
    guided_output = role_guided_model(X, role_features['soft_role_embeddings'])
    print(f"   Role-guided predictions: {guided_output['prediction'][:3].flatten()}")
    print()

    print("🎉 ALL ADVANCED STRATEGIES DEMONSTRATED!")
    print("=" * 70)
    print("✅ Confidence Calibration - Uncertainty quantification ready")
    print("✅ Volatility-Aware Ensemble - Dynamic model weighting ready")
    print("✅ Tier-Weighted Evaluation - Bias correction ready")
    print("✅ Outlier Handling - Risk-aware predictions ready")
    print("✅ Elite Scorer Profiling - Behavioral embeddings ready")
    print("✅ Role Pretraining - Context-aware modeling ready")
    print()
    print("🚀 Your WNBA system now has PROFESSIONAL-GRADE capabilities!")
    print("🏆 These techniques rival NBA/WNBA analytics departments!")


def main():
    """Main execution function"""

    print("🎯 ADVANCED MODEL STRATEGIES FOR WNBA PREDICTION")
    print("=" * 60)
    print("🔬 Implementing professional-grade model enhancements...")
    print()

    try:
        # Demonstrate all strategies
        demonstrate_advanced_strategies()

        print("\n🎉 ADVANCED STRATEGIES IMPLEMENTATION COMPLETE!")
        print("🚀 Your WNBA prediction system is now at professional level!")

        return True

    except Exception as e:
        print(f"❌ Error during implementation: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    if not success:
        exit(1)