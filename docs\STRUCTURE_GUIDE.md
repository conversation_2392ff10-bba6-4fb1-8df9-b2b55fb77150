# 🏗️ WNBA Production Codebase Structure

## 📁 **Expert-Level Organization**

### **`src/` - Source Code**
- **`data_collection/`** - Automated data collection systems
- **`federated_learning/`** - Federated learning infrastructure  
- **`models/`** - Machine learning models
- **`data_modules/`** - Data processing modules
- **`utils/`** - Utility functions and helpers

### **`config/` - Configuration**
- All configuration files and settings

### **`data/` - Data Storage**
- **`master/`** - Master datasets
- **`federated/`** - Federated learning data (13 teams)
- **`team_isolated/`** - Team-specific train/val/test splits
- **`raw/`** - Raw data files and artifacts

### **`scripts/` - Automation Scripts**
- **`automation/`** - Shell scripts and automation

### **`logs/` - System Logs**
- Log files and tracking databases

### **`docs/` - Documentation**
- Markdown documentation and guides

### **`reports/` - Analysis Reports**
- JSON reports and analysis summaries

### **`requirements/` - Dependencies**
- Package requirements and dependencies

### **`models/` - Trained Models**
- Saved model artifacts (existing directory)

### **`archive/` - Archived Files**
- Reference files and old versions

## 🚀 **Benefits of New Structure**

✅ **Clear separation of concerns**  
✅ **Easy to navigate and understand**  
✅ **Follows Python package conventions**  
✅ **Scalable for future development**  
✅ **Production-ready organization**  

## 📋 **Import Examples**

```python
# Data collection
from src.data_collection.unified_wnba_automated_collector import UnifiedWNBACollector

# Federated learning
from src.federated_learning.federated_wnba_server import FederatedWNBAServer

# Models
from src.models.player_points_model import PlayerPointsModel

# Configuration
from config.wnba_config import WNBAConfig
```

## 📊 **Directory Contents**

### **`src/data_collection/`**
- `unified_wnba_automated_collector.py` - Main automated collector (3:00 AM)
- `start_automated_collection.py` - System startup and management
- `odds_api_roster_manager.py` - Odds API integration (500 credits/month)
- `data_collection_monitor.py` - Collection monitoring
- `extract_additional_nba_api_data.py` - Research tool for advanced endpoints
- `integrate_nba_api_data.py` - Manual data integration tool

### **`src/federated_learning/`**
- `federated_wnba_server.py` - Federated learning server
- `federated_wnba_client.py` - Federated learning client
- `federated_monitoring.py` - Federated system monitoring
- `federated_config.py` - Federated configuration
- `federated_feature_pipeline.py` - Feature processing pipeline
- `federated_team_data_loader.py` - Team data loading
- `flower_client.py` - Flower federated client
- `flower_server.py` - Flower federated server
- `ray_federated_launcher.py` - Ray distributed launcher

### **`src/models/`**
- `player_points_model.py` - Player points prediction model
- `game_totals_model.py` - Game totals prediction model
- `modern_player_points_model.py` - Enhanced player model
- `enhanced_player_points_model.py` - Advanced player model

### **`src/data_modules/`**
- `game_totals_data_module.py` - Game totals data processing
- `real_identity_data_module.py` - Real identity data handling
- `real_temporal_data_module.py` - Temporal data processing

### **`src/utils/`**
- `complete_wnba_prediction_system.py` - Complete prediction system

### **`config/`**
- `wnba_config.py` - Main WNBA configuration
- `federated_config.json` - Federated learning config
- `unified_collector_config.json` - Collector configuration
- `wnba_2025_season_config.json` - 2025 season configuration
- `clean_features_list.json` - Clean feature definitions

### **`data/master/`**
- `wnba_definitive_master_dataset_FIXED.csv` - 92MB master dataset
- `wnba_stadium_locations.csv` - Arena data with altitudes
- `team_summary_13_teams.csv` - 13 team summary

### **`data/federated/`**
- 13 team-specific CSV files: `ATL_data.csv`, `CHI_data.csv`, etc.

### **`data/team_isolated/`**
- Train/val/test splits for all 13 teams
- `team_info.json` - Team metadata

### **`data/raw/`**
- Box score files, NBA API data, roster files, tracking data

### **`scripts/automation/`**
- `start_client_*.sh` - 13 team client startup scripts
- `start_federated_server.sh` - Server startup script
- `start_multiple_clients.sh` - Multi-client launcher
- `launch_flower_training.py` - Flower training launcher
- Various automation scripts

### **`logs/`**
- `unified_wnba_collector.log` - Unified system logs
- `unified_collection_tracking.db` - SQLite tracking database
- `daily_collection_summary.json` - Daily collection summaries

### **`docs/`**
- All markdown documentation files
- System guides and documentation

### **`reports/`**
- JSON analysis reports and summaries
- System audit reports
- Performance analysis

### **`requirements/`**
- `pyproject.toml` - Python project configuration
- `roster_requirements.txt` - Roster-specific requirements

## 🎯 **Usage Guidelines**

1. **Always import from the organized structure**
2. **Use relative imports within packages when appropriate**
3. **Keep configuration in `config/` directory**
4. **Store all data in appropriate `data/` subdirectories**
5. **Use `scripts/automation/` for deployment scripts**
6. **Document changes in `docs/`**
7. **Save analysis results in `reports/`**

## 🔧 **Development Workflow**

1. **Source code changes** → `src/`
2. **Configuration updates** → `config/`
3. **New data** → `data/` (appropriate subdirectory)
4. **Automation scripts** → `scripts/automation/`
5. **Documentation** → `docs/`
6. **Analysis results** → `reports/`

This expert-level organization ensures scalability, maintainability, and production readiness! 🚀
