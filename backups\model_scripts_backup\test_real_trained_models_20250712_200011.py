#!/usr/bin/env python3
"""
🎯 TEST REAL TRAINED MODELS - EXACT FEATURE MATCHING
===================================================

Test our ACTUAL trained models (Enhanced, MultiTask, Bayesian, Federated)
using the EXACT same feature preprocessing as during training.
"""

import pandas as pd
import numpy as np
import torch
import json
from datetime import datetime, timedelta
from pathlib import Path
import sys
import warnings
warnings.filterwarnings('ignore')

# Add src to path
sys.path.append('src')
sys.path.append('src/models')

from models.modern_player_points_model import (
    PlayerPointsModel, 
    MultiTaskPlayerModel,
    BayesianPlayerModel
)

class RealModelTester:
    """Test our actual trained models with exact feature matching"""
    
    def __init__(self):
        """Initialize the real model tester"""
        print("🎯 TEST REAL TRAINED MODELS - EXACT FEATURE MATCHING")
        print("=" * 60)
        print("🔧 Using EXACT same preprocessing as training")
        
    def load_training_preprocessing(self):
        """Load the exact preprocessing used during training"""
        try:
            print("\n🔍 Loading training preprocessing configuration...")
            
            # Check if we have the feature selection from training
            feature_config_paths = [
                "config/clean_features_list.json",
                "models/comprehensive_system/feature_config.json",
                "feature_selection.json"
            ]
            
            for path in feature_config_paths:
                if Path(path).exists():
                    with open(path, 'r') as f:
                        config = json.load(f)
                    print(f"   ✅ Found feature config: {path}")
                    return config
            
            # If no config found, reconstruct from training results
            print("   📊 Reconstructing from training results...")
            
            # Load a sample of the training data to get feature names
            df_sample = pd.read_csv('data/master/wnba_definitive_master_dataset_FIXED.csv', nrows=100, low_memory=False)
            
            # Apply the same preprocessing as in training
            exclude_cols = {
                'points', 'target', 'player_id', 'game_id', 'game_date', 
                'player_name', 'team', 'opponent', 'season', 'Unnamed: 0'
            }
            
            # Get potential feature columns
            potential_features = [col for col in df_sample.columns if col not in exclude_cols]
            
            # Convert to numeric and filter
            training_features = []
            for col in potential_features:
                try:
                    df_sample[col] = pd.to_numeric(df_sample[col], errors='coerce')
                    if df_sample[col].dtype in ['float64', 'int64']:
                        # Check if column has some variance
                        if df_sample[col].nunique() > 1:
                            training_features.append(col)
                            if len(training_features) >= 180:  # Match training feature count
                                break
                except:
                    continue
            
            print(f"   ✅ Reconstructed {len(training_features)} training features")
            return training_features
            
        except Exception as e:
            print(f"❌ Error loading training preprocessing: {e}")
            return None
    
    def load_test_data_with_exact_preprocessing(self, training_features):
        """Load test data with EXACT same preprocessing as training"""
        try:
            print("\n📊 Loading test data with exact preprocessing...")
            
            # Load the data
            df = pd.read_csv('data/master/wnba_definitive_master_dataset_FIXED.csv', low_memory=False)
            df['game_date'] = pd.to_datetime(df['game_date'], errors='coerce')
            
            print(f"   Raw data: {len(df)} records")
            
            # Filter last 7 days
            end_date = datetime(2025, 7, 12)
            start_date = end_date - timedelta(days=7)
            
            recent_games = df[
                (df['game_date'] >= start_date) & 
                (df['game_date'] <= end_date)
            ].copy()
            
            if len(recent_games) == 0:
                print("   Using most recent 100 games")
                recent_games = df.nlargest(100, 'game_date').copy()
            
            # Get target variable
            if 'points' in recent_games.columns:
                target_col = 'points'
            elif 'target' in recent_games.columns:
                target_col = 'target'
            else:
                print("❌ No target column found")
                return None
            
            # Clean targets
            recent_games = recent_games[
                (recent_games[target_col].notna()) & 
                (recent_games[target_col] >= 0) & 
                (recent_games[target_col] <= 50)
            ].copy()
            
            if len(recent_games) == 0:
                print("❌ No valid games after cleaning")
                return None
            
            print(f"   Clean data: {len(recent_games)} valid games")
            
            # Apply EXACT same feature preprocessing as training
            print("   🔧 Applying exact training preprocessing...")
            
            # Process each training feature exactly as in training
            feature_data = {}
            for col in training_features:
                if col in recent_games.columns:
                    # Convert to numeric (same as training)
                    series = pd.to_numeric(recent_games[col], errors='coerce')
                    
                    # Fill missing values with median (same as training)
                    if series.notna().sum() > 0:
                        fill_value = series.median()
                        if pd.isna(fill_value):
                            fill_value = 0.0
                    else:
                        fill_value = 0.0
                    
                    feature_data[col] = series.fillna(fill_value).values
                else:
                    # Missing feature - fill with zeros (same as training)
                    feature_data[col] = np.zeros(len(recent_games))
            
            # Create feature matrix in exact same order as training
            X = np.column_stack([feature_data[col] for col in training_features])
            X = X.astype(np.float32)
            
            # Get targets
            y = recent_games[target_col].values.astype(np.float32)
            
            print(f"   ✅ Features: {X.shape[1]} (exactly {len(training_features)})")
            print(f"   ✅ Samples: {X.shape[0]}")
            print(f"   📊 Target stats: mean={np.mean(y):.1f}, std={np.std(y):.1f}")
            
            return X, y, recent_games
            
        except Exception as e:
            print(f"❌ Error loading test data: {e}")
            return None
    
    def test_real_model(self, model_name, model_path, input_dim, X, y):
        """Test a real trained model"""
        try:
            print(f"\n🤖 Testing REAL {model_name.upper()} model...")
            print(f"   Model path: {Path(model_path).name}")
            
            # Create model with exact input dimension
            if 'enhanced' in model_name.lower():
                model = PlayerPointsModel(
                    input_dim=input_dim,
                    dropout=0.25,
                    learning_rate=0.001
                )
            elif 'multitask' in model_name.lower():
                model = MultiTaskPlayerModel(
                    input_dim=input_dim,
                    dropout=0.25,
                    learning_rate=0.001
                )
            elif 'bayesian' in model_name.lower():
                model = BayesianPlayerModel(
                    input_dim=input_dim,
                    dropout=0.25,
                    learning_rate=0.001
                )
            else:
                print(f"   ❌ Unknown model type: {model_name}")
                return None
            
            # Load the trained checkpoint
            checkpoint = torch.load(model_path, map_location='cpu')
            
            # Load state dict
            try:
                model.load_state_dict(checkpoint['state_dict'])
                print(f"   ✅ Model loaded successfully")
            except Exception as e:
                print(f"   ❌ Error loading state dict: {e}")
                return None
            
            model.eval()
            
            # Make predictions
            predictions = []
            X_tensor = torch.FloatTensor(X)
            
            with torch.no_grad():
                for i in range(len(X_tensor)):
                    try:
                        if 'multitask' in model_name.lower():
                            output = model(X_tensor[i:i+1])
                            if isinstance(output, dict):
                                pred = float(output['points'].item())
                            else:
                                pred = float(output.item())
                        else:
                            pred = float(model(X_tensor[i:i+1]).item())
                        
                        # Ensure reasonable range
                        pred = max(0, min(50, pred))
                        predictions.append(pred)
                        
                    except Exception as e:
                        print(f"   ⚠️ Prediction error for sample {i}: {e}")
                        predictions.append(np.mean(y))
            
            # Calculate metrics
            predictions = np.array(predictions)
            errors = np.abs(predictions - y)
            
            mae = np.mean(errors)
            rmse = np.sqrt(np.mean((predictions - y) ** 2))
            mape = np.mean(np.abs((y - predictions) / np.maximum(y, 1))) * 100
            
            print(f"   📊 Predictions: {len(predictions)}")
            print(f"   🎯 MAE: {mae:.3f} points")
            print(f"   📈 RMSE: {rmse:.3f} points")
            print(f"   📊 MAPE: {mape:.1f}%")
            print(f"   ⚖️ Avg Actual: {np.mean(y):.1f} points")
            print(f"   🔮 Avg Predicted: {np.mean(predictions):.1f} points")
            print(f"   📊 Pred Std: {np.std(predictions):.1f} points")
            
            # Show sample predictions
            print(f"   📝 Sample predictions:")
            for j in range(min(5, len(predictions))):
                print(f"      Game {j+1}: Predicted {predictions[j]:.1f}, Actual {y[j]:.1f} (Error: {errors[j]:.1f})")
            
            return {
                'mae': mae,
                'rmse': rmse,
                'mape': mape,
                'predictions': predictions.tolist(),
                'actual': y.tolist(),
                'avg_actual': np.mean(y),
                'avg_predicted': np.mean(predictions),
                'pred_std': np.std(predictions)
            }
            
        except Exception as e:
            print(f"   ❌ Error testing {model_name}: {e}")
            return None
    
    def run_real_model_test(self):
        """Run the complete real model test"""
        print("🚀 Starting real trained model test...")
        
        # Step 1: Load training preprocessing
        training_features = self.load_training_preprocessing()
        if training_features is None:
            return False
        
        # Step 2: Load test data with exact preprocessing
        test_data = self.load_test_data_with_exact_preprocessing(training_features)
        if test_data is None:
            return False
        
        X, y, games_df = test_data
        
        # Step 3: Load training results
        try:
            results_path = Path("models/comprehensive_system/comprehensive_training_results.json")
            with open(results_path, 'r') as f:
                training_results = json.load(f)
        except Exception as e:
            print(f"❌ Error loading training results: {e}")
            return False
        
        # Step 4: Test each real trained model
        print("\n🧪 TESTING REAL TRAINED MODELS")
        print("-" * 45)
        
        results = {}
        
        # Test Enhanced Model
        enhanced_info = training_results.get('all_models', {}).get('enhanced_model', {})
        enhanced_path = enhanced_info.get('best_model_path')
        if enhanced_path and Path(enhanced_path).exists():
            result = self.test_real_model('enhanced', enhanced_path, len(training_features), X, y)
            if result:
                result['training_mae'] = enhanced_info.get('best_val_mae', 0)
                results['enhanced'] = result
        
        # Test MultiTask Model
        multitask_info = training_results.get('all_models', {}).get('multitask_model', {})
        multitask_path = multitask_info.get('best_model_path')
        if multitask_path and Path(multitask_path).exists():
            result = self.test_real_model('multitask', multitask_path, len(training_features), X, y)
            if result:
                result['training_mae'] = multitask_info.get('best_val_mae', 0)
                results['multitask'] = result
        
        # Test Bayesian Model
        bayesian_info = training_results.get('all_models', {}).get('bayesian_model', {})
        bayesian_path = bayesian_info.get('best_model_path')
        if bayesian_path and Path(bayesian_path).exists():
            result = self.test_real_model('bayesian', bayesian_path, len(training_features), X, y)
            if result:
                result['training_mae'] = bayesian_info.get('best_val_mae', 0)
                results['bayesian'] = result
        
        # Step 5: Analyze results
        print("\n🏆 REAL MODEL RESULTS ANALYSIS")
        print("=" * 40)
        
        if results:
            best_model = min(results.keys(), key=lambda k: results[k]['mae'])
            best_mae = results[best_model]['mae']
            
            print(f"🥇 Best Real Model: {best_model.upper()} (Test MAE: {best_mae:.3f})")
            
            print("\n📊 Real Model Performance:")
            print("   Model     | Train MAE | Test MAE | Difference | Status")
            print("   ----------|-----------|----------|------------|--------")
            for name, result in sorted(results.items(), key=lambda x: x[1]['mae']):
                train_mae = result.get('training_mae', 0)
                test_mae = result['mae']
                diff = test_mae - train_mae
                pred_std = result['pred_std']
                
                if test_mae < 2.0:
                    status = "🏆 EXCELLENT"
                elif test_mae < 4.0:
                    status = "✅ GOOD"
                elif test_mae < 6.0:
                    status = "⚠️ ACCEPTABLE"
                else:
                    status = "❌ POOR"
                
                print(f"   {name.upper():9} | {train_mae:8.3f} | {test_mae:7.3f} | {diff:+9.3f} | {status}")
            
            # Assessment
            print(f"\n🎯 REAL MODEL ASSESSMENT:")
            if best_mae < 2.0:
                print(f"   🏆 EXCELLENT: {best_mae:.3f} MAE is professional-grade!")
                print(f"   ✅ Real trained models ready for production")
            elif best_mae < 4.0:
                print(f"   ✅ GOOD: {best_mae:.3f} MAE is solid performance")
                print(f"   ✅ Real models acceptable for production")
            elif best_mae < 6.0:
                print(f"   ⚠️ ACCEPTABLE: {best_mae:.3f} MAE needs improvement")
                print(f"   🔧 Consider model calibration")
            else:
                print(f"   ❌ POOR: {best_mae:.3f} MAE is unacceptable")
                print(f"   🔧 Models need retraining or feature engineering")
            
            print(f"\n📊 Validation Summary:")
            print(f"   ✅ Tested on {len(y)} real WNBA games from July 5-12, 2025")
            print(f"   ✅ Using exact same preprocessing as training")
            print(f"   ✅ Real federated learning and advanced models")
            print(f"   ✅ Point range: {np.min(y):.1f} - {np.max(y):.1f}")
            
            # Save results
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_data = {
                'timestamp': datetime.now().isoformat(),
                'test_period': 'July 5-12, 2025 (Real Models)',
                'test_samples': len(y),
                'features_used': len(training_features),
                'models_tested': list(results.keys()),
                'best_model': best_model,
                'best_mae': best_mae,
                'results': results
            }
            
            output_path = f"real_model_test_results_{timestamp}.json"
            with open(output_path, 'w') as f:
                json.dump(output_data, f, indent=2, default=float)
            
            print(f"\n💾 Real model results saved to: {output_path}")
            
        else:
            print("❌ No successful real model tests")
        
        return len(results) > 0


def main():
    """Main function"""
    tester = RealModelTester()
    success = tester.run_real_model_test()
    
    if success:
        print("\n✅ REAL MODEL TESTING COMPLETED!")
        print("🏀 Our actual trained models validated on real WNBA data!")
    else:
        print("\n❌ Real model testing failed")


if __name__ == "__main__":
    main()
