#!/usr/bin/env python3
"""
🏀 ULTIMATE CLEAN WNBA MODEL TRAINER
===================================

Streamlined version combining all expert improvements:
✅ Noise reduction (remove extreme values, handle missing data)
✅ Feature selection (reduce from 637 to best features)
✅ Basketball domain knowledge (positions, arenas, etc.)
✅ Clean training (optimal hyperparameters)
"""

import pandas as pd
import numpy as np
import json
import logging
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Tuple, Any
import warnings
warnings.filterwarnings('ignore')

# Feature selection
from sklearn.feature_selection import SelectKBest, f_regression
from sklearn.ensemble import RandomForestRegressor
from sklearn.linear_model import LassoCV
from sklearn.preprocessing import StandardScaler
from sklearn.impute import SimpleImputer

# PyTorch and Lightning
import torch
import pytorch_lightning as pl
from pytorch_lightning.callbacks import EarlyStopping, ModelCheckpoint
from pytorch_lightning.loggers import TensorBoardLogger
from pytorch_lightning import seed_everything

# Our model
from src.models.modern_player_points_model import PlayerPointsModel, WNBADataModule

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# ULTIMATE CONFIGURATION
RANDOM_SEED = 42
TARGET_FEATURES = 150
EXPERT_EPOCHS = 50
PATIENCE = 10

class UltimateCleanTrainer:
    """Ultimate clean trainer with all improvements"""
    
    def __init__(self):
        self.random_seed = RANDOM_SEED
        seed_everything(self.random_seed, workers=True)
        
        logger.info("🏀 ULTIMATE CLEAN TRAINER INITIALIZED")
        logger.info("🎯 Combining: Noise reduction + Domain knowledge + Clean training")
    
    def clean_and_enhance_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """Clean data and add basketball domain knowledge"""
        
        logger.info("🧹 CLEANING AND ENHANCING DATA...")
        
        # Step 1: Remove extreme target values
        if 'target' in df.columns:
            initial_count = len(df)
            clean_mask = (df['target'] >= 0) & (df['target'] <= 40)
            df = df[clean_mask].copy()
            removed = initial_count - len(df)
            logger.info(f"   📊 Removed {removed} extreme target values")
        
        # Step 2: Add basketball domain features
        logger.info("   🏀 Adding basketball domain features...")
        
        # Player positions (simplified mapping)
        position_map = {
            'guard': 1, 'forward': 2, 'center': 3,
            'pg': 1, 'sg': 1, 'sf': 2, 'pf': 2, 'c': 3
        }
        
        if 'player_name' in df.columns:
            # Simple position inference (would use real mappings in production)
            df['position_role'] = 1  # Default to guard
            df['is_guard'] = 1
            df['is_forward'] = 0
            df['is_center'] = 0
        
        # Home advantage
        if 'is_home' in df.columns:
            df['home_advantage'] = df['is_home'].astype(float)
        else:
            df['home_advantage'] = 0.5
        
        # Arena altitude effects
        altitude_map = {
            'SEA': 52, 'MIN': 845, 'IND': 707, 'PHO': 1086, 'LAS': 239,
            'LV': 2000, 'WAS': 46, 'CHI': 593, 'CON': 1000, 'DAL': 426,
            'ATL': 1023, 'NYL': 35, 'GSV': 52
        }
        
        if 'team_abbrev' in df.columns:
            df['arena_altitude'] = df['team_abbrev'].map(altitude_map).fillna(500)
            df['altitude_effect'] = 1.0 - (df['arena_altitude'] - 500) / 10000
            df['high_altitude'] = (df['arena_altitude'] > 1000).astype(int)
        
        # Conference effects
        eastern_teams = ['ATL', 'CHI', 'CON', 'IND', 'NYL', 'WAS']
        western_teams = ['DAL', 'GSV', 'LAS', 'LV', 'MIN', 'PHO', 'SEA']
        
        if 'team_abbrev' in df.columns:
            df['is_eastern'] = df['team_abbrev'].isin(eastern_teams).astype(int)
            df['is_western'] = df['team_abbrev'].isin(western_teams).astype(int)
        
        # Season phase
        if 'year' in df.columns and 'game_date' in df.columns:
            try:
                df['game_date'] = pd.to_datetime(df['game_date'])
                df['season_phase'] = df.groupby('year')['game_date'].transform(
                    lambda x: (x - x.min()) / (x.max() - x.min())
                ).fillna(0.5)
            except:
                df['season_phase'] = 0.5
        
        logger.info(f"   ✅ Enhanced data: {df.shape}")
        return df
    
    def select_best_features(self, df: pd.DataFrame) -> List[str]:
        """Select best features using multiple methods"""
        
        logger.info(f"🎯 SELECTING BEST {TARGET_FEATURES} FEATURES...")
        
        # Define features to exclude
        exclude_cols = [
            'target', 'player_name', 'team_abbrev', 'game_id', 'game_date', 
            'year', 'player_id', 'team_id', 'season', 'SEASON_ID', 'GAME_DATE',
            'collection_date'
        ]
        
        feature_cols = [col for col in df.columns if col not in exclude_cols]
        logger.info(f"   🔧 Available features: {len(feature_cols)}")
        
        # Handle missing data
        for col in feature_cols:
            if col in df.columns:
                if df[col].dtype in ['object', 'string']:
                    df[col] = pd.to_numeric(df[col], errors='coerce')
                df[col] = df[col].fillna(df[col].median())
        
        # Remove high correlation features
        logger.info("   🔗 Removing highly correlated features...")
        corr_matrix = df[feature_cols].corr().abs()
        upper_triangle = corr_matrix.where(
            np.triu(np.ones(corr_matrix.shape), k=1).astype(bool)
        )
        
        to_remove = []
        for column in upper_triangle.columns:
            correlated = upper_triangle.index[upper_triangle[column] > 0.95].tolist()
            to_remove.extend(correlated)
        
        to_remove = list(set(to_remove))
        clean_features = [col for col in feature_cols if col not in to_remove]
        logger.info(f"   ✅ After correlation removal: {len(clean_features)} features")
        
        # Feature selection
        X = df[clean_features].fillna(0)
        y = df['target']
        
        # Random Forest importance
        rf = RandomForestRegressor(n_estimators=50, random_state=42, n_jobs=-1)
        rf.fit(X, y)
        rf_importance = pd.Series(rf.feature_importances_, index=clean_features)
        
        # Select top features
        top_features = rf_importance.nlargest(TARGET_FEATURES).index.tolist()
        
        logger.info(f"   ✅ Selected {len(top_features)} best features")
        logger.info(f"   🌟 Top 5: {top_features[:5]}")
        
        return top_features
    
    def prepare_clean_data(self) -> Tuple[pd.DataFrame, pd.DataFrame, pd.DataFrame, List[str]]:
        """Prepare completely clean data"""
        
        logger.info("🚀 PREPARING CLEAN DATA")
        logger.info("=" * 40)
        
        # Load data
        data_path = Path("data/master/wnba_definitive_master_dataset_FIXED.csv")
        df = pd.read_csv(data_path)
        logger.info(f"📊 Initial data: {df.shape}")
        
        # Clean and enhance
        df_clean = self.clean_and_enhance_data(df)
        
        # Select best features
        best_features = self.select_best_features(df_clean)
        
        # Temporal splits
        if 'year' in df_clean.columns:
            train_mask = df_clean['year'] <= 2022
            val_mask = df_clean['year'] == 2023
            test_mask = df_clean['year'] >= 2024
            
            train_df = df_clean[train_mask].copy()
            val_df = df_clean[val_mask].copy()
            test_df = df_clean[test_mask].copy()
        
        logger.info("✅ CLEAN DATA READY!")
        logger.info(f"📊 Train: {len(train_df):,}, Val: {len(val_df):,}, Test: {len(test_df):,}")
        logger.info(f"🔧 Features: {len(best_features)}")
        logger.info(f"📈 Samples per feature: {len(train_df)//len(best_features)}")
        
        return train_df, val_df, test_df, best_features
    
    def train_clean_model(self) -> Dict[str, Any]:
        """Train the ultimate clean model"""
        
        logger.info("🏋️ TRAINING ULTIMATE CLEAN MODEL")
        logger.info("=" * 40)
        
        training_start = datetime.now()
        
        # Prepare data
        train_df, val_df, test_df, features = self.prepare_clean_data()
        
        # Data module
        data_module = WNBADataModule(
            train_df=train_df,
            val_df=val_df,
            test_df=test_df,
            feature_cols=features,
            target_col='target',
            batch_size=256,
            num_workers=4
        )
        
        # Model
        model = PlayerPointsModel(
            input_dim=len(features),
            dropout=0.2,
            learning_rate=0.001,
            use_role_embedding=True
        )
        
        # Callbacks
        callbacks = [
            EarlyStopping(
                monitor='val_loss',
                patience=PATIENCE,
                mode='min',
                verbose=True,
                min_delta=0.001
            ),
            ModelCheckpoint(
                dirpath='models/ultimate_clean',
                filename='clean_model_{epoch:02d}_{val_loss:.4f}',
                monitor='val_loss',
                mode='min',
                save_top_k=3,
                verbose=True
            )
        ]
        
        # Trainer
        trainer = pl.Trainer(
            max_epochs=EXPERT_EPOCHS,
            callbacks=callbacks,
            logger=TensorBoardLogger('models/ultimate_clean/logs', name='clean_training'),
            accelerator='auto',
            devices='auto',
            precision='16-mixed',
            gradient_clip_val=1.0,
            deterministic=True
        )
        
        logger.info("🚀 Starting clean training...")
        trainer.fit(model, data_module)
        
        # Results
        training_end = datetime.now()
        duration = training_end - training_start
        best_path = trainer.checkpoint_callback.best_model_path
        best_score = trainer.checkpoint_callback.best_model_score
        
        logger.info("✅ ULTIMATE CLEAN MODEL COMPLETE!")
        logger.info(f"🏆 Best model: {best_path}")
        logger.info(f"📊 Best val_loss: {best_score:.4f}")
        logger.info(f"⏱️ Duration: {duration}")
        
        # Save metadata
        metadata = {
            'timestamp': training_start.isoformat(),
            'duration_seconds': duration.total_seconds(),
            'features': len(features),
            'improvements': [
                'noise_reduction', 'domain_knowledge', 'feature_selection',
                'clean_training', 'optimal_hyperparameters'
            ],
            'results': {
                'best_model_path': best_path,
                'best_val_loss': float(best_score)
            }
        }
        
        metadata_path = Path('models/ultimate_clean/metadata.json')
        metadata_path.parent.mkdir(parents=True, exist_ok=True)
        with open(metadata_path, 'w') as f:
            json.dump(metadata, f, indent=2)
        
        return metadata

def main():
    """Main function"""
    
    logger.info("🏀 STARTING ULTIMATE CLEAN WNBA MODEL TRAINING")
    
    trainer = UltimateCleanTrainer()
    results = trainer.train_clean_model()
    
    logger.info("🎉 ULTIMATE CLEAN MODEL COMPLETE!")
    
    return results

if __name__ == "__main__":
    main()
