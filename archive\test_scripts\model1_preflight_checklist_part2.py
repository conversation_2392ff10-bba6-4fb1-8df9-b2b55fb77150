"""
Continuation of Model 1 Pre-Flight Checklist - Part 2
"""

def check_output_monitoring(self) -> Dict[str, Dict[str, Any]]:
    """Check output and monitoring setup"""
    results = {}
    
    # Check output directory
    output_dir = Path("models/production")
    output_dir.mkdir(parents=True, exist_ok=True)
    
    if output_dir.exists() and os.access(output_dir, os.W_OK):
        results["output_directory"] = {
            "status": "PASS",
            "message": f"Output directory ready: {output_dir}",
            "details": "Directory is writable"
        }
    else:
        results["output_directory"] = {
            "status": "FAIL",
            "message": f"Output directory not accessible: {output_dir}",
            "details": "Check permissions"
        }
    
    # Check metrics definition
    metrics = ['MAE', 'RMSE', 'R²', 'MAPE', 'Dynamic Difficulty', 'Fairness Score']
    results["metrics_defined"] = {
        "status": "PASS",
        "message": f"Monitoring metrics defined: {len(metrics)} metrics",
        "details": f"Metrics: {metrics}"
    }
    
    # Print results
    for check_name, result in results.items():
        status_icon = "✅" if result["status"] == "PASS" else "⚠️" if result["status"] == "WARN" else "❌"
        print(f"   {status_icon} {check_name}: {result['message']}")
    
    return results

def check_sanity(self) -> Dict[str, Dict[str, Any]]:
    """Perform sanity checks"""
    results = {}
    
    # Load sample data for inspection
    try:
        data_path = "consolidated_wnba/01_player_data/basic_stats/complete_real_wnba_features_with_metadata_processed.csv"
        if Path(data_path).exists():
            df = pd.read_csv(data_path, nrows=1000)  # Sample for speed
            
            # Check data distributions
            if 'target' in df.columns:
                target_stats = df['target'].describe()
                results["target_distribution"] = {
                    "status": "PASS",
                    "message": f"Target distribution: mean={target_stats['mean']:.1f}, std={target_stats['std']:.1f}",
                    "details": f"Range: {target_stats['min']:.1f} to {target_stats['max']:.1f}"
                }
            
            # Check team distribution
            if 'team_abbrev' in df.columns:
                team_counts = df['team_abbrev'].value_counts()
                results["team_distribution"] = {
                    "status": "PASS",
                    "message": f"Team distribution: {len(team_counts)} teams",
                    "details": f"Sample teams: {list(team_counts.head(3).index)}"
                }
        
    except Exception as e:
        results["data_inspection"] = {
            "status": "FAIL",
            "message": f"Data inspection failed: {str(e)}",
            "details": "Check data file accessibility"
        }
    
    # Print results
    for check_name, result in results.items():
        status_icon = "✅" if result["status"] == "PASS" else "⚠️" if result["status"] == "WARN" else "❌"
        print(f"   {status_icon} {check_name}: {result['message']}")
    
    return results

def check_training_protocol(self) -> Dict[str, Dict[str, Any]]:
    """Check training protocol readiness"""
    results = {}
    
    # Check training configuration
    config = self.config
    
    results["expert_epochs"] = {
        "status": "PASS",
        "message": "Expert-level training epochs configured",
        "details": f"LightGBM: 1000 rounds, Neural: {config.models.NEURAL_NETWORK_PARAMS['max_epochs']} epochs"
    }
    
    results["temporal_splits"] = {
        "status": "PASS",
        "message": f"Temporal splits: {config.models.TRAIN_YEARS}-{config.models.VALIDATION_YEAR}-{config.models.TEST_YEARS}",
        "details": "Matches federated learning splits"
    }
    
    # Print results
    for check_name, result in results.items():
        status_icon = "✅" if result["status"] == "PASS" else "⚠️" if result["status"] == "WARN" else "❌"
        print(f"   {status_icon} {check_name}: {result['message']}")
    
    return results

def generate_final_report(self) -> Dict[str, Any]:
    """Generate final pre-flight report"""
    
    print("\n" + "=" * 60)
    print("📊 FINAL PRE-FLIGHT REPORT")
    print("=" * 60)
    
    # Count results
    total_checks = 0
    passed_checks = 0
    warning_checks = 0
    failed_checks = 0
    
    for section_name, section_results in self.checklist_results.items():
        for check_name, result in section_results.items():
            total_checks += 1
            if result['status'] == 'PASS':
                passed_checks += 1
            elif result['status'] == 'WARN':
                warning_checks += 1
            else:
                failed_checks += 1
    
    # Calculate readiness score
    readiness_score = (passed_checks / total_checks * 100) if total_checks > 0 else 0
    
    print(f"📈 READINESS SCORE: {readiness_score:.1f}%")
    print(f"✅ PASSED: {passed_checks}/{total_checks} checks")
    print(f"⚠️ WARNINGS: {warning_checks} checks")
    print(f"❌ FAILED: {failed_checks} checks")
    
    # Determine readiness status
    if failed_checks == 0 and readiness_score >= 90:
        status = "🚀 READY FOR TRAINING"
        recommendation = "All systems go! You can proceed with Model 1 training."
    elif failed_checks == 0 and readiness_score >= 80:
        status = "⚠️ READY WITH WARNINGS"
        recommendation = "Training can proceed, but address warnings for optimal performance."
    else:
        status = "❌ NOT READY"
        recommendation = "Address critical failures before training."
    
    print(f"\n🎯 STATUS: {status}")
    print(f"💡 RECOMMENDATION: {recommendation}")
    
    # Training command
    if readiness_score >= 80:
        print(f"\n🚀 READY TO LAUNCH:")
        print(f"   python train_model_1_real_data.py --production")
        print(f"   python train_model_1_real_data.py --nas")
        print(f"   python train_model_1_real_data.py --bayesian")
    
    return {
        'readiness_score': readiness_score,
        'status': status,
        'total_checks': total_checks,
        'passed_checks': passed_checks,
        'warning_checks': warning_checks,
        'failed_checks': failed_checks,
        'recommendation': recommendation,
        'checklist_results': self.checklist_results
    }
