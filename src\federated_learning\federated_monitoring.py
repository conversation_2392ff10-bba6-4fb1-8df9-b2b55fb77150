#!/usr/bin/env python3
"""
📊 FEDERATED LEARNING MONITORING SYSTEM
=======================================

Production monitoring for WNBA federated learning including:
- Data drift detection across teams
- Fairness monitoring between teams
- Performance tracking and alerts
- Automated schema validation
- Configuration compliance checks

MONITORING PRINCIPLES:
- Real-time drift detection
- Cross-team fairness analysis
- Automated validation pipelines
- Production alerting system
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Any, Optional
from pathlib import Path
import logging
import json
from datetime import datetime, timedelta
from scipy import stats
from sklearn.metrics import mean_absolute_error, mean_squared_error
import warnings
warnings.filterwarnings('ignore')

logger = logging.getLogger(__name__)

class FederatedMonitor:
    """
    Comprehensive monitoring system for federated WNBA learning.
    Tracks drift, fairness, and system health across all teams.
    """
    
    def __init__(self, monitoring_dir: str = "federated_monitoring"):
        self.monitoring_dir = Path(monitoring_dir)
        self.monitoring_dir.mkdir(exist_ok=True)
        
        # Monitoring thresholds
        self.DRIFT_THRESHOLD = 0.1  # KL divergence threshold
        self.FAIRNESS_THRESHOLD = 0.15  # Performance difference threshold
        self.SCHEMA_CHECK_INTERVAL = 24  # Hours
        self.CONFIG_CHECK_INTERVAL = 12  # Hours
        
        # Team performance baselines
        self.team_baselines = {}
        self.drift_history = {}
        self.fairness_history = {}
        
        # WNBA teams
        self.WNBA_TEAMS = ['ATL', 'CHI', 'CON', 'DAL', 'GSV', 'IND', 'LAS', 'LV', 'MIN', 'NYL', 'PHO', 'SEA', 'WAS']
        
        logger.info(f"📊 Federated monitoring initialized")
        logger.info(f"   📁 Monitoring directory: {self.monitoring_dir}")
        logger.info(f"   🚨 Drift threshold: {self.DRIFT_THRESHOLD}")
        logger.info(f"   ⚖️ Fairness threshold: {self.FAIRNESS_THRESHOLD}")
    
    def detect_data_drift(self, team_id: str, current_data: pd.DataFrame, 
                         reference_data: pd.DataFrame, feature_cols: List[str]) -> Dict[str, Any]:
        """
        Detect data drift for a specific team using statistical tests.
        
        Args:
            team_id: Team identifier
            current_data: Current batch of data
            reference_data: Reference/baseline data
            feature_cols: List of feature columns to monitor
            
        Returns:
            Drift detection results
        """
        logger.info(f"🔍 Detecting data drift for {team_id}...")
        
        drift_results = {
            'team_id': team_id,
            'timestamp': datetime.now().isoformat(),
            'drift_detected': False,
            'feature_drifts': {},
            'overall_drift_score': 0.0,
            'alerts': []
        }
        
        drift_scores = []
        
        for feature in feature_cols:
            if feature in current_data.columns and feature in reference_data.columns:
                try:
                    # Get feature distributions
                    current_values = current_data[feature].dropna()
                    reference_values = reference_data[feature].dropna()
                    
                    if len(current_values) > 10 and len(reference_values) > 10:
                        # Kolmogorov-Smirnov test for distribution drift
                        ks_stat, ks_pvalue = stats.ks_2samp(reference_values, current_values)
                        
                        # Jensen-Shannon divergence for drift magnitude
                        try:
                            # Create histograms for JS divergence
                            min_val = min(current_values.min(), reference_values.min())
                            max_val = max(current_values.max(), reference_values.max())
                            bins = np.linspace(min_val, max_val, 20)
                            
                            hist_current, _ = np.histogram(current_values, bins=bins, density=True)
                            hist_reference, _ = np.histogram(reference_values, bins=bins, density=True)
                            
                            # Add small epsilon to avoid log(0)
                            hist_current = hist_current + 1e-10
                            hist_reference = hist_reference + 1e-10
                            
                            # Normalize
                            hist_current = hist_current / hist_current.sum()
                            hist_reference = hist_reference / hist_reference.sum()
                            
                            # Jensen-Shannon divergence
                            m = 0.5 * (hist_current + hist_reference)
                            js_div = 0.5 * stats.entropy(hist_current, m) + 0.5 * stats.entropy(hist_reference, m)
                            
                        except Exception as e:
                            logger.warning(f"   ⚠️ JS divergence calculation failed for {feature}: {e}")
                            js_div = 0.0
                        
                        # Statistical summary drift
                        current_mean = current_values.mean()
                        reference_mean = reference_values.mean()
                        mean_shift = abs(current_mean - reference_mean) / (reference_values.std() + 1e-10)
                        
                        drift_score = min(js_div, 1.0)  # Cap at 1.0
                        drift_scores.append(drift_score)
                        
                        drift_results['feature_drifts'][feature] = {
                            'ks_statistic': float(ks_stat),
                            'ks_pvalue': float(ks_pvalue),
                            'js_divergence': float(js_div),
                            'mean_shift': float(mean_shift),
                            'drift_score': float(drift_score),
                            'drift_detected': drift_score > self.DRIFT_THRESHOLD
                        }
                        
                        # Alert for significant drift
                        if drift_score > self.DRIFT_THRESHOLD:
                            alert = f"DRIFT ALERT: {feature} drift score {drift_score:.3f} > {self.DRIFT_THRESHOLD}"
                            drift_results['alerts'].append(alert)
                            logger.warning(f"   🚨 {alert}")
                
                except Exception as e:
                    logger.warning(f"   ⚠️ Drift detection failed for {feature}: {e}")
        
        # Overall drift assessment
        if drift_scores:
            drift_results['overall_drift_score'] = float(np.mean(drift_scores))
            drift_results['drift_detected'] = drift_results['overall_drift_score'] > self.DRIFT_THRESHOLD
        
        # Store drift history
        if team_id not in self.drift_history:
            self.drift_history[team_id] = []
        self.drift_history[team_id].append(drift_results)
        
        logger.info(f"   📊 {team_id} drift score: {drift_results['overall_drift_score']:.3f}")
        if drift_results['drift_detected']:
            logger.warning(f"   🚨 DRIFT DETECTED for {team_id}!")
        
        return drift_results
    
    def monitor_team_fairness(self, team_performances: Dict[str, Dict[str, float]]) -> Dict[str, Any]:
        """
        Monitor fairness across teams by analyzing performance disparities.
        
        Args:
            team_performances: Dict of {team_id: {metric: value}}
            
        Returns:
            Fairness analysis results
        """
        logger.info(f"⚖️ Monitoring fairness across {len(team_performances)} teams...")
        
        fairness_results = {
            'timestamp': datetime.now().isoformat(),
            'fairness_violations': [],
            'performance_gaps': {},
            'team_rankings': {},
            'alerts': []
        }
        
        # Analyze each performance metric
        metrics = set()
        for team_perf in team_performances.values():
            metrics.update(team_perf.keys())
        
        for metric in metrics:
            metric_values = {}
            for team_id, perf in team_performances.items():
                if metric in perf:
                    metric_values[team_id] = perf[metric]
            
            if len(metric_values) > 1:
                values = list(metric_values.values())
                min_val, max_val = min(values), max(values)
                
                # Calculate performance gap
                if min_val > 0:
                    gap = (max_val - min_val) / min_val
                else:
                    gap = max_val - min_val
                
                fairness_results['performance_gaps'][metric] = {
                    'gap': float(gap),
                    'min_value': float(min_val),
                    'max_value': float(max_val),
                    'min_team': min(metric_values, key=metric_values.get),
                    'max_team': max(metric_values, key=metric_values.get)
                }
                
                # Check for fairness violations
                if gap > self.FAIRNESS_THRESHOLD:
                    violation = {
                        'metric': metric,
                        'gap': gap,
                        'threshold': self.FAIRNESS_THRESHOLD,
                        'disadvantaged_team': min(metric_values, key=metric_values.get),
                        'advantaged_team': max(metric_values, key=metric_values.get)
                    }
                    fairness_results['fairness_violations'].append(violation)
                    
                    alert = f"FAIRNESS ALERT: {metric} gap {gap:.3f} > {self.FAIRNESS_THRESHOLD} ({violation['disadvantaged_team']} vs {violation['advantaged_team']})"
                    fairness_results['alerts'].append(alert)
                    logger.warning(f"   🚨 {alert}")
                
                # Team rankings for this metric
                sorted_teams = sorted(metric_values.items(), key=lambda x: x[1], reverse=True)
                fairness_results['team_rankings'][metric] = [team for team, _ in sorted_teams]
        
        # Special handling for GSV (newest team)
        if 'GSV' in team_performances:
            gsv_note = "GSV performance may be lower due to limited historical data (2025+ team)"
            fairness_results['gsv_context'] = gsv_note
            logger.info(f"   🆕 {gsv_note}")
        
        # Store fairness history
        self.fairness_history[datetime.now().isoformat()] = fairness_results
        
        logger.info(f"   ⚖️ Fairness analysis complete: {len(fairness_results['fairness_violations'])} violations")
        
        return fairness_results
    
    def validate_schema_consistency(self, team_data_paths: Dict[str, str]) -> Dict[str, Any]:
        """
        Validate schema consistency across all teams.
        
        Args:
            team_data_paths: Dict of {team_id: data_file_path}
            
        Returns:
            Schema validation results
        """
        logger.info(f"🔍 Validating schema consistency across {len(team_data_paths)} teams...")
        
        validation_results = {
            'timestamp': datetime.now().isoformat(),
            'schema_consistent': True,
            'reference_schema': None,
            'team_schemas': {},
            'inconsistencies': [],
            'alerts': []
        }
        
        reference_columns = None
        reference_dtypes = None
        reference_team = None
        
        for team_id, data_path in team_data_paths.items():
            try:
                if Path(data_path).exists():
                    # Load sample to check schema
                    df_sample = pd.read_csv(data_path, nrows=100)
                    
                    team_schema = {
                        'columns': list(df_sample.columns),
                        'dtypes': {col: str(dtype) for col, dtype in df_sample.dtypes.items()},
                        'shape': df_sample.shape,
                        'column_count': len(df_sample.columns)
                    }
                    
                    validation_results['team_schemas'][team_id] = team_schema
                    
                    # Set reference schema from first team
                    if reference_columns is None:
                        reference_columns = set(df_sample.columns)
                        reference_dtypes = df_sample.dtypes.to_dict()
                        reference_team = team_id
                        validation_results['reference_schema'] = team_schema
                    else:
                        # Compare with reference
                        current_columns = set(df_sample.columns)
                        
                        # Check column differences
                        missing_cols = reference_columns - current_columns
                        extra_cols = current_columns - reference_columns
                        
                        if missing_cols or extra_cols:
                            inconsistency = {
                                'team_id': team_id,
                                'type': 'column_mismatch',
                                'missing_columns': list(missing_cols),
                                'extra_columns': list(extra_cols)
                            }
                            validation_results['inconsistencies'].append(inconsistency)
                            validation_results['schema_consistent'] = False
                            
                            alert = f"SCHEMA ALERT: {team_id} has column mismatch with {reference_team}"
                            validation_results['alerts'].append(alert)
                            logger.warning(f"   🚨 {alert}")
                
                else:
                    logger.warning(f"   ⚠️ Data file not found for {team_id}: {data_path}")
            
            except Exception as e:
                logger.error(f"   ❌ Schema validation failed for {team_id}: {e}")
        
        if validation_results['schema_consistent']:
            logger.info(f"   ✅ Schema consistency validated across all teams")
        else:
            logger.warning(f"   ⚠️ Schema inconsistencies detected: {len(validation_results['inconsistencies'])}")
        
        return validation_results
    
    def check_config_compliance(self) -> Dict[str, Any]:
        """
        Check federated configuration compliance and consistency.
        
        Returns:
            Configuration compliance results
        """
        logger.info(f"⚙️ Checking federated configuration compliance...")
        
        compliance_results = {
            'timestamp': datetime.now().isoformat(),
            'config_valid': True,
            'compliance_issues': [],
            'alerts': []
        }
        
        try:
            # Load and validate federated config
            from federated_config import get_federated_config
            config = get_federated_config()
            
            # Validate configuration
            if not config.validate_config():
                compliance_results['config_valid'] = False
                issue = "Configuration validation failed"
                compliance_results['compliance_issues'].append(issue)
                compliance_results['alerts'].append(f"CONFIG ALERT: {issue}")
            
            # Check team weights
            team_weights = config.get_all_team_weights()
            if len(team_weights) != 13:
                issue = f"Expected 13 team weights, got {len(team_weights)}"
                compliance_results['compliance_issues'].append(issue)
                compliance_results['alerts'].append(f"CONFIG ALERT: {issue}")
            
            # Check for missing teams
            missing_teams = set(self.WNBA_TEAMS) - set(team_weights.keys())
            if missing_teams:
                issue = f"Missing team weights: {missing_teams}"
                compliance_results['compliance_issues'].append(issue)
                compliance_results['alerts'].append(f"CONFIG ALERT: {issue}")
            
            # Validate feature pipeline
            from federated_feature_pipeline import federated_pipeline
            schema_info = federated_pipeline.get_schema_info()
            
            if schema_info['total_features'] != config.MODEL_CONFIG['input_dim']:
                issue = f"Feature count mismatch: pipeline={schema_info['total_features']}, model={config.MODEL_CONFIG['input_dim']}"
                compliance_results['compliance_issues'].append(issue)
                compliance_results['alerts'].append(f"CONFIG ALERT: {issue}")
            
            logger.info(f"   ✅ Configuration compliance check complete")
            
        except Exception as e:
            logger.error(f"   ❌ Configuration compliance check failed: {e}")
            compliance_results['config_valid'] = False
            compliance_results['compliance_issues'].append(str(e))
        
        return compliance_results
    
    def generate_monitoring_report(self) -> Dict[str, Any]:
        """
        Generate comprehensive monitoring report.
        
        Returns:
            Complete monitoring report
        """
        logger.info(f"📋 Generating comprehensive monitoring report...")
        
        report = {
            'timestamp': datetime.now().isoformat(),
            'system_health': 'healthy',
            'drift_summary': {},
            'fairness_summary': {},
            'schema_status': 'valid',
            'config_status': 'compliant',
            'alerts': [],
            'recommendations': []
        }
        
        # Summarize drift detection
        total_teams_with_drift = 0
        for team_id, drift_history in self.drift_history.items():
            if drift_history and drift_history[-1]['drift_detected']:
                total_teams_with_drift += 1
        
        report['drift_summary'] = {
            'teams_monitored': len(self.drift_history),
            'teams_with_drift': total_teams_with_drift,
            'drift_rate': total_teams_with_drift / max(len(self.drift_history), 1)
        }
        
        # Summarize fairness
        if self.fairness_history:
            latest_fairness = list(self.fairness_history.values())[-1]
            report['fairness_summary'] = {
                'fairness_violations': len(latest_fairness['fairness_violations']),
                'performance_gaps': len(latest_fairness['performance_gaps'])
            }
        
        # System health assessment
        if total_teams_with_drift > len(self.WNBA_TEAMS) * 0.3:  # >30% teams with drift
            report['system_health'] = 'degraded'
            report['recommendations'].append("High drift rate detected - consider model retraining")
        
        if report['fairness_summary'].get('fairness_violations', 0) > 0:
            report['system_health'] = 'attention_needed'
            report['recommendations'].append("Fairness violations detected - review team-specific performance")
        
        logger.info(f"   📊 Monitoring report generated: {report['system_health']} status")
        
        # Save report
        report_file = self.monitoring_dir / f"monitoring_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w') as f:
            json.dump(report, f, indent=2)
        
        logger.info(f"   💾 Report saved: {report_file}")
        
        return report
    
    def save_monitoring_state(self):
        """Save monitoring state for persistence."""
        state = {
            'drift_history': self.drift_history,
            'fairness_history': self.fairness_history,
            'team_baselines': self.team_baselines
        }
        
        state_file = self.monitoring_dir / "monitoring_state.json"
        with open(state_file, 'w') as f:
            json.dump(state, f, indent=2, default=str)
        
        logger.info(f"💾 Monitoring state saved: {state_file}")

# Global monitoring instance
federated_monitor = FederatedMonitor()

class AutomatedValidator:
    """
    Automated validation scheduler for periodic checks.
    """

    def __init__(self, monitor: FederatedMonitor):
        self.monitor = monitor
        self.last_schema_check = None
        self.last_config_check = None

    def should_run_schema_check(self) -> bool:
        """Check if schema validation should run."""
        if self.last_schema_check is None:
            return True

        time_since_last = datetime.now() - self.last_schema_check
        return time_since_last.total_seconds() > (self.monitor.SCHEMA_CHECK_INTERVAL * 3600)

    def should_run_config_check(self) -> bool:
        """Check if config validation should run."""
        if self.last_config_check is None:
            return True

        time_since_last = datetime.now() - self.last_config_check
        return time_since_last.total_seconds() > (self.monitor.CONFIG_CHECK_INTERVAL * 3600)

    def run_automated_checks(self) -> Dict[str, Any]:
        """Run all automated validation checks."""
        logger.info("🤖 Running automated validation checks...")

        results = {
            'timestamp': datetime.now().isoformat(),
            'checks_run': [],
            'alerts': []
        }

        # Schema validation
        if self.should_run_schema_check():
            logger.info("   🔍 Running automated schema validation...")

            # Get team data paths
            team_data_paths = {}
            isolated_dir = Path("team_isolated_data")
            for team in self.monitor.WNBA_TEAMS:
                train_file = isolated_dir / f"{team}_train.csv"
                if train_file.exists():
                    team_data_paths[team] = str(train_file)

            schema_results = self.monitor.validate_schema_consistency(team_data_paths)
            results['schema_validation'] = schema_results
            results['checks_run'].append('schema_validation')
            results['alerts'].extend(schema_results.get('alerts', []))

            self.last_schema_check = datetime.now()

        # Config compliance
        if self.should_run_config_check():
            logger.info("   ⚙️ Running automated config compliance check...")

            config_results = self.monitor.check_config_compliance()
            results['config_compliance'] = config_results
            results['checks_run'].append('config_compliance')
            results['alerts'].extend(config_results.get('alerts', []))

            self.last_config_check = datetime.now()

        # Generate monitoring report
        if results['checks_run']:
            report = self.monitor.generate_monitoring_report()
            results['monitoring_report'] = report
            results['alerts'].extend(report.get('alerts', []))

        logger.info(f"   ✅ Automated checks complete: {len(results['checks_run'])} checks run")

        return results

# Global instances
federated_monitor = FederatedMonitor()
automated_validator = AutomatedValidator(federated_monitor)

if __name__ == "__main__":
    # Test federated monitoring
    print("📊 TESTING FEDERATED MONITORING SYSTEM")
    print("=" * 50)

    monitor = FederatedMonitor()
    validator = AutomatedValidator(monitor)

    # Test configuration compliance
    config_results = monitor.check_config_compliance()
    print(f"⚙️ Config compliance: {'✅' if config_results['config_valid'] else '❌'}")

    # Test automated validation
    auto_results = validator.run_automated_checks()
    print(f"🤖 Automated checks: {len(auto_results['checks_run'])} checks run")

    # Generate test report
    report = monitor.generate_monitoring_report()
    print(f"📋 Monitoring report: {report['system_health']} status")

    print(f"\n🏆 FEDERATED MONITORING READY!")
    print(f"   ✅ Drift detection system")
    print(f"   ✅ Fairness monitoring")
    print(f"   ✅ Schema validation")
    print(f"   ✅ Config compliance checks")
    print(f"   ✅ Automated validation scheduler")
    print(f"   ✅ Production monitoring pipeline")
