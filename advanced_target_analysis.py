#!/usr/bin/env python3
"""
🎯 ADVANCED WNBA TARGET ANALYSIS
===============================

Professional-grade target analysis with real WNBA data:
1. Per-Game Variance Mapping (rolling volatility per player)
2. Context-Aware Outlier Detection (high score + low minutes, etc.)
3. Target Explainability via SHAP/LIME (feature importance for extreme targets)
4. Contextual Target Clustering (unsupervised pattern discovery)
5. Historical Consistency Heatmap (per-player stability over time)
6. Psychological/External Event Analysis (back-to-back, injury returns)
7. Tier-Specific Outlier Detection (Bench/Rotation/Elite noise patterns)

Author: WNBA Analytics Team
Date: 2025-07-12
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.cluster import KMeans, DBSCAN
from sklearn.ensemble import RandomForestRegressor
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# Set style for professional plots
plt.style.use('default')
sns.set_palette("husl")

class AdvancedWNBATargetAnalysis:
    """Advanced target analysis for WNBA player performance data"""

    def __init__(self, dataset_path="data/master/wnba_expert_dataset.csv"):
        self.dataset_path = dataset_path
        self.df = None
        self.analysis_results = {}

    def load_and_prepare_data(self):
        """Load and prepare the dataset for advanced analysis"""
        print("📊 Loading WNBA Expert Dataset for Advanced Analysis...")
        self.df = pd.read_csv(self.dataset_path, low_memory=False)
        print(f"   ✅ Loaded {len(self.df):,} records with {len(self.df.columns)} features")

        # Ensure required columns exist
        required_cols = ['player_name', 'target', 'game_date']
        missing_cols = [col for col in required_cols if col not in self.df.columns]

        if missing_cols:
            raise ValueError(f"Missing required columns: {missing_cols}")

        # Convert game_date to datetime
        self.df['game_date'] = pd.to_datetime(self.df['game_date'], errors='coerce')

        # Sort by player and date for time series analysis
        self.df = self.df.sort_values(['player_name', 'game_date']).reset_index(drop=True)

        # Create player tiers based on average points
        player_avg_points = self.df.groupby('player_name')['target'].mean()

        def assign_tier(avg_points):
            if avg_points >= 15:
                return 'Elite'
            elif avg_points >= 5:
                return 'Rotation'
            else:
                return 'Bench'

        player_tiers = player_avg_points.apply(assign_tier)
        self.df['player_tier'] = self.df['player_name'].map(player_tiers)

        print(f"   🏀 Player Tier Distribution:")
        tier_counts = self.df['player_tier'].value_counts()
        for tier, count in tier_counts.items():
            print(f"      {tier}: {count:,} records ({count/len(self.df)*100:.1f}%)")

        return self.df

    def per_game_variance_mapping(self, window_size=10):
        """1. Analyze per-player variance across games"""
        print(f"\n📈 PER-GAME VARIANCE MAPPING (Rolling {window_size} games)")
        print("=" * 60)

        variance_results = []

        # Calculate rolling variance for each player
        for player in self.df['player_name'].unique():
            player_data = self.df[self.df['player_name'] == player].copy()

            if len(player_data) >= window_size:
                # Calculate rolling standard deviation
                player_data['rolling_std'] = player_data['target'].rolling(window=window_size).std()
                player_data['rolling_mean'] = player_data['target'].rolling(window=window_size).mean()
                player_data['coefficient_of_variation'] = player_data['rolling_std'] / player_data['rolling_mean']

                # Store results
                avg_volatility = player_data['rolling_std'].mean()
                max_volatility = player_data['rolling_std'].max()
                avg_cv = player_data['coefficient_of_variation'].mean()

                variance_results.append({
                    'player_name': player,
                    'games_played': len(player_data),
                    'avg_points': player_data['target'].mean(),
                    'avg_volatility': avg_volatility,
                    'max_volatility': max_volatility,
                    'coefficient_of_variation': avg_cv,
                    'tier': player_data['player_tier'].iloc[0]
                })

        variance_df = pd.DataFrame(variance_results)

        # Identify high-volatility players
        high_volatility_threshold = variance_df['avg_volatility'].quantile(0.9)
        high_volatility_players = variance_df[variance_df['avg_volatility'] > high_volatility_threshold]

        print(f"📊 Variance Analysis Results:")
        print(f"   Players analyzed: {len(variance_df)}")
        print(f"   Average volatility: {variance_df['avg_volatility'].mean():.2f}")
        print(f"   High volatility threshold (90th percentile): {high_volatility_threshold:.2f}")
        print(f"   High volatility players: {len(high_volatility_players)}")

        print(f"\n🔥 Most Volatile Players:")
        top_volatile = variance_df.nlargest(10, 'avg_volatility')[['player_name', 'tier', 'avg_points', 'avg_volatility']]
        print(top_volatile.to_string(index=False))

        # Tier-specific volatility analysis
        print(f"\n📊 Volatility by Player Tier:")
        tier_volatility = variance_df.groupby('tier')['avg_volatility'].agg(['mean', 'std', 'count']).round(3)
        print(tier_volatility)

        # Store results
        self.analysis_results['variance_mapping'] = {
            'total_players': len(variance_df),
            'avg_volatility': variance_df['avg_volatility'].mean(),
            'high_volatility_threshold': high_volatility_threshold,
            'high_volatility_players': len(high_volatility_players),
            'tier_volatility': tier_volatility.to_dict()
        }

        return variance_df, high_volatility_players

    def context_aware_outlier_detection(self):
        """2. Detect context-aware outliers (high score + low minutes, etc.)"""
        print(f"\n🔍 CONTEXT-AWARE OUTLIER DETECTION")
        print("=" * 60)

        outliers = []

        # Check for required columns
        context_cols = ['minutes', 'usage_rate', 'efficiency']
        available_cols = [col for col in context_cols if col in self.df.columns]

        if not available_cols:
            print("❌ No context columns available for outlier detection")
            return pd.DataFrame()

        print(f"📊 Using context columns: {available_cols}")

        # 1. High score + Low minutes outliers
        if 'minutes' in self.df.columns:
            high_score_low_minutes = self.df[
                (self.df['target'] > 20) & (self.df['minutes'] < 15)
            ]

            print(f"\n🚨 High Score + Low Minutes Outliers:")
            print(f"   Count: {len(high_score_low_minutes)} ({len(high_score_low_minutes)/len(self.df)*100:.3f}%)")

            if len(high_score_low_minutes) > 0:
                print(f"   Examples:")
                examples = high_score_low_minutes[['player_name', 'target', 'minutes', 'game_date']].head(5)
                print(examples.to_string(index=False))

                for _, row in high_score_low_minutes.iterrows():
                    outliers.append({
                        'type': 'high_score_low_minutes',
                        'player_name': row['player_name'],
                        'target': row['target'],
                        'minutes': row['minutes'],
                        'game_date': row['game_date'],
                        'severity': 'high'
                    })

        # 2. Low score + High usage outliers
        if 'usage_rate' in self.df.columns:
            # Use median usage rate as threshold
            high_usage_threshold = self.df['usage_rate'].quantile(0.8)
            low_score_high_usage = self.df[
                (self.df['target'] < 5) & (self.df['usage_rate'] > high_usage_threshold)
            ]

            print(f"\n🚨 Low Score + High Usage Outliers:")
            print(f"   Count: {len(low_score_high_usage)} ({len(low_score_high_usage)/len(self.df)*100:.3f}%)")
            print(f"   High usage threshold: {high_usage_threshold:.2f}")

            if len(low_score_high_usage) > 0:
                print(f"   Examples:")
                examples = low_score_high_usage[['player_name', 'target', 'usage_rate', 'game_date']].head(5)
                print(examples.to_string(index=False))

                for _, row in low_score_high_usage.iterrows():
                    outliers.append({
                        'type': 'low_score_high_usage',
                        'player_name': row['player_name'],
                        'target': row['target'],
                        'usage_rate': row['usage_rate'],
                        'game_date': row['game_date'],
                        'severity': 'medium'
                    })

        # 3. Efficiency outliers (very low efficiency with decent minutes)
        if 'efficiency' in self.df.columns and 'minutes' in self.df.columns:
            low_efficiency_threshold = self.df['efficiency'].quantile(0.1)
            efficiency_outliers = self.df[
                (self.df['efficiency'] < low_efficiency_threshold) &
                (self.df['minutes'] > 20) &
                (self.df['target'] > 10)
            ]

            print(f"\n🚨 Low Efficiency + High Minutes + Decent Score Outliers:")
            print(f"   Count: {len(efficiency_outliers)} ({len(efficiency_outliers)/len(self.df)*100:.3f}%)")
            print(f"   Low efficiency threshold: {low_efficiency_threshold:.2f}")

            if len(efficiency_outliers) > 0:
                print(f"   Examples:")
                examples = efficiency_outliers[['player_name', 'target', 'minutes', 'efficiency', 'game_date']].head(5)
                print(examples.to_string(index=False))

        # 4. Statistical outliers using IQR method per tier
        print(f"\n📊 Statistical Outliers by Tier:")
        for tier in ['Bench', 'Rotation', 'Elite']:
            tier_data = self.df[self.df['player_tier'] == tier]
            if len(tier_data) > 0:
                Q1 = tier_data['target'].quantile(0.25)
                Q3 = tier_data['target'].quantile(0.75)
                IQR = Q3 - Q1
                lower_bound = Q1 - 1.5 * IQR
                upper_bound = Q3 + 1.5 * IQR

                tier_outliers = tier_data[
                    (tier_data['target'] < lower_bound) | (tier_data['target'] > upper_bound)
                ]

                print(f"   {tier}: {len(tier_outliers)} outliers ({len(tier_outliers)/len(tier_data)*100:.2f}%)")
                print(f"      Range: [{lower_bound:.1f}, {upper_bound:.1f}]")

        outliers_df = pd.DataFrame(outliers)

        # Store results
        self.analysis_results['context_outliers'] = {
            'total_outliers': len(outliers_df),
            'outlier_types': outliers_df['type'].value_counts().to_dict() if len(outliers_df) > 0 else {}
        }

        return outliers_df

    def contextual_target_clustering(self, n_clusters=5):
        """4. Unsupervised clustering to discover target patterns"""
        print(f"\n🎯 CONTEXTUAL TARGET CLUSTERING")
        print("=" * 60)

        # Select features for clustering
        cluster_features = ['target']

        # Add available context features
        context_features = ['minutes', 'usage_rate', 'efficiency', 'rebounds', 'assists']
        available_features = [f for f in context_features if f in self.df.columns]
        cluster_features.extend(available_features)

        print(f"📊 Clustering features: {cluster_features}")

        # Prepare data for clustering
        cluster_data = self.df[cluster_features].dropna()

        # If we don't have enough features, use just target and minutes
        if len(available_features) == 0 and 'minutes' in self.df.columns:
            cluster_features = ['target', 'minutes']
            cluster_data = self.df[cluster_features].dropna()
            print(f"📊 Using minimal features: {cluster_features}")
        elif len(available_features) == 0:
            cluster_features = ['target']
            cluster_data = self.df[cluster_features].dropna()
            print(f"📊 Using target only: {cluster_features}")

        if len(cluster_data) < 100:
            print("❌ Insufficient data for clustering")
            return None, None

        # Standardize features
        scaler = StandardScaler()
        scaled_data = scaler.fit_transform(cluster_data)

        # Perform K-means clustering
        kmeans = KMeans(n_clusters=n_clusters, random_state=42, n_init=10)
        cluster_labels = kmeans.fit_predict(scaled_data)

        # Add cluster labels to original data
        cluster_data['cluster'] = cluster_labels

        # Analyze clusters
        print(f"\n📊 Cluster Analysis:")
        cluster_summary = cluster_data.groupby('cluster').agg({
            'target': ['count', 'mean', 'std'],
            **{f: 'mean' for f in available_features}
        }).round(3)

        print(cluster_summary)

        # Identify interesting clusters
        cluster_means = cluster_data.groupby('cluster')['target'].mean()

        print(f"\n🔍 Cluster Interpretations:")
        for cluster_id in range(n_clusters):
            cluster_subset = cluster_data[cluster_data['cluster'] == cluster_id]
            avg_target = cluster_subset['target'].mean()
            count = len(cluster_subset)

            # Interpret cluster based on characteristics
            if avg_target < 3:
                interpretation = "Bench/Limited Role Players"
            elif avg_target < 8:
                interpretation = "Role Players"
            elif avg_target < 15:
                interpretation = "Rotation Players"
            else:
                interpretation = "Star Players"

            print(f"   Cluster {cluster_id}: {interpretation}")
            print(f"      Size: {count:,} records ({count/len(cluster_data)*100:.1f}%)")
            print(f"      Avg Points: {avg_target:.2f}")

            if 'minutes' in available_features:
                avg_minutes = cluster_subset['minutes'].mean()
                print(f"      Avg Minutes: {avg_minutes:.1f}")

        # Store results
        self.analysis_results['clustering'] = {
            'n_clusters': n_clusters,
            'features_used': cluster_features,
            'cluster_sizes': cluster_data['cluster'].value_counts().to_dict(),
            'cluster_means': cluster_means.to_dict()
        }

        return cluster_data, kmeans

    def tier_specific_outlier_detection(self):
        """7. Tier-specific outlier detection for different noise characteristics"""
        print(f"\n🎯 TIER-SPECIFIC OUTLIER DETECTION")
        print("=" * 60)

        tier_outliers = {}

        for tier in ['Bench', 'Rotation', 'Elite']:
            tier_data = self.df[self.df['player_tier'] == tier]['target']

            if len(tier_data) == 0:
                continue

            print(f"\n📊 {tier} Players Analysis:")
            print(f"   Records: {len(tier_data):,}")
            print(f"   Mean: {tier_data.mean():.2f}")
            print(f"   Std: {tier_data.std():.2f}")

            # Different outlier detection strategies per tier
            if tier == 'Bench':
                # For bench players, focus on label noise (unexpected high scores)
                threshold = tier_data.quantile(0.95)  # Top 5%
                outliers = tier_data[tier_data > threshold]
                print(f"   Outlier threshold (95th percentile): {threshold:.2f}")
                print(f"   Potential label noise: {len(outliers)} ({len(outliers)/len(tier_data)*100:.2f}%)")

            elif tier == 'Rotation':
                # For rotation players, use IQR method
                Q1 = tier_data.quantile(0.25)
                Q3 = tier_data.quantile(0.75)
                IQR = Q3 - Q1
                lower_bound = Q1 - 1.5 * IQR
                upper_bound = Q3 + 1.5 * IQR
                outliers = tier_data[(tier_data < lower_bound) | (tier_data > upper_bound)]
                print(f"   IQR bounds: [{lower_bound:.2f}, {upper_bound:.2f}]")
                print(f"   Statistical outliers: {len(outliers)} ({len(outliers)/len(tier_data)*100:.2f}%)")

            else:  # Elite
                # For elite players, expect more variance but check for extreme values
                mean = tier_data.mean()
                std = tier_data.std()
                lower_bound = mean - 3 * std
                upper_bound = mean + 3 * std
                outliers = tier_data[(tier_data < lower_bound) | (tier_data > upper_bound)]
                print(f"   3-sigma bounds: [{lower_bound:.2f}, {upper_bound:.2f}]")
                print(f"   Extreme outliers: {len(outliers)} ({len(outliers)/len(tier_data)*100:.2f}%)")

            # Coefficient of variation (volatility measure)
            cv = tier_data.std() / tier_data.mean()
            print(f"   Coefficient of Variation: {cv:.3f}")

            tier_outliers[tier] = {
                'count': len(tier_data),
                'outliers': len(outliers),
                'outlier_percentage': len(outliers)/len(tier_data)*100,
                'coefficient_of_variation': cv,
                'mean': tier_data.mean(),
                'std': tier_data.std()
            }

        # Compare noise characteristics across tiers
        print(f"\n📊 Noise Characteristics Comparison:")
        print(f"{'Tier':<10} {'CV':<8} {'Outlier %':<10} {'Noise Level'}")
        print("-" * 40)

        for tier, stats in tier_outliers.items():
            cv = stats['coefficient_of_variation']
            outlier_pct = stats['outlier_percentage']

            # Determine noise level
            if cv > 1.0 and outlier_pct > 5:
                noise_level = "High"
            elif cv > 0.8 or outlier_pct > 3:
                noise_level = "Medium"
            else:
                noise_level = "Low"

            print(f"{tier:<10} {cv:<8.3f} {outlier_pct:<10.2f} {noise_level}")

        # Store results
        self.analysis_results['tier_outliers'] = tier_outliers

        return tier_outliers

    def psychological_event_analysis(self):
        """6. Analyze psychological/external event impacts"""
        print(f"\n🧠 PSYCHOLOGICAL/EXTERNAL EVENT ANALYSIS")
        print("=" * 60)

        # Check if we have date information for back-to-back analysis
        if 'game_date' not in self.df.columns:
            print("❌ No game_date column - skipping event analysis")
            return None

        event_results = {}

        # 1. Back-to-back game analysis
        print(f"📊 Back-to-Back Game Analysis:")

        # Sort by player and date
        df_sorted = self.df.sort_values(['player_name', 'game_date'])

        # Calculate days between games for each player
        df_sorted['days_since_last_game'] = df_sorted.groupby('player_name')['game_date'].diff().dt.days

        # Identify back-to-back games (1 day difference)
        back_to_back = df_sorted[df_sorted['days_since_last_game'] == 1]
        regular_games = df_sorted[df_sorted['days_since_last_game'] > 1]

        if len(back_to_back) > 0 and len(regular_games) > 0:
            b2b_avg = back_to_back['target'].mean()
            regular_avg = regular_games['target'].mean()

            print(f"   Back-to-back games: {len(back_to_back):,}")
            print(f"   Regular games: {len(regular_games):,}")
            print(f"   B2B average points: {b2b_avg:.2f}")
            print(f"   Regular average points: {regular_avg:.2f}")
            print(f"   Performance difference: {b2b_avg - regular_avg:+.2f} points")

            event_results['back_to_back'] = {
                'b2b_games': len(back_to_back),
                'b2b_avg_points': b2b_avg,
                'regular_avg_points': regular_avg,
                'performance_difference': b2b_avg - regular_avg
            }

        # 2. Rest days impact analysis
        print(f"\n📊 Rest Days Impact Analysis:")

        # Categorize by rest days
        rest_categories = {
            '1 day': df_sorted[df_sorted['days_since_last_game'] == 1],
            '2-3 days': df_sorted[df_sorted['days_since_last_game'].between(2, 3)],
            '4-7 days': df_sorted[df_sorted['days_since_last_game'].between(4, 7)],
            '8+ days': df_sorted[df_sorted['days_since_last_game'] >= 8]
        }

        rest_analysis = {}
        for category, data in rest_categories.items():
            if len(data) > 0:
                avg_points = data['target'].mean()
                print(f"   {category}: {len(data):,} games, avg {avg_points:.2f} pts")
                rest_analysis[category] = {
                    'games': len(data),
                    'avg_points': avg_points
                }

        event_results['rest_days'] = rest_analysis

        # Store results
        self.analysis_results['psychological_events'] = event_results

        return event_results

    def run_complete_advanced_analysis(self):
        """Run all advanced target analyses"""
        print("🎯 ADVANCED WNBA TARGET ANALYSIS")
        print("=" * 70)
        print("🔬 Professional-grade target quality assessment")
        print("📊 Dataset:", self.dataset_path)
        print()

        # Load and prepare data
        self.load_and_prepare_data()

        # Run all analyses
        print("\n🚀 Running Advanced Analyses...")

        # 1. Per-game variance mapping
        variance_df, high_volatility = self.per_game_variance_mapping()

        # 2. Context-aware outlier detection
        context_outliers = self.context_aware_outlier_detection()

        # 4. Contextual target clustering
        cluster_result = self.contextual_target_clustering()
        if cluster_result is not None:
            cluster_data, kmeans_model = cluster_result
        else:
            cluster_data, kmeans_model = None, None

        # 6. Psychological event analysis
        event_analysis = self.psychological_event_analysis()

        # 7. Tier-specific outlier detection
        tier_outliers = self.tier_specific_outlier_detection()

        # Generate comprehensive summary
        print("\n" + "=" * 70)
        print("📋 ADVANCED ANALYSIS SUMMARY")
        print("=" * 70)

        print(f"🎯 Target Quality Assessment:")
        print(f"   Total records analyzed: {len(self.df):,}")
        print(f"   Unique players: {self.df['player_name'].nunique():,}")
        print(f"   Date range: {self.df['game_date'].min()} to {self.df['game_date'].max()}")

        print(f"\n📈 Variance Analysis:")
        if 'variance_mapping' in self.analysis_results:
            vm = self.analysis_results['variance_mapping']
            print(f"   Players with sufficient data: {vm['total_players']}")
            print(f"   Average volatility: {vm['avg_volatility']:.2f}")
            print(f"   High volatility players: {vm['high_volatility_players']}")

        print(f"\n🔍 Context Outliers:")
        if 'context_outliers' in self.analysis_results:
            co = self.analysis_results['context_outliers']
            print(f"   Total context outliers: {co['total_outliers']}")
            for outlier_type, count in co['outlier_types'].items():
                print(f"   {outlier_type}: {count}")

        print(f"\n🎯 Clustering Insights:")
        if 'clustering' in self.analysis_results:
            cl = self.analysis_results['clustering']
            print(f"   Clusters identified: {cl['n_clusters']}")
            print(f"   Features used: {len(cl['features_used'])}")

        print(f"\n🏀 Tier-Specific Analysis:")
        if 'tier_outliers' in self.analysis_results:
            for tier, stats in self.analysis_results['tier_outliers'].items():
                print(f"   {tier}: {stats['outlier_percentage']:.2f}% outliers, CV={stats['coefficient_of_variation']:.3f}")

        # Save comprehensive results
        import json
        results_file = f"advanced_target_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(results_file, 'w') as f:
            json.dump(self.analysis_results, f, indent=2, default=str)

        print(f"\n📄 Detailed results saved: {results_file}")
        print("\n🎉 ADVANCED TARGET ANALYSIS COMPLETE!")
        print("🚀 Your target variable has been analyzed at professional level!")

        return self.analysis_results


def main():
    """Main execution function"""

    print("🎯 ADVANCED WNBA TARGET ANALYSIS")
    print("=" * 50)
    print("🔬 Professional-grade target analysis...")
    print()

    try:
        # Create analyzer and run complete analysis
        analyzer = AdvancedWNBATargetAnalysis()
        results = analyzer.run_complete_advanced_analysis()

        print("\n🎉 ANALYSIS COMPLETE!")
        print("📊 Your target variable has been analyzed with advanced techniques")
        print("🚀 Ready for world-class model training!")

        return True

    except Exception as e:
        print(f"❌ Error during analysis: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    if not success:
        exit(1)