#!/usr/bin/env python3
"""
🔧 PROPER MODEL RETRAINING - PROFESSIONAL SOLUTION
=================================================

Complete retraining of WNBA models with:
1. Correct feature handling and validation
2. Fixed role embedding integration  
3. Proper validation during training
4. Thorough testing before deployment

Target: <2.0 MAE professional accuracy
"""

import pandas as pd
import numpy as np
import torch
import torch.nn as nn
import pytorch_lightning as pl
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler, RobustScaler
from sklearn.metrics import mean_absolute_error, mean_squared_error
import json
from datetime import datetime, timedelta
from pathlib import Path
import sys
import warnings
warnings.filterwarnings('ignore')

# Add src to path
sys.path.append('src')
sys.path.append('src/models')

class ProperWNBAModel(pl.LightningModule):
    """Properly designed WNBA model with correct feature handling"""
    
    def __init__(self, input_dim: int, dropout: float = 0.3, learning_rate: float = 1e-3):
        """
        Initialize model with exact feature dimensions
        
        Args:
            input_dim: Exact number of input features (no role embedding confusion)
            dropout: Dropout probability
            learning_rate: Learning rate
        """
        super().__init__()
        self.save_hyperparameters()
        
        # Simple, robust architecture
        self.network = nn.Sequential(
            nn.Linear(input_dim, 256),
            nn.BatchNorm1d(256),
            nn.ReLU(),
            nn.Dropout(dropout),
            
            nn.Linear(256, 128),
            nn.BatchNorm1d(128),
            nn.ReLU(),
            nn.Dropout(dropout),
            
            nn.Linear(128, 64),
            nn.BatchNorm1d(64),
            nn.ReLU(),
            nn.Dropout(dropout),
            
            nn.Linear(64, 32),
            nn.ReLU(),
            
            nn.Linear(32, 1)
        )
        
        # Loss function - Huber loss for robustness
        self.loss_fn = nn.HuberLoss(delta=1.0)
        
        print(f"🤖 Proper WNBA Model initialized:")
        print(f"   Input dimension: {input_dim}")
        print(f"   Architecture: {input_dim} -> 256 -> 128 -> 64 -> 32 -> 1")
        print(f"   Dropout: {dropout}")
        print(f"   Learning rate: {learning_rate}")
    
    def forward(self, x):
        """Forward pass - simple and reliable"""
        return self.network(x).squeeze()
    
    def training_step(self, batch, batch_idx):
        """Training step with proper logging"""
        x, y = batch
        y_pred = self(x)
        loss = self.loss_fn(y_pred, y)
        
        # Calculate MAE for monitoring
        mae = torch.mean(torch.abs(y_pred - y))
        
        self.log('train_loss', loss, prog_bar=True)
        self.log('train_mae', mae, prog_bar=True)
        
        return loss
    
    def validation_step(self, batch, batch_idx):
        """Validation step with comprehensive metrics"""
        x, y = batch
        y_pred = self(x)
        loss = self.loss_fn(y_pred, y)
        
        # Calculate metrics
        mae = torch.mean(torch.abs(y_pred - y))
        mse = torch.mean((y_pred - y) ** 2)
        
        self.log('val_loss', loss, prog_bar=True)
        self.log('val_mae', mae, prog_bar=True)
        self.log('val_mse', mse)
        
        return {'val_loss': loss, 'val_mae': mae, 'predictions': y_pred, 'targets': y}
    
    def configure_optimizers(self):
        """Configure optimizer with proper scheduling"""
        optimizer = torch.optim.AdamW(
            self.parameters(), 
            lr=self.hparams.learning_rate,
            weight_decay=1e-5
        )
        
        scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(
            optimizer,
            mode='min',
            factor=0.5,
            patience=10
        )
        
        return {
            'optimizer': optimizer,
            'lr_scheduler': {
                'scheduler': scheduler,
                'monitor': 'val_mae'
            }
        }

class ProperModelRetrainer:
    """Professional model retraining system"""
    
    def __init__(self):
        """Initialize the retrainer"""
        print("🔧 PROPER MODEL RETRAINING - PROFESSIONAL SOLUTION")
        print("=" * 60)
        print("🎯 Target: <2.0 MAE professional accuracy")
        
    def load_and_prepare_data_properly(self):
        """Load and prepare data with proper validation"""
        try:
            print("\n📊 Loading and preparing data properly...")
            
            # Load the full dataset
            df = pd.read_csv('data/master/wnba_definitive_master_dataset_FIXED.csv', low_memory=False)
            df['game_date'] = pd.to_datetime(df['game_date'], errors='coerce')
            
            print(f"   Raw data: {len(df)} records, {len(df.columns)} columns")
            
            # Get target variable
            if 'points' in df.columns:
                target_col = 'points'
            elif 'target' in df.columns:
                target_col = 'target'
            else:
                print("❌ No target column found")
                return None
            
            # Clean data properly but less aggressively
            print("   🧹 Cleaning data...")

            # Remove invalid targets (be less aggressive)
            df = df[
                (df[target_col].notna()) &
                (df[target_col] >= 0) &
                (df[target_col] <= 60)  # More generous WNBA range
            ].copy()

            print(f"   After cleaning: {len(df)} records")
            
            # Feature selection with proper validation
            print("   🎯 Selecting robust features...")
            
            exclude_cols = {
                'points', 'target', 'player_id', 'game_id', 'game_date', 
                'player_name', 'team', 'opponent', 'season', 'Unnamed: 0'
            }
            
            # Get potential features
            potential_features = [col for col in df.columns if col not in exclude_cols]
            
            # Select features with proper validation
            robust_features = []
            for col in potential_features:
                try:
                    # Convert to numeric
                    df[col] = pd.to_numeric(df[col], errors='coerce')
                    
                    if df[col].dtype in ['float64', 'int64']:
                        # Check data quality
                        missing_pct = df[col].isnull().sum() / len(df)
                        unique_values = df[col].nunique()
                        
                        # Only keep high-quality features (be more generous)
                        if missing_pct < 0.7 and unique_values > 1:  # <70% missing, has variance
                            robust_features.append(col)

                            # Stop at reasonable number of features
                            if len(robust_features) >= 150:
                                break
                except:
                    continue
            
            print(f"   Selected {len(robust_features)} robust features")
            
            # Fill missing values properly
            for col in robust_features:
                median_val = df[col].median()
                if pd.isna(median_val):
                    median_val = 0.0
                df[col] = df[col].fillna(median_val)
            
            # Create feature matrix
            X = df[robust_features].values.astype(np.float32)
            y = df[target_col].values.astype(np.float32)
            
            # Remove extreme outliers
            print("   🔧 Removing extreme outliers...")
            
            # Remove target outliers (beyond 3 std)
            target_mean = np.mean(y)
            target_std = np.std(y)
            outlier_mask = np.abs(y - target_mean) <= 3 * target_std
            
            X = X[outlier_mask]
            y = y[outlier_mask]
            
            # Remove feature outliers
            for i in range(X.shape[1]):
                col_data = X[:, i]
                if np.std(col_data) > 0:
                    col_mean = np.mean(col_data)
                    col_std = np.std(col_data)
                    outlier_mask = np.abs(col_data - col_mean) <= 3 * col_std
                    X = X[outlier_mask]
                    y = y[outlier_mask]
            
            print(f"   Final data: {X.shape[0]} samples, {X.shape[1]} features")
            print(f"   Target stats: mean={np.mean(y):.1f}, std={np.std(y):.1f}, range=[{np.min(y):.1f}, {np.max(y):.1f}]")
            print(f"   Feature stats: mean={np.mean(X):.3f}, std={np.mean(np.std(X, axis=0)):.3f}")
            
            return X, y, robust_features
            
        except Exception as e:
            print(f"❌ Error preparing data: {e}")
            return None
    
    def create_proper_train_test_split(self, X, y):
        """Create proper temporal train/test split"""
        print("\n🔄 Creating proper train/test split...")
        
        # Use 80/20 split with stratification by target ranges
        # Create target bins for stratification
        y_bins = pd.cut(y, bins=5, labels=False)
        
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, 
            test_size=0.2, 
            random_state=42,
            stratify=y_bins
        )
        
        print(f"   Train: {len(X_train)} samples")
        print(f"   Test: {len(X_test)} samples")
        print(f"   Train target: mean={np.mean(y_train):.1f}, std={np.std(y_train):.1f}")
        print(f"   Test target: mean={np.mean(y_test):.1f}, std={np.std(y_test):.1f}")
        
        return X_train, X_test, y_train, y_test
    
    def scale_features_properly(self, X_train, X_test):
        """Scale features with robust scaling"""
        print("\n📏 Scaling features properly...")
        
        # Use RobustScaler for better outlier handling
        scaler = RobustScaler()
        
        X_train_scaled = scaler.fit_transform(X_train)
        X_test_scaled = scaler.transform(X_test)
        
        print(f"   Scaled train: mean={np.mean(X_train_scaled):.3f}, std={np.mean(np.std(X_train_scaled, axis=0)):.3f}")
        print(f"   Scaled test: mean={np.mean(X_test_scaled):.3f}, std={np.mean(np.std(X_test_scaled, axis=0)):.3f}")
        
        return X_train_scaled, X_test_scaled, scaler
    
    def create_data_loaders(self, X_train, X_test, y_train, y_test):
        """Create PyTorch data loaders"""
        print("\n📦 Creating data loaders...")
        
        # Convert to tensors
        train_dataset = torch.utils.data.TensorDataset(
            torch.FloatTensor(X_train),
            torch.FloatTensor(y_train)
        )
        
        test_dataset = torch.utils.data.TensorDataset(
            torch.FloatTensor(X_test),
            torch.FloatTensor(y_test)
        )
        
        # Create data loaders
        train_loader = torch.utils.data.DataLoader(
            train_dataset,
            batch_size=64,
            shuffle=True,
            num_workers=0,  # Set to 0 for Windows compatibility
            pin_memory=True
        )
        
        test_loader = torch.utils.data.DataLoader(
            test_dataset,
            batch_size=64,
            shuffle=False,
            num_workers=0,
            pin_memory=True
        )
        
        print(f"   Train loader: {len(train_loader)} batches")
        print(f"   Test loader: {len(test_loader)} batches")
        
        return train_loader, test_loader
    
    def train_model_properly(self, train_loader, test_loader, input_dim):
        """Train model with proper validation"""
        print("\n🚀 Training model properly...")
        
        # Create model
        model = ProperWNBAModel(
            input_dim=input_dim,
            dropout=0.3,
            learning_rate=1e-3
        )
        
        # Setup trainer with proper callbacks
        trainer = pl.Trainer(
            max_epochs=100,
            accelerator='auto',
            devices=1,
            callbacks=[
                pl.callbacks.EarlyStopping(
                    monitor='val_mae',
                    patience=15,
                    mode='min',
                    verbose=True
                ),
                pl.callbacks.ModelCheckpoint(
                    monitor='val_mae',
                    mode='min',
                    save_top_k=1,
                    filename='best_model_{epoch:02d}_{val_mae:.4f}',
                    verbose=True
                ),
                pl.callbacks.LearningRateMonitor(logging_interval='epoch')
            ],
            log_every_n_steps=10,
            enable_progress_bar=True,
            enable_model_summary=True
        )
        
        # Train the model
        print("   🏋️ Starting training...")
        trainer.fit(model, train_loader, test_loader)
        
        # Get best model
        best_model_path = trainer.checkpoint_callback.best_model_path
        best_model = ProperWNBAModel.load_from_checkpoint(best_model_path)
        best_model.eval()
        
        print(f"   ✅ Training completed!")
        print(f"   📁 Best model: {best_model_path}")
        print(f"   🎯 Best validation MAE: {trainer.checkpoint_callback.best_model_score:.4f}")
        
        return best_model, best_model_path, trainer.checkpoint_callback.best_model_score
    
    def test_model_thoroughly(self, model, test_loader, X_test, y_test):
        """Test model thoroughly with comprehensive metrics"""
        print("\n🧪 Testing model thoroughly...")
        
        model.eval()
        all_predictions = []
        all_targets = []
        
        with torch.no_grad():
            for batch in test_loader:
                x, y = batch
                predictions = model(x)
                
                all_predictions.extend(predictions.cpu().numpy())
                all_targets.extend(y.cpu().numpy())
        
        all_predictions = np.array(all_predictions)
        all_targets = np.array(all_targets)
        
        # Calculate comprehensive metrics
        mae = mean_absolute_error(all_targets, all_predictions)
        mse = mean_squared_error(all_targets, all_predictions)
        rmse = np.sqrt(mse)
        mape = np.mean(np.abs((all_targets - all_predictions) / np.maximum(all_targets, 1))) * 100
        
        # Additional metrics
        pred_std = np.std(all_predictions)
        target_std = np.std(all_targets)
        correlation = np.corrcoef(all_predictions, all_targets)[0, 1]
        
        print(f"   📊 Comprehensive Test Results:")
        print(f"      🎯 MAE: {mae:.3f} points")
        print(f"      📈 RMSE: {rmse:.3f} points")
        print(f"      📊 MAPE: {mape:.1f}%")
        print(f"      🔗 Correlation: {correlation:.3f}")
        print(f"      ⚖️ Avg Target: {np.mean(all_targets):.1f} points")
        print(f"      🔮 Avg Predicted: {np.mean(all_predictions):.1f} points")
        print(f"      📊 Target Std: {target_std:.1f} points")
        print(f"      📊 Pred Std: {pred_std:.1f} points")
        
        # Show sample predictions
        print(f"   📝 Sample predictions:")
        for i in range(min(10, len(all_predictions))):
            error = abs(all_predictions[i] - all_targets[i])
            print(f"      Sample {i+1}: Predicted {all_predictions[i]:.1f}, Actual {all_targets[i]:.1f} (Error: {error:.1f})")
        
        # Assessment
        if mae < 2.0:
            status = "🏆 EXCELLENT - Professional grade!"
        elif mae < 3.0:
            status = "✅ GOOD - Production ready"
        elif mae < 4.0:
            status = "⚠️ ACCEPTABLE - Needs monitoring"
        else:
            status = "❌ POOR - Requires improvement"
        
        print(f"   📊 Assessment: {status}")
        
        return {
            'mae': mae,
            'rmse': rmse,
            'mape': mape,
            'correlation': correlation,
            'predictions': all_predictions.tolist(),
            'targets': all_targets.tolist(),
            'pred_std': pred_std,
            'target_std': target_std,
            'status': status
        }
    
    def run_proper_retraining(self):
        """Run the complete proper retraining pipeline"""
        print("🚀 Starting proper model retraining...")
        
        # Step 1: Load and prepare data
        data = self.load_and_prepare_data_properly()
        if data is None:
            return False
        
        X, y, features = data
        
        # Step 2: Create train/test split
        X_train, X_test, y_train, y_test = self.create_proper_train_test_split(X, y)
        
        # Step 3: Scale features
        X_train_scaled, X_test_scaled, scaler = self.scale_features_properly(X_train, X_test)
        
        # Step 4: Create data loaders
        train_loader, test_loader = self.create_data_loaders(X_train_scaled, X_test_scaled, y_train, y_test)
        
        # Step 5: Train model
        model, model_path, best_val_mae = self.train_model_properly(train_loader, test_loader, X_train_scaled.shape[1])
        
        # Step 6: Test thoroughly
        test_results = self.test_model_thoroughly(model, test_loader, X_test_scaled, y_test)
        
        # Step 7: Save everything
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Save model info
        model_info = {
            'timestamp': datetime.now().isoformat(),
            'model_path': model_path,
            'input_dim': X_train_scaled.shape[1],
            'features': features,
            'scaler_params': {
                'center': scaler.center_.tolist(),
                'scale': scaler.scale_.tolist()
            },
            'training_samples': len(X_train),
            'test_samples': len(X_test),
            'best_val_mae': float(best_val_mae),
            'test_results': test_results
        }
        
        info_path = f"proper_model_info_{timestamp}.json"
        with open(info_path, 'w') as f:
            json.dump(model_info, f, indent=2, default=float)
        
        print(f"\n💾 Model info saved to: {info_path}")
        
        # Final assessment
        test_mae = test_results['mae']
        print(f"\n🎯 FINAL ASSESSMENT:")
        if test_mae < 2.0:
            print(f"   🏆 SUCCESS: {test_mae:.3f} MAE is professional-grade!")
            print(f"   ✅ Model ready for production deployment")
        elif test_mae < 3.0:
            print(f"   ✅ GOOD: {test_mae:.3f} MAE is production ready")
            print(f"   ✅ Model acceptable for deployment with monitoring")
        elif test_mae < 4.0:
            print(f"   ⚠️ ACCEPTABLE: {test_mae:.3f} MAE needs improvement")
            print(f"   🔧 Consider additional feature engineering")
        else:
            print(f"   ❌ POOR: {test_mae:.3f} MAE is unacceptable")
            print(f"   🔧 Requires data quality investigation")
        
        print(f"\n📊 Proper retraining completed!")
        print(f"   ✅ Model trained on {len(X_train)} samples")
        print(f"   ✅ Tested on {len(X_test)} samples")
        print(f"   ✅ {len(features)} robust features")
        print(f"   ✅ Professional validation pipeline")
        
        return test_mae < 4.0


def main():
    """Main function"""
    retrainer = ProperModelRetrainer()
    success = retrainer.run_proper_retraining()
    
    if success:
        print("\n✅ PROPER MODEL RETRAINING COMPLETED SUCCESSFULLY!")
        print("🏀 Professional WNBA prediction model ready!")
    else:
        print("\n❌ Proper retraining failed")


if __name__ == "__main__":
    main()
