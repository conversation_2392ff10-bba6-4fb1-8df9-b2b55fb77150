{"timestamp": "2025-07-12T21:59:12.590946", "overall_status": "warning", "overall_health_score": 78.0, "systems": {"data_collection": {"status": "healthy", "health_score": 100, "last_update": "2025-07-11T15:56:45.923585", "alerts": ["No collection in 30.0 hours", "Log not updated in 30.7 hours"], "metrics": {"collector": "Available", "collections_7d": 2, "last_date": "2025-07-11", "last_time": "2025-07-11T15:56", "records": 0, "new_games": 0, "total_games": 128, "games_7d": 128, "api_requests_today": 0, "endpoints_processed": 0, "log_size_kb": 0.17, "log_last_modified": "2025-07-11 15:16:10", "ai_anomaly_score": 22.2}}, "federated_learning": {"status": "healthy", "health_score": 100.0, "last_update": "2025-07-11T14:57:24.147475", "alerts": [], "metrics": {"teams_monitored": 13, "teams_with_drift": 0, "fairness_violations": 0, "schema_status": "valid", "config_status": "compliant", "ai_anomaly_score": 3.1}}, "federated_multiverse": {"status": "WARNING", "health_score": 80.0, "last_update": "2025-07-12T21:59:11.430975", "alerts": ["Expert mapping integration issue: No module named 'expert_multiverse_integration'..."], "metrics": {"integration_file": "Available", "team_scripts": 13, "master_launcher": "Available", "active_teams": 8, "total_teams": 13, "participation_rate": 0.62, "current_round": 15, "global_mae": 2.85, "convergence_status": "converging", "multiverse_models": 9, "privacy_budget": 1.0, "expert_integration": "Error", "ai_anomaly_score": 19.7}}, "data_infrastructure": {"status": "healthy", "health_score": 100, "last_update": "2025-07-12T21:59:12.337351", "alerts": [], "metrics": {"dataset_size_mb": 88.53, "columns": 642, "total_rows": "29,237", "data_completeness": "642 columns, 29,237 rows", "federated_teams": 13, "federated_coverage": "13/13 teams", "team_isolated_files": 39, "team_splits_status": "39/39 files (13 teams × 3 splits)", "ai_anomaly_score": 2.8}}, "model_system": {"status": "warning", "health_score": 70, "last_update": "2025-07-12T21:59:12.343428", "alerts": ["Missing model directory: model1_real_data", "Missing model source: player_points_model.py"], "metrics": {"model_directories": 26, "model_source_files": 3, "ai_anomaly_score": 30.4}}, "backup_system": {"status": "healthy", "health_score": 100, "last_update": "2025-07-12T21:59:12.355004", "alerts": [], "metrics": {"backup_files": 1, "latest_backup_age_hours": 30.7, "latest_backup_file": "master_dataset_backup_2025-07-11.csv", "total_backup_size_mb": 88.53, "model_backup_dirs": 0, "ai_anomaly_score": 0}}, "api_health": {"status": "healthy", "health_score": 100, "last_update": "2025-07-12T21:59:12.359095", "alerts": [], "metrics": {"api_requests_today": 0, "api_credits_remaining": 500, "last_api_request": "2025-07-11T17:10:00.000000", "config_files_available": 2, "config_files": ["unified_collector_config.json", "wnba_2025_season_config.json"], "api_log_files": 0, "ai_anomaly_score": 6.6}}, "system_resources": {"status": "healthy", "health_score": 100, "last_update": "2025-07-12T21:59:12.556877", "alerts": [], "metrics": {"data_size_mb": 575.19, "models_size_mb": 367.66, "logs_size_mb": 0.03, "backups_size_mb": 95.92, "reports_size_mb": 0.06, "total_disk_usage_mb": 1038.86, "total_disk_usage_gb": 1.01, "log_files": 2, "log_file_sizes": ["unified_monitoring.log: 1.84KB", "unified_wnba_collector.log: 0.17KB"], "ai_anomaly_score": 5.4}}, "configuration": {"status": "healthy", "health_score": 100, "last_update": "2025-07-12T21:59:12.559861", "alerts": [], "metrics": {"available_configs": 4, "missing_configs": 0, "config_files_list": ["unified_collector_config.json", "wnba_2025_season_config.json", "federated_config.json", "clean_features_list.json"], "python_configs": 2, "requirements_files": 2, "environment_variables": 0, "ai_anomaly_score": 5.3}}, "player_tracking": {"status": "healthy", "health_score": 94, "last_update": "2025-07-12T21:59:12.560412", "alerts": [], "metrics": {"Players Tracked": "97%", "Accuracy": "97.4%", "Latency": "79ms", "Court Coverage": "99%", "Data Points/Sec": "2,775", "ai_anomaly_score": 3.7}}, "shot_analysis": {"status": "healthy", "health_score": 99, "last_update": "2025-07-12T21:59:12.560643", "alerts": [], "metrics": {"Shot Detection": "98.7%", "Arc Analysis": "95.7%", "Release Time": "0.40s", "Make Prediction": "82%", "Shots Analyzed": "1,255", "ai_anomaly_score": 6.7}}, "injury_prediction": {"status": "healthy", "health_score": 91, "last_update": "2025-07-12T21:59:12.560825", "alerts": [], "metrics": {"Accuracy": "92%", "Prevented Injuries": "9", "Risk Alerts": "7", "Load Monitoring": "98%", "Recovery Tracking": "92%", "ai_anomaly_score": 6.6}}, "game_simulation": {"status": "healthy", "health_score": 96, "last_update": "2025-07-12T21:59:12.561043", "alerts": [], "metrics": {"Accuracy": "83.6%", "Sim Speed": "257x realtime", "Scenarios Run": "2,220", "Win Prob Accuracy": "78%", "Model Confidence": "88%", "ai_anomaly_score": 3.3}}, "betting_optimization": {"status": "healthy", "health_score": 90, "last_update": "2025-07-12T21:59:12.561241", "alerts": [], "metrics": {"Line Accuracy": "78.6%", "Market Coverage": "99%", "Update Frequency": "19s", "Profit Margin": "5.4%", "Risk Assessment": "96%", "ai_anomaly_score": 1.3}}, "performance_forecasting": {"status": "healthy", "health_score": 95, "last_update": "2025-07-12T21:59:12.561402", "alerts": [], "metrics": {"Player Predictions": "85.3%", "Team Predictions": "82.2%", "Seasonal Trends": "86%", "Lineup Optimization": "93%", "Model Drift": "3.9%", "ai_anomaly_score": 7.2}}, "opponent_scouting": {"status": "healthy", "health_score": 95, "last_update": "2025-07-12T21:59:12.561559", "alerts": [], "metrics": {"Play Recognition": "95.2%", "Tendency Analysis": "88%", "Weakness Detection": "90%", "Strategy Effectiveness": "84%", "Report Generation": "99%", "ai_anomaly_score": 3.9}}, "fan_engagement": {"status": "healthy", "health_score": 90, "last_update": "2025-07-12T21:59:12.561715", "alerts": [], "metrics": {"Real-time Updates": "100%", "Personalization": "85%", "Content Delivery": "99%", "User Satisfaction": "4.4/5", "Engagement Rate": "76%", "ai_anomaly_score": 3.6}}, "referee_analytics": {"status": "healthy", "health_score": 94, "last_update": "2025-07-12T21:59:12.561871", "alerts": [], "metrics": {"Call Accuracy": "94.3%", "Consistency Score": "93%", "Game Flow Impact": "3.4", "Bias Detection": "96%", "Performance Rating": "8.8/10", "ai_anomaly_score": 0}}, "player_load_monitoring": {"status": "healthy", "health_score": 94, "last_update": "2025-07-12T21:59:12.562566", "alerts": [], "metrics": {"Load Index": "79%", "Fatigue Risk": "Medium", "Peak Load": "95%", "Recovery": "Optimal", "Injury Risk": "10.4%", "ai_anomaly_score": 5.1}}, "line_movement_watchdog": {"status": "ERROR", "health_score": 0.0, "last_update": "2025-07-12T21:59:12.570874", "alerts": ["Status check failed: 'charmap' codec can't decode byte 0x90 in position 3479: character maps to <undefined>"], "metrics": {"ai_anomaly_score": 49.3}}, "medusa_autopilot": {"status": "ERROR", "health_score": 0.0, "last_update": "2025-07-12T21:59:12.576277", "alerts": ["Status check failed: 'charmap' codec can't decode byte 0x90 in position 3479: character maps to <undefined>"], "metrics": {"ai_anomaly_score": 46.2}}, "drift_detection": {"status": "ERROR", "health_score": 0.0, "last_update": "2025-07-12T21:59:12.584610", "alerts": ["Status check failed: 'charmap' codec can't decode byte 0x81 in position 1868: character maps to <undefined>"], "metrics": {"ai_anomaly_score": 55.6}}, "hybrid_prediction_system": {"status": "ERROR", "health_score": 0.0, "last_update": "2025-07-12T21:59:12.587890", "alerts": ["Status check failed: 'charmap' codec can't decode byte 0x9d in position 3588: character maps to <undefined>"], "metrics": {"ai_anomaly_score": 45.9}}, "accuracy_tracking": {"status": "WARNING", "health_score": 85.0, "last_update": "2025-07-12T21:59:12.589330", "alerts": ["Accuracy tracking database not found"], "metrics": {"tracking_models": ["Centralized", "Federated", "Multiverse", "Hybrid"], "accuracy_trend": "Declining", "last_accuracy_update": "30 min ago", "prediction_confidence": "0.79", "model_agreement_rate": "0.71", "ai_anomaly_score": 9.8}}}, "summary": {"total_systems": 16, "healthy_systems": 18, "warning_systems": 0, "critical_systems": 1, "offline_systems": 0, "total_alerts": 3, "games_today": 3, "predictions_accuracy": "83%", "player_coverage": "98.7%", "models_active": 14, "data_streams": 27}, "alerts": ["Data Collection: No collection in 30.0 hours", "Data Collection: Log not updated in 30.7 hours", "Federated Multiverse: Expert mapping integration issue: No module named 'expert_multiverse_integration'...", "Model System: Missing model directory: model1_real_data", "Model System: Missing model source: player_points_model.py", "Line Movement Watchdog: Status check failed: 'charmap' codec can't decode byte 0x90 in position 3479: character maps to <undefined>", "Medusa Autopilot: Status check failed: 'charmap' codec can't decode byte 0x90 in position 3479: character maps to <undefined>", "Drift Detection: Status check failed: 'charmap' codec can't decode byte 0x81 in position 1868: character maps to <undefined>", "Hybrid Prediction System: Status check failed: 'charmap' codec can't decode byte 0x9d in position 3588: character maps to <undefined>", "Accuracy Tracking: Accuracy tracking database not found"], "recommendations": ["System health below 80% - investigate critical issues", "Warning systems need monitoring: model_system"], "live_games": [{"id": "**********", "matchup": "MIN @ CHI", "home_team": "CHI", "away_team": "MIN", "home_score": 0, "away_score": 0, "quarter": "Final", "time_remaining": "0:00", "game_status": "Final", "possession": "MIN", "win_probability": {"home": 50, "away": 50}, "pace": 100, "lead_changes": 0, "largest_lead": 0}, {"id": "**********", "matchup": "GSV @ LVA", "home_team": "LVA", "away_team": "GSV", "home_score": 0, "away_score": 0, "quarter": "Final", "time_remaining": "0:00", "game_status": "Final", "possession": "GSV", "win_probability": {"home": 50, "away": 50}, "pace": 100, "lead_changes": 0, "largest_lead": 0}], "top_performers": [{"name": "<PERSON><PERSON><PERSON>", "team": "SEA", "position": "F", "points": 28, "rebounds": 12, "assists": 6, "efficiency": 35, "usage_rate": "23%", "true_shooting": "59%", "plus_minus": 0, "minutes": 38, "player_impact": "10.5"}, {"name": "<PERSON><PERSON><PERSON>", "team": "SEA", "position": "F", "points": 28, "rebounds": 12, "assists": 6, "efficiency": 35, "usage_rate": "23%", "true_shooting": "59%", "plus_minus": 0, "minutes": 38, "player_impact": "10.5"}, {"name": "<PERSON>", "team": "PHX", "position": "G", "points": 26, "rebounds": 4, "assists": 5, "efficiency": 29, "usage_rate": "26%", "true_shooting": "61%", "plus_minus": 0, "minutes": 35, "player_impact": "8.7"}, {"name": "<PERSON>", "team": "PHX", "position": "G", "points": 26, "rebounds": 4, "assists": 5, "efficiency": 29, "usage_rate": "33%", "true_shooting": "61%", "plus_minus": 0, "minutes": 35, "player_impact": "8.7"}, {"name": "<PERSON><PERSON>", "team": "SEA", "position": "G", "points": 21, "rebounds": 5, "assists": 4, "efficiency": 24, "usage_rate": "34%", "true_shooting": "50%", "plus_minus": 0, "minutes": 33, "player_impact": "7.2"}, {"name": "<PERSON><PERSON>", "team": "SEA", "position": "G", "points": 21, "rebounds": 5, "assists": 4, "efficiency": 24, "usage_rate": "27%", "true_shooting": "50%", "plus_minus": 0, "minutes": 33, "player_impact": "7.2"}, {"name": "Kahleah Copper", "team": "PHX", "position": "G", "points": 17, "rebounds": 6, "assists": 3, "efficiency": 20, "usage_rate": "35%", "true_shooting": "46%", "plus_minus": 0, "minutes": 29, "player_impact": "6.0"}, {"name": "Kahleah Copper", "team": "PHX", "position": "G", "points": 17, "rebounds": 6, "assists": 3, "efficiency": 20, "usage_rate": "26%", "true_shooting": "46%", "plus_minus": 0, "minutes": 29, "player_impact": "6.0"}], "model_performance": {"Points Prediction MAE": "1.8", "Rebounds Prediction MAE": "1.6", "Assists Prediction MAE": "1.5", "Win Probability Accuracy": "84%", "Injury Risk AUC": "0.891", "Shot Prediction Accuracy": "82%", "Player Load Model R²": "0.871", "Lineup Optimization Score": "87%"}, "injury_report": [{"player": "<PERSON>", "team": "WAS", "status": "OUT", "injury": "Back", "impact": "9.5"}, {"player": "<PERSON>", "team": "LVA", "status": "ACTIVE", "injury": "<PERSON><PERSON>", "impact": "3.1"}, {"player": "<PERSON>", "team": "PHX", "status": "GTD", "injury": "<PERSON><PERSON><PERSON>", "impact": "8.2"}], "season_trends": {"pace": 95, "offensive_rating": 109, "defensive_rating": 106, "three_point_rate": "37%", "player_load": "+10%", "injury_rate": "8.8%", "scoring_variance": "22.7", "competitive_balance": "0.74"}, "advanced_metrics": {"Player Impact Estimate": "24.7", "Usage Rate": "23%", "True Shooting %": "64%", "Net Rating": "+15.0", "Contested Shot %": "47%", "Defensive Win Shares": "3.4", "Box Plus/Minus": "5.0", "Value Over Replacement": "2.8"}, "optimal_lineups": {"Most Efficient": "Lineup A (+22.2 Net Rating)", "Best Net Rating": "+27.2", "Best Defensive": "Lineup B (97.9 Def Rating)", "Best Closing": "Lineup C (78% Win Rate)", "Highest Pace": "Lineup D (115 Pace)", "Most Balanced": "Lineup E (0.95 Balance Score)"}, "play_types": {"Transition": "1.13 PPP", "Pick and Roll": "1.00 PPP", "Post Up": "0.86 PPP", "Isolation": "0.96 PPP", "Spot Up": "1.18 PPP", "Cut": "1.32 PPP", "Handoff": "0.97 PPP", "Putback": "1.41 PPP"}, "team_colors": {"ATL": {"primary": "#E03A3E", "secondary": "#C1D32F"}, "CHI": {"primary": "#418FDE", "secondary": "#FFCD00"}, "CON": {"primary": "#A6192E", "secondary": "#7A9A01"}, "DAL": {"primary": "#00A9E0", "secondary": "#C4D600"}, "IND": {"primary": "#FDBB30", "secondary": "#002D62"}, "LVA": {"primary": "#C8102E", "secondary": "#BEC0C2"}, "LAS": {"primary": "#702F8A", "secondary": "#FFC72C"}, "MIN": {"primary": "#78BE20", "secondary": "#236192"}, "NYL": {"primary": "#FF671F", "secondary": "#6ECEB2"}, "PHX": {"primary": "#201747", "secondary": "#E56020"}, "SEA": {"primary": "#2C5234", "secondary": "#F0F1F2"}, "WAS": {"primary": "#002B5C", "secondary": "#E31837"}, "GSW": {"primary": "#1D428A", "secondary": "#FFC72C"}}, "federated_multiverse_metrics": {"federation_status": {"active_teams": "9/13", "current_round": "Round 4", "global_mae": "2.34", "convergence": "Converging", "privacy_budget": "1.0"}, "multiverse_ensemble": {"total_models": "9", "ensemble_mae": "2.52", "model_agreement": "0.94", "prediction_confidence": "0.89", "specialization_score": "0.87"}, "expert_integration": {"player_mappings": "465", "team_mappings": "13", "role_categories": "3", "context_scenarios": "5", "coverage_rate": "100%"}}, "team_federation_status": {"ATL": {"status": "Inactive", "local_mae": "N/A", "samples_trained": "0", "participation_rate": "0%", "last_update": "9 hrs ago", "data_quality": "N/A", "expert_coverage": "N/A"}, "CHI": {"status": "Inactive", "local_mae": "N/A", "samples_trained": "0", "participation_rate": "0%", "last_update": "18 hrs ago", "data_quality": "N/A", "expert_coverage": "N/A"}, "CON": {"status": "Active", "local_mae": "3.33", "samples_trained": "1,452", "participation_rate": "86.4%", "last_update": "13 min ago", "data_quality": "96.3%", "expert_coverage": "90.2%"}, "DAL": {"status": "Active", "local_mae": "2.24", "samples_trained": "1,976", "participation_rate": "93.6%", "last_update": "13 min ago", "data_quality": "87.3%", "expert_coverage": "93.4%"}, "GSV": {"status": "Inactive", "local_mae": "N/A", "samples_trained": "0", "participation_rate": "0%", "last_update": "22 hrs ago", "data_quality": "N/A", "expert_coverage": "N/A"}, "IND": {"status": "Inactive", "local_mae": "N/A", "samples_trained": "0", "participation_rate": "0%", "last_update": "6 hrs ago", "data_quality": "N/A", "expert_coverage": "N/A"}, "LAS": {"status": "Active", "local_mae": "3.09", "samples_trained": "1,483", "participation_rate": "77.1%", "last_update": "9 min ago", "data_quality": "92.3%", "expert_coverage": "93.8%"}, "LV": {"status": "Active", "local_mae": "4.37", "samples_trained": "1,535", "participation_rate": "79.2%", "last_update": "9 min ago", "data_quality": "86.6%", "expert_coverage": "92.6%"}, "MIN": {"status": "Active", "local_mae": "3.60", "samples_trained": "1,946", "participation_rate": "82.1%", "last_update": "4 min ago", "data_quality": "99.2%", "expert_coverage": "90.3%"}, "NYL": {"status": "Inactive", "local_mae": "N/A", "samples_trained": "0", "participation_rate": "0%", "last_update": "20 hrs ago", "data_quality": "N/A", "expert_coverage": "N/A"}, "PHO": {"status": "Inactive", "local_mae": "N/A", "samples_trained": "0", "participation_rate": "0%", "last_update": "14 hrs ago", "data_quality": "N/A", "expert_coverage": "N/A"}, "SEA": {"status": "Active", "local_mae": "3.50", "samples_trained": "2,782", "participation_rate": "87.9%", "last_update": "14 min ago", "data_quality": "92.5%", "expert_coverage": "93.7%"}, "WAS": {"status": "Active", "local_mae": "2.25", "samples_trained": "2,816", "participation_rate": "87.5%", "last_update": "4 min ago", "data_quality": "88.9%", "expert_coverage": "95.6%"}}, "multiverse_model_performance": {"PossessionBasedModel": {"mae": "3.13", "weight": "0.175", "accuracy_trend": "Improving", "specialization_score": "0.71", "contribution": "14.6%", "training_rounds": "37"}, "HighLeverageModel": {"mae": "2.53", "weight": "0.056", "accuracy_trend": "Improving", "specialization_score": "0.94", "contribution": "8.5%", "training_rounds": "39"}, "TeamDynamicsModel": {"mae": "4.51", "weight": "0.161", "accuracy_trend": "Improving", "specialization_score": "0.92", "contribution": "12.0%", "training_rounds": "23"}, "ContextualPerformanceModel": {"mae": "3.97", "weight": "0.071", "accuracy_trend": "Declining", "specialization_score": "0.87", "contribution": "17.4%", "training_rounds": "43"}, "CumulativeFatigueModel": {"mae": "4.47", "weight": "0.115", "accuracy_trend": "Improving", "specialization_score": "0.83", "contribution": "11.2%", "training_rounds": "48"}, "InjuryImpactModel": {"mae": "2.57", "weight": "0.161", "accuracy_trend": "Declining", "specialization_score": "0.92", "contribution": "5.9%", "training_rounds": "25"}, "CoachingStyleModel": {"mae": "3.92", "weight": "0.177", "accuracy_trend": "Declining", "specialization_score": "0.87", "contribution": "8.2%", "training_rounds": "46"}, "ArenaEffectModel": {"mae": "4.06", "weight": "0.111", "accuracy_trend": "Stable", "specialization_score": "0.77", "contribution": "12.0%", "training_rounds": "20"}, "WeatherImpactModel": {"mae": "3.81", "weight": "0.062", "accuracy_trend": "Stable", "specialization_score": "0.83", "contribution": "19.7%", "training_rounds": "17"}}, "line_movement_alerts": [], "autopilot_proposals": [{"proposal_id": "PROP_001", "type": "Feature Engineering", "description": "Optimize fatigue modeling", "expected_improvement": "0.36 MAE reduction", "confidence": "0.64", "implementation_time": "114 minutes", "status": "In Progress", "priority": "High"}, {"proposal_id": "PROP_002", "type": "Feature Engineering", "description": "Optimize injury prediction", "expected_improvement": "0.45 MAE reduction", "confidence": "0.79", "implementation_time": "53 minutes", "status": "In Progress", "priority": "High"}], "drift_analysis": {"overall_drift_score": "0.047", "drift_threshold": "0.100", "teams_with_drift": [{"team": "ATL", "drift_score": "0.163", "severity": "Medium"}, {"team": "WAS", "drift_score": "0.206", "severity": "High"}], "feature_drift_summary": {"minutes": {"drift_score": "0.048", "status": "Stable"}, "field_goals": {"drift_score": "0.093", "status": "Minor Drift"}, "three_pointers": {"drift_score": "0.021", "status": "Significant Drift"}, "free_throws": {"drift_score": "0.067", "status": "Minor Drift"}, "rebounds": {"drift_score": "0.123", "status": "Significant Drift"}, "assists": {"drift_score": "0.016", "status": "Minor Drift"}}, "temporal_drift": {"detected": "True", "severity": "Low", "affected_features": 7}}, "hybrid_performance": {"model_performance": {"centralized": {"mae": "3.27", "predictions": 114, "weight": "0.30"}, "federated": {"mae": "2.15", "predictions": 141, "weight": "0.53"}, "multiverse": {"mae": "3.12", "predictions": 115, "weight": "0.18"}, "hybrid": {"mae": "2.39", "predictions": 166, "improvement": "0.76"}}, "weight_adjustments_today": 3, "best_performer": "Hybrid", "performance_trend": "Improving", "confidence_score": "0.85"}, "accuracy_trends": {"accuracy_history": [{"date": "2025-07-12", "mae": "2.10", "predictions": 185, "accuracy_score": "0.48"}, {"date": "2025-07-11", "mae": "2.73", "predictions": 81, "accuracy_score": "0.32"}, {"date": "2025-07-10", "mae": "2.14", "predictions": 107, "accuracy_score": "0.47"}, {"date": "2025-07-09", "mae": "2.37", "predictions": 51, "accuracy_score": "0.41"}, {"date": "2025-07-08", "mae": "2.46", "predictions": 191, "accuracy_score": "0.38"}, {"date": "2025-07-07", "mae": "2.34", "predictions": 50, "accuracy_score": "0.41"}, {"date": "2025-07-06", "mae": "2.56", "predictions": 139, "accuracy_score": "0.36"}, {"date": "2025-07-05", "mae": "2.04", "predictions": 147, "accuracy_score": "0.49"}, {"date": "2025-07-04", "mae": "2.27", "predictions": 61, "accuracy_score": "0.43"}, {"date": "2025-07-03", "mae": "2.58", "predictions": 53, "accuracy_score": "0.35"}, {"date": "2025-07-02", "mae": "2.93", "predictions": 121, "accuracy_score": "0.27"}, {"date": "2025-07-01", "mae": "2.32", "predictions": 118, "accuracy_score": "0.42"}, {"date": "2025-06-30", "mae": "2.66", "predictions": 75, "accuracy_score": "0.34"}, {"date": "2025-06-29", "mae": "2.04", "predictions": 150, "accuracy_score": "0.49"}, {"date": "2025-06-28", "mae": "2.55", "predictions": 130, "accuracy_score": "0.36"}, {"date": "2025-06-27", "mae": "2.42", "predictions": 86, "accuracy_score": "0.39"}, {"date": "2025-06-26", "mae": "2.39", "predictions": 127, "accuracy_score": "0.40"}, {"date": "2025-06-25", "mae": "2.51", "predictions": 189, "accuracy_score": "0.37"}, {"date": "2025-06-24", "mae": "2.23", "predictions": 119, "accuracy_score": "0.44"}, {"date": "2025-06-23", "mae": "2.04", "predictions": 193, "accuracy_score": "0.49"}, {"date": "2025-06-22", "mae": "2.52", "predictions": 52, "accuracy_score": "0.37"}, {"date": "2025-06-21", "mae": "2.02", "predictions": 65, "accuracy_score": "0.50"}, {"date": "2025-06-20", "mae": "2.71", "predictions": 103, "accuracy_score": "0.32"}, {"date": "2025-06-19", "mae": "2.55", "predictions": 90, "accuracy_score": "0.36"}, {"date": "2025-06-18", "mae": "2.17", "predictions": 123, "accuracy_score": "0.46"}, {"date": "2025-06-17", "mae": "2.60", "predictions": 116, "accuracy_score": "0.35"}, {"date": "2025-06-16", "mae": "2.52", "predictions": 92, "accuracy_score": "0.37"}, {"date": "2025-06-15", "mae": "1.89", "predictions": 144, "accuracy_score": "0.53"}, {"date": "2025-06-14", "mae": "2.61", "predictions": 68, "accuracy_score": "0.35"}, {"date": "2025-06-13", "mae": "2.34", "predictions": 128, "accuracy_score": "0.42"}], "current_mae": "2.39", "trend": "Improving", "trend_change": "-0.8%", "best_day_mae": "1.89", "worst_day_mae": "2.93", "total_predictions": 3354, "avg_daily_predictions": "112"}}