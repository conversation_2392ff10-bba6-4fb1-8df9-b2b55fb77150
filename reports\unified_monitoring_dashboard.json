{"timestamp": "2025-07-12T21:58:26.178958", "overall_status": "warning", "overall_health_score": 77.9, "systems": {"data_collection": {"status": "healthy", "health_score": 100, "last_update": "2025-07-11T15:56:45.923585", "alerts": ["No collection in 30.0 hours", "Log not updated in 30.7 hours"], "metrics": {"collector": "Available", "collections_7d": 2, "last_date": "2025-07-11", "last_time": "2025-07-11T15:56", "records": 0, "new_games": 0, "total_games": 128, "games_7d": 128, "api_requests_today": 0, "endpoints_processed": 0, "log_size_kb": 0.17, "log_last_modified": "2025-07-11 15:16:10", "ai_anomaly_score": 22.3}}, "federated_learning": {"status": "healthy", "health_score": 100.0, "last_update": "2025-07-11T14:57:24.147475", "alerts": [], "metrics": {"teams_monitored": 13, "teams_with_drift": 0, "fairness_violations": 0, "schema_status": "valid", "config_status": "compliant", "ai_anomaly_score": 3.9}}, "federated_multiverse": {"status": "WARNING", "health_score": 80.0, "last_update": "2025-07-12T21:58:24.357974", "alerts": ["Expert mapping integration issue: No module named 'expert_multiverse_integration'...", "High global MAE: 3.80"], "metrics": {"integration_file": "Available", "team_scripts": 13, "master_launcher": "Available", "active_teams": 12, "total_teams": 13, "participation_rate": 0.92, "current_round": 38, "global_mae": 3.8, "convergence_status": "converging", "multiverse_models": 9, "privacy_budget": 1.0, "expert_integration": "Error", "ai_anomaly_score": 21.3}}, "data_infrastructure": {"status": "healthy", "health_score": 100, "last_update": "2025-07-12T21:58:26.075786", "alerts": [], "metrics": {"dataset_size_mb": 88.53, "columns": 642, "total_rows": "29,237", "data_completeness": "642 columns, 29,237 rows", "federated_teams": 13, "federated_coverage": "13/13 teams", "team_isolated_files": 39, "team_splits_status": "39/39 files (13 teams × 3 splits)", "ai_anomaly_score": 3.7}}, "model_system": {"status": "warning", "health_score": 70, "last_update": "2025-07-12T21:58:26.080809", "alerts": ["Missing model directory: model1_real_data", "Missing model source: player_points_model.py"], "metrics": {"model_directories": 26, "model_source_files": 3, "ai_anomaly_score": 32.9}}, "backup_system": {"status": "healthy", "health_score": 100, "last_update": "2025-07-12T21:58:26.088471", "alerts": [], "metrics": {"backup_files": 1, "latest_backup_age_hours": 30.7, "latest_backup_file": "master_dataset_backup_2025-07-11.csv", "total_backup_size_mb": 88.53, "model_backup_dirs": 0, "ai_anomaly_score": 3.4}}, "api_health": {"status": "healthy", "health_score": 100, "last_update": "2025-07-12T21:58:26.090655", "alerts": [], "metrics": {"api_requests_today": 0, "api_credits_remaining": 500, "last_api_request": "2025-07-11T17:10:00.000000", "config_files_available": 2, "config_files": ["unified_collector_config.json", "wnba_2025_season_config.json"], "api_log_files": 0, "ai_anomaly_score": 5.4}}, "system_resources": {"status": "healthy", "health_score": 100, "last_update": "2025-07-12T21:58:26.161666", "alerts": [], "metrics": {"data_size_mb": 575.19, "models_size_mb": 367.66, "logs_size_mb": 0.03, "backups_size_mb": 95.92, "reports_size_mb": 0.06, "total_disk_usage_mb": 1038.86, "total_disk_usage_gb": 1.01, "log_files": 2, "log_file_sizes": ["unified_monitoring.log: 1.16KB", "unified_wnba_collector.log: 0.17KB"], "ai_anomaly_score": 7.8}}, "configuration": {"status": "healthy", "health_score": 100, "last_update": "2025-07-12T21:58:26.165021", "alerts": [], "metrics": {"available_configs": 4, "missing_configs": 0, "config_files_list": ["unified_collector_config.json", "wnba_2025_season_config.json", "federated_config.json", "clean_features_list.json"], "python_configs": 2, "requirements_files": 2, "environment_variables": 0, "ai_anomaly_score": 0}}, "player_tracking": {"status": "healthy", "health_score": 92, "last_update": "2025-07-12T21:58:26.165395", "alerts": [], "metrics": {"Players Tracked": "98%", "Accuracy": "97.1%", "Latency": "90ms", "Court Coverage": "100%", "Data Points/Sec": "3,140", "ai_anomaly_score": 8.6}}, "shot_analysis": {"status": "warning", "health_score": 89, "last_update": "2025-07-12T21:58:26.165473", "alerts": ["Release point calibration needed", "Make prediction model drift detected"], "metrics": {"Shot Detection": "99.1%", "Arc Analysis": "98.1%", "Release Time": "0.39s", "Make Prediction": "81%", "Shots Analyzed": "1,358", "ai_anomaly_score": 20.3}}, "injury_prediction": {"status": "healthy", "health_score": 91, "last_update": "2025-07-12T21:58:26.165515", "alerts": [], "metrics": {"Accuracy": "89%", "Prevented Injuries": "15", "Risk Alerts": "5", "Load Monitoring": "99%", "Recovery Tracking": "96%", "ai_anomaly_score": 7.3}}, "game_simulation": {"status": "healthy", "health_score": 92, "last_update": "2025-07-12T21:58:26.165552", "alerts": [], "metrics": {"Accuracy": "83.3%", "Sim Speed": "296x realtime", "Scenarios Run": "1,220", "Win Prob Accuracy": "82%", "Model Confidence": "90%", "ai_anomaly_score": 9.7}}, "betting_optimization": {"status": "healthy", "health_score": 97, "last_update": "2025-07-12T21:58:26.165587", "alerts": [], "metrics": {"Line Accuracy": "81.2%", "Market Coverage": "95%", "Update Frequency": "20s", "Profit Margin": "4.6%", "Risk Assessment": "98%", "ai_anomaly_score": 0}}, "performance_forecasting": {"status": "warning", "health_score": 88, "last_update": "2025-07-12T21:58:26.165658", "alerts": ["System operating normally"], "metrics": {"Player Predictions": "81.0%", "Team Predictions": "85.8%", "Seasonal Trends": "91%", "Lineup Optimization": "88%", "Model Drift": "4.6%", "ai_anomaly_score": 16.1}}, "opponent_scouting": {"status": "healthy", "health_score": 94, "last_update": "2025-07-12T21:58:26.165693", "alerts": [], "metrics": {"Play Recognition": "92.5%", "Tendency Analysis": "94%", "Weakness Detection": "88%", "Strategy Effectiveness": "86%", "Report Generation": "98%", "ai_anomaly_score": 7.1}}, "fan_engagement": {"status": "healthy", "health_score": 97, "last_update": "2025-07-12T21:58:26.165726", "alerts": [], "metrics": {"Real-time Updates": "99%", "Personalization": "87%", "Content Delivery": "96%", "User Satisfaction": "4.7/5", "Engagement Rate": "72%", "ai_anomaly_score": 2.3}}, "referee_analytics": {"status": "healthy", "health_score": 98, "last_update": "2025-07-12T21:58:26.165791", "alerts": [], "metrics": {"Call Accuracy": "96.8%", "Consistency Score": "92%", "Game Flow Impact": "3.4", "Bias Detection": "97%", "Performance Rating": "8.5/10", "ai_anomaly_score": 4.9}}, "player_load_monitoring": {"status": "healthy", "health_score": 97, "last_update": "2025-07-12T21:58:26.165825", "alerts": [], "metrics": {"Load Index": "85%", "Fatigue Risk": "Low", "Peak Load": "91%", "Recovery": "Good", "Injury Risk": "10.5%", "ai_anomaly_score": 1.0}}, "line_movement_watchdog": {"status": "ERROR", "health_score": 0.0, "last_update": "2025-07-12T21:58:26.168304", "alerts": ["Status check failed: 'charmap' codec can't decode byte 0x90 in position 3479: character maps to <undefined>"], "metrics": {"ai_anomaly_score": 50.5}}, "medusa_autopilot": {"status": "ERROR", "health_score": 0.0, "last_update": "2025-07-12T21:58:26.172022", "alerts": ["Status check failed: 'charmap' codec can't decode byte 0x90 in position 3479: character maps to <undefined>"], "metrics": {"ai_anomaly_score": 50.3}}, "drift_detection": {"status": "ERROR", "health_score": 0.0, "last_update": "2025-07-12T21:58:26.174913", "alerts": ["Status check failed: 'charmap' codec can't decode byte 0x81 in position 1868: character maps to <undefined>"], "metrics": {"ai_anomaly_score": 51.7}}, "hybrid_prediction_system": {"status": "ERROR", "health_score": 0.0, "last_update": "2025-07-12T21:58:26.177180", "alerts": ["Status check failed: 'charmap' codec can't decode byte 0x9d in position 3588: character maps to <undefined>"], "metrics": {"ai_anomaly_score": 47.8}}, "accuracy_tracking": {"status": "WARNING", "health_score": 85.0, "last_update": "2025-07-12T21:58:26.178308", "alerts": ["Accuracy tracking database not found"], "metrics": {"tracking_models": ["Centralized", "Federated", "Multiverse", "Hybrid"], "accuracy_trend": "Declining", "last_accuracy_update": "16 min ago", "prediction_confidence": "0.89", "model_agreement_rate": "0.77", "ai_anomaly_score": 11.1}}}, "summary": {"total_systems": 16, "healthy_systems": 16, "warning_systems": 0, "critical_systems": 0, "offline_systems": 0, "total_alerts": 1, "games_today": 6, "predictions_accuracy": "79%", "player_coverage": "98.7%", "models_active": 15, "data_streams": 29}, "alerts": ["Data Collection: No collection in 30.0 hours", "Data Collection: Log not updated in 30.7 hours", "Federated Multiverse: Expert mapping integration issue: No module named 'expert_multiverse_integration'...", "Federated Multiverse: High global MAE: 3.80", "Model System: Missing model directory: model1_real_data", "Model System: Missing model source: player_points_model.py", "Shot Analysis: Release point calibration needed", "Shot Analysis: Make prediction model drift detected", "Performance Forecasting: System operating normally", "Line Movement Watchdog: Status check failed: 'charmap' codec can't decode byte 0x90 in position 3479: character maps to <undefined>", "Medusa Autopilot: Status check failed: 'charmap' codec can't decode byte 0x90 in position 3479: character maps to <undefined>", "Drift Detection: Status check failed: 'charmap' codec can't decode byte 0x81 in position 1868: character maps to <undefined>", "Hybrid Prediction System: Status check failed: 'charmap' codec can't decode byte 0x9d in position 3588: character maps to <undefined>", "Accuracy Tracking: Accuracy tracking database not found"], "recommendations": ["System health below 80% - investigate critical issues", "Warning systems need monitoring: model_system, shot_analysis, performance_forecasting"], "live_games": [{"id": "**********", "matchup": "MIN @ CHI", "home_team": "CHI", "away_team": "MIN", "home_score": 0, "away_score": 0, "quarter": "Final", "time_remaining": "0:00", "game_status": "Final", "possession": "MIN", "win_probability": {"home": 50, "away": 50}, "pace": 100, "lead_changes": 0, "largest_lead": 0}, {"id": "**********", "matchup": "GSV @ LVA", "home_team": "LVA", "away_team": "GSV", "home_score": 0, "away_score": 0, "quarter": "Final", "time_remaining": "0:00", "game_status": "Final", "possession": "GSV", "win_probability": {"home": 50, "away": 50}, "pace": 100, "lead_changes": 0, "largest_lead": 0}], "top_performers": [{"name": "<PERSON><PERSON><PERSON>", "team": "SEA", "position": "F", "points": 28, "rebounds": 12, "assists": 6, "efficiency": 35, "usage_rate": "31%", "true_shooting": "59%", "plus_minus": 0, "minutes": 38, "player_impact": "10.5"}, {"name": "<PERSON><PERSON><PERSON>", "team": "SEA", "position": "F", "points": 28, "rebounds": 12, "assists": 6, "efficiency": 35, "usage_rate": "24%", "true_shooting": "59%", "plus_minus": 0, "minutes": 38, "player_impact": "10.5"}, {"name": "<PERSON>", "team": "PHX", "position": "G", "points": 26, "rebounds": 4, "assists": 5, "efficiency": 29, "usage_rate": "33%", "true_shooting": "61%", "plus_minus": 0, "minutes": 35, "player_impact": "8.7"}, {"name": "<PERSON>", "team": "PHX", "position": "G", "points": 26, "rebounds": 4, "assists": 5, "efficiency": 29, "usage_rate": "27%", "true_shooting": "61%", "plus_minus": 0, "minutes": 35, "player_impact": "8.7"}, {"name": "<PERSON><PERSON>", "team": "SEA", "position": "G", "points": 21, "rebounds": 5, "assists": 4, "efficiency": 24, "usage_rate": "34%", "true_shooting": "50%", "plus_minus": 0, "minutes": 33, "player_impact": "7.2"}, {"name": "<PERSON><PERSON>", "team": "SEA", "position": "G", "points": 21, "rebounds": 5, "assists": 4, "efficiency": 24, "usage_rate": "32%", "true_shooting": "50%", "plus_minus": 0, "minutes": 33, "player_impact": "7.2"}, {"name": "Kahleah Copper", "team": "PHX", "position": "G", "points": 17, "rebounds": 6, "assists": 3, "efficiency": 20, "usage_rate": "31%", "true_shooting": "46%", "plus_minus": 0, "minutes": 29, "player_impact": "6.0"}, {"name": "Kahleah Copper", "team": "PHX", "position": "G", "points": 17, "rebounds": 6, "assists": 3, "efficiency": 20, "usage_rate": "24%", "true_shooting": "46%", "plus_minus": 0, "minutes": 29, "player_impact": "6.0"}], "model_performance": {"Points Prediction MAE": "2.2", "Rebounds Prediction MAE": "1.2", "Assists Prediction MAE": "1.6", "Win Probability Accuracy": "83%", "Injury Risk AUC": "0.899", "Shot Prediction Accuracy": "75%", "Player Load Model R²": "0.816", "Lineup Optimization Score": "86%"}, "injury_report": [{"player": "<PERSON>", "team": "LVA", "status": "ACTIVE", "injury": "<PERSON><PERSON>", "impact": "3.1"}, {"player": "<PERSON>", "team": "WAS", "status": "OUT", "injury": "Back", "impact": "9.5"}, {"player": "<PERSON>", "team": "SEA", "status": "LOAD_MGMT", "injury": "Knee", "impact": "6.8"}], "season_trends": {"pace": 98, "offensive_rating": 109, "defensive_rating": 103, "three_point_rate": "37%", "player_load": "+12%", "injury_rate": "9.8%", "scoring_variance": "19.6", "competitive_balance": "0.79"}, "advanced_metrics": {"Player Impact Estimate": "21.1", "Usage Rate": "31%", "True Shooting %": "60%", "Net Rating": "+15.4", "Contested Shot %": "35%", "Defensive Win Shares": "4.3", "Box Plus/Minus": "5.4", "Value Over Replacement": "2.8"}, "optimal_lineups": {"Most Efficient": "Lineup A (+19.5 Net Rating)", "Best Net Rating": "+27.3", "Best Defensive": "Lineup B (93.3 Def Rating)", "Best Closing": "Lineup C (85% Win Rate)", "Highest Pace": "Lineup D (115 Pace)", "Most Balanced": "Lineup E (0.85 Balance Score)"}, "play_types": {"Transition": "1.16 PPP", "Pick and Roll": "0.92 PPP", "Post Up": "0.96 PPP", "Isolation": "0.93 PPP", "Spot Up": "1.03 PPP", "Cut": "1.25 PPP", "Handoff": "1.06 PPP", "Putback": "1.42 PPP"}, "team_colors": {"ATL": {"primary": "#E03A3E", "secondary": "#C1D32F"}, "CHI": {"primary": "#418FDE", "secondary": "#FFCD00"}, "CON": {"primary": "#A6192E", "secondary": "#7A9A01"}, "DAL": {"primary": "#00A9E0", "secondary": "#C4D600"}, "IND": {"primary": "#FDBB30", "secondary": "#002D62"}, "LVA": {"primary": "#C8102E", "secondary": "#BEC0C2"}, "LAS": {"primary": "#702F8A", "secondary": "#FFC72C"}, "MIN": {"primary": "#78BE20", "secondary": "#236192"}, "NYL": {"primary": "#FF671F", "secondary": "#6ECEB2"}, "PHX": {"primary": "#201747", "secondary": "#E56020"}, "SEA": {"primary": "#2C5234", "secondary": "#F0F1F2"}, "WAS": {"primary": "#002B5C", "secondary": "#E31837"}, "GSW": {"primary": "#1D428A", "secondary": "#FFC72C"}}, "federated_multiverse_metrics": {"federation_status": {"active_teams": "11/13", "current_round": "Round 3", "global_mae": "2.62", "convergence": "Stable", "privacy_budget": "1.0"}, "multiverse_ensemble": {"total_models": "9", "ensemble_mae": "3.07", "model_agreement": "0.92", "prediction_confidence": "0.89", "specialization_score": "0.71"}, "expert_integration": {"player_mappings": "465", "team_mappings": "13", "role_categories": "3", "context_scenarios": "5", "coverage_rate": "100%"}}, "team_federation_status": {"ATL": {"status": "Active", "local_mae": "4.19", "samples_trained": "2,206", "participation_rate": "96.1%", "last_update": "1 min ago", "data_quality": "88.2%", "expert_coverage": "99.4%"}, "CHI": {"status": "Active", "local_mae": "2.19", "samples_trained": "2,704", "participation_rate": "71.3%", "last_update": "11 min ago", "data_quality": "87.8%", "expert_coverage": "94.6%"}, "CON": {"status": "Active", "local_mae": "3.73", "samples_trained": "2,085", "participation_rate": "93.2%", "last_update": "10 min ago", "data_quality": "95.6%", "expert_coverage": "95.9%"}, "DAL": {"status": "Active", "local_mae": "4.31", "samples_trained": "2,824", "participation_rate": "77.6%", "last_update": "11 min ago", "data_quality": "91.4%", "expert_coverage": "95.4%"}, "GSV": {"status": "Active", "local_mae": "4.03", "samples_trained": "1,074", "participation_rate": "88.1%", "last_update": "13 min ago", "data_quality": "94.6%", "expert_coverage": "97.1%"}, "IND": {"status": "Active", "local_mae": "2.57", "samples_trained": "1,304", "participation_rate": "72.7%", "last_update": "6 min ago", "data_quality": "98.1%", "expert_coverage": "91.8%"}, "LAS": {"status": "Inactive", "local_mae": "N/A", "samples_trained": "0", "participation_rate": "0%", "last_update": "21 hrs ago", "data_quality": "N/A", "expert_coverage": "N/A"}, "LV": {"status": "Active", "local_mae": "3.68", "samples_trained": "2,308", "participation_rate": "93.4%", "last_update": "9 min ago", "data_quality": "93.6%", "expert_coverage": "91.7%"}, "MIN": {"status": "Active", "local_mae": "3.16", "samples_trained": "2,812", "participation_rate": "77.9%", "last_update": "5 min ago", "data_quality": "98.1%", "expert_coverage": "98.1%"}, "NYL": {"status": "Active", "local_mae": "4.14", "samples_trained": "2,075", "participation_rate": "79.6%", "last_update": "1 min ago", "data_quality": "89.5%", "expert_coverage": "95.5%"}, "PHO": {"status": "Active", "local_mae": "3.95", "samples_trained": "2,028", "participation_rate": "89.9%", "last_update": "14 min ago", "data_quality": "97.3%", "expert_coverage": "91.2%"}, "SEA": {"status": "Inactive", "local_mae": "N/A", "samples_trained": "0", "participation_rate": "0%", "last_update": "22 hrs ago", "data_quality": "N/A", "expert_coverage": "N/A"}, "WAS": {"status": "Inactive", "local_mae": "N/A", "samples_trained": "0", "participation_rate": "0%", "last_update": "9 hrs ago", "data_quality": "N/A", "expert_coverage": "N/A"}}, "multiverse_model_performance": {"PossessionBasedModel": {"mae": "3.42", "weight": "0.128", "accuracy_trend": "Declining", "specialization_score": "0.88", "contribution": "6.1%", "training_rounds": "36"}, "HighLeverageModel": {"mae": "4.65", "weight": "0.066", "accuracy_trend": "Declining", "specialization_score": "0.90", "contribution": "14.1%", "training_rounds": "44"}, "TeamDynamicsModel": {"mae": "3.15", "weight": "0.105", "accuracy_trend": "Improving", "specialization_score": "0.63", "contribution": "18.4%", "training_rounds": "46"}, "ContextualPerformanceModel": {"mae": "2.58", "weight": "0.142", "accuracy_trend": "Stable", "specialization_score": "0.60", "contribution": "14.5%", "training_rounds": "41"}, "CumulativeFatigueModel": {"mae": "2.56", "weight": "0.104", "accuracy_trend": "Improving", "specialization_score": "0.83", "contribution": "14.8%", "training_rounds": "35"}, "InjuryImpactModel": {"mae": "3.90", "weight": "0.156", "accuracy_trend": "Stable", "specialization_score": "0.72", "contribution": "6.8%", "training_rounds": "44"}, "CoachingStyleModel": {"mae": "3.88", "weight": "0.077", "accuracy_trend": "Improving", "specialization_score": "0.62", "contribution": "5.7%", "training_rounds": "37"}, "ArenaEffectModel": {"mae": "3.04", "weight": "0.096", "accuracy_trend": "Declining", "specialization_score": "0.88", "contribution": "17.9%", "training_rounds": "32"}, "WeatherImpactModel": {"mae": "3.74", "weight": "0.164", "accuracy_trend": "Declining", "specialization_score": "0.74", "contribution": "15.0%", "training_rounds": "48"}}, "line_movement_alerts": [], "autopilot_proposals": [{"proposal_id": "PROP_001", "type": "Architecture Adjustment", "description": "Optimize matchup analysis", "expected_improvement": "0.26 MAE reduction", "confidence": "0.64", "implementation_time": "56 minutes", "status": "Pending", "priority": "Medium"}, {"proposal_id": "PROP_002", "type": "Hyperparameter Tuning", "description": "Optimize player role detection", "expected_improvement": "0.35 MAE reduction", "confidence": "0.66", "implementation_time": "58 minutes", "status": "In Progress", "priority": "Medium"}, {"proposal_id": "PROP_003", "type": "Architecture Adjustment", "description": "Optimize matchup analysis", "expected_improvement": "0.40 MAE reduction", "confidence": "0.86", "implementation_time": "91 minutes", "status": "Ready", "priority": "High"}, {"proposal_id": "PROP_004", "type": "Training Optimization", "description": "Optimize injury prediction", "expected_improvement": "0.18 MAE reduction", "confidence": "0.64", "implementation_time": "27 minutes", "status": "Pending", "priority": "Medium"}, {"proposal_id": "PROP_005", "type": "Hyperparameter Tuning", "description": "Optimize player role detection", "expected_improvement": "0.26 MAE reduction", "confidence": "0.85", "implementation_time": "39 minutes", "status": "In Progress", "priority": "Low"}], "drift_analysis": {"overall_drift_score": "0.105", "drift_threshold": "0.100", "teams_with_drift": [{"team": "ATL", "drift_score": "0.252", "severity": "High"}, {"team": "DAL", "drift_score": "0.297", "severity": "High"}, {"team": "IND", "drift_score": "0.170", "severity": "Medium"}], "feature_drift_summary": {"minutes": {"drift_score": "0.130", "status": "Stable"}, "field_goals": {"drift_score": "0.060", "status": "Minor Drift"}, "three_pointers": {"drift_score": "0.078", "status": "Minor Drift"}, "free_throws": {"drift_score": "0.018", "status": "Minor Drift"}, "rebounds": {"drift_score": "0.125", "status": "Stable"}, "assists": {"drift_score": "0.145", "status": "Significant Drift"}}, "temporal_drift": {"detected": "True", "severity": "High", "affected_features": 5}}, "hybrid_performance": {"model_performance": {"centralized": {"mae": "2.98", "predictions": 128, "weight": "0.37"}, "federated": {"mae": "2.18", "predictions": 111, "weight": "0.58"}, "multiverse": {"mae": "3.23", "predictions": 83, "weight": "0.28"}, "hybrid": {"mae": "2.68", "predictions": 175, "improvement": "0.38"}}, "weight_adjustments_today": 11, "best_performer": "Federated", "performance_trend": "Declining", "confidence_score": "0.93"}, "accuracy_trends": {"accuracy_history": [{"date": "2025-07-12", "mae": "2.30", "predictions": 166, "accuracy_score": "0.43"}, {"date": "2025-07-11", "mae": "2.61", "predictions": 117, "accuracy_score": "0.35"}, {"date": "2025-07-10", "mae": "2.57", "predictions": 126, "accuracy_score": "0.36"}, {"date": "2025-07-09", "mae": "2.77", "predictions": 160, "accuracy_score": "0.31"}, {"date": "2025-07-08", "mae": "2.50", "predictions": 176, "accuracy_score": "0.37"}, {"date": "2025-07-07", "mae": "3.03", "predictions": 56, "accuracy_score": "0.24"}, {"date": "2025-07-06", "mae": "2.78", "predictions": 60, "accuracy_score": "0.31"}, {"date": "2025-07-05", "mae": "2.60", "predictions": 110, "accuracy_score": "0.35"}, {"date": "2025-07-04", "mae": "2.59", "predictions": 108, "accuracy_score": "0.35"}, {"date": "2025-07-03", "mae": "2.34", "predictions": 134, "accuracy_score": "0.42"}, {"date": "2025-07-02", "mae": "2.37", "predictions": 114, "accuracy_score": "0.41"}, {"date": "2025-07-01", "mae": "2.86", "predictions": 166, "accuracy_score": "0.29"}, {"date": "2025-06-30", "mae": "2.02", "predictions": 120, "accuracy_score": "0.50"}, {"date": "2025-06-29", "mae": "2.40", "predictions": 198, "accuracy_score": "0.40"}, {"date": "2025-06-28", "mae": "2.29", "predictions": 154, "accuracy_score": "0.43"}, {"date": "2025-06-27", "mae": "2.91", "predictions": 133, "accuracy_score": "0.27"}, {"date": "2025-06-26", "mae": "2.18", "predictions": 197, "accuracy_score": "0.46"}, {"date": "2025-06-25", "mae": "2.18", "predictions": 122, "accuracy_score": "0.46"}, {"date": "2025-06-24", "mae": "2.39", "predictions": 55, "accuracy_score": "0.40"}, {"date": "2025-06-23", "mae": "2.87", "predictions": 174, "accuracy_score": "0.28"}, {"date": "2025-06-22", "mae": "2.75", "predictions": 76, "accuracy_score": "0.31"}, {"date": "2025-06-21", "mae": "2.39", "predictions": 109, "accuracy_score": "0.40"}, {"date": "2025-06-20", "mae": "2.49", "predictions": 83, "accuracy_score": "0.38"}, {"date": "2025-06-19", "mae": "2.48", "predictions": 142, "accuracy_score": "0.38"}, {"date": "2025-06-18", "mae": "2.72", "predictions": 136, "accuracy_score": "0.32"}, {"date": "2025-06-17", "mae": "2.57", "predictions": 162, "accuracy_score": "0.36"}, {"date": "2025-06-16", "mae": "2.97", "predictions": 161, "accuracy_score": "0.26"}, {"date": "2025-06-15", "mae": "2.79", "predictions": 73, "accuracy_score": "0.30"}, {"date": "2025-06-14", "mae": "2.53", "predictions": 74, "accuracy_score": "0.37"}, {"date": "2025-06-13", "mae": "2.40", "predictions": 163, "accuracy_score": "0.40"}], "current_mae": "2.65", "trend": "Declining", "trend_change": "+8.0%", "best_day_mae": "2.02", "worst_day_mae": "3.03", "total_predictions": 3825, "avg_daily_predictions": "128"}}