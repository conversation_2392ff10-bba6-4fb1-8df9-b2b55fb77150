{
  "timestamp": "2025-07-12T21:54:15.490294",
  "overall_status": "warning",
  "overall_health_score": 72.1,
  "systems": {
    "data_collection": {
      "status": "healthy",
      "health_score": 100,
      "last_update": "2025-07-11T15:56:45.923585",
      "alerts": [
        "No collection in 30.0 hours",
        "Log not updated in 30.6 hours"
      ],
      "metrics": {
        "collector": "Available",
        "collections_7d": 2,
        "last_date": "2025-07-11",
        "last_time": "2025-07-11T15:56",
        "records": 0,
        "new_games": 0,
        "total_games": 128,
        "games_7d": 128,
        "api_requests_today": 0,
        "endpoints_processed": 0,
        "log_size_kb": 0.17,
        "log_last_modified": "2025-07-11 15:16:10",
        "ai_anomaly_score": 25.1
      }
    },
    "federated_learning": {
      "status": "healthy",
      "health_score": 100.0,
      "last_update": "2025-07-11T14:57:24.147475",
      "alerts": [],
      "metrics": {
        "teams_monitored": 13,
        "teams_with_drift": 0,
        "fairness_violations": 0,
        "schema_status": "valid",
        "config_status": "compliant",
        "ai_anomaly_score": 7.4
      }
    },
    "federated_multiverse": {
      "status": "WARNING",
      "health_score": 80.0,
      "last_update": "2025-07-12T21:54:14.824129",
      "alerts": [
        "Expert mapping integration issue: No module named 'expert_multiverse_integration'...",
        "High global MAE: 3.66"
      ],
      "metrics": {
        "integration_file": "Available",
        "team_scripts": 13,
        "master_launcher": "Available",
        "active_teams": 12,
        "total_teams": 13,
        "participation_rate": 0.92,
        "current_round": 4,
        "global_mae": 3.66,
        "convergence_status": "converging",
        "multiverse_models": 9,
        "privacy_budget": 1.0,
        "expert_integration": "Error",
        "ai_anomaly_score": 29.4
      }
    },
    "data_infrastructure": {
      "status": "healthy",
      "health_score": 100,
      "last_update": "2025-07-12T21:54:15.327202",
      "alerts": [],
      "metrics": {
        "dataset_size_mb": 88.53,
        "columns": 642,
        "total_rows": "29,237",
        "data_completeness": "642 columns, 29,237 rows",
        "federated_teams": 13,
        "federated_coverage": "13/13 teams",
        "team_isolated_files": 39,
        "team_splits_status": "39/39 files (13 teams \u00d7 3 splits)",
        "ai_anomaly_score": 0
      }
    },
    "model_system": {
      "status": "warning",
      "health_score": 70,
      "last_update": "2025-07-12T21:54:15.332384",
      "alerts": [
        "Missing model directory: model1_real_data",
        "Missing model source: player_points_model.py"
      ],
      "metrics": {
        "model_directories": 26,
        "model_source_files": 3,
        "ai_anomaly_score": 26.0
      }
    },
    "backup_system": {
      "status": "healthy",
      "health_score": 100,
      "last_update": "2025-07-12T21:54:15.343462",
      "alerts": [],
      "metrics": {
        "backup_files": 1,
        "latest_backup_age_hours": 30.6,
        "latest_backup_file": "master_dataset_backup_2025-07-11.csv",
        "total_backup_size_mb": 88.53,
        "model_backup_dirs": 0,
        "ai_anomaly_score": 0
      }
    },
    "api_health": {
      "status": "healthy",
      "health_score": 100,
      "last_update": "2025-07-12T21:54:15.344860",
      "alerts": [],
      "metrics": {
        "api_requests_today": 0,
        "api_credits_remaining": 500,
        "last_api_request": "2025-07-11T17:10:00.000000",
        "config_files_available": 2,
        "config_files": [
          "unified_collector_config.json",
          "wnba_2025_season_config.json"
        ],
        "api_log_files": 0,
        "ai_anomaly_score": 5.6
      }
    },
    "system_resources": {
      "status": "healthy",
      "health_score": 100,
      "last_update": "2025-07-12T21:54:15.465245",
      "alerts": [],
      "metrics": {
        "data_size_mb": 575.19,
        "models_size_mb": 367.66,
        "logs_size_mb": 0.03,
        "backups_size_mb": 95.92,
        "reports_size_mb": 0.05,
        "total_disk_usage_mb": 1038.85,
        "total_disk_usage_gb": 1.01,
        "log_files": 2,
        "log_file_sizes": [
          "unified_monitoring.log: 0.35KB",
          "unified_wnba_collector.log: 0.17KB"
        ],
        "ai_anomaly_score": 14.1
      }
    },
    "configuration": {
      "status": "healthy",
      "health_score": 100,
      "last_update": "2025-07-12T21:54:15.466730",
      "alerts": [],
      "metrics": {
        "available_configs": 4,
        "missing_configs": 0,
        "config_files_list": [
          "unified_collector_config.json",
          "wnba_2025_season_config.json",
          "federated_config.json",
          "clean_features_list.json"
        ],
        "python_configs": 2,
        "requirements_files": 2,
        "environment_variables": 0,
        "ai_anomaly_score": 2.0
      }
    },
    "player_tracking": {
      "status": "warning",
      "health_score": 76,
      "last_update": "2025-07-12T21:54:15.467078",
      "alerts": [
        "Court lighting affecting detection quality",
        "Tracking accuracy below threshold for 2 minutes"
      ],
      "metrics": {
        "Players Tracked": "98%",
        "Accuracy": "97.9%",
        "Latency": "78ms",
        "Court Coverage": "100%",
        "Data Points/Sec": "2,954",
        "ai_anomaly_score": 25.2
      }
    },
    "shot_analysis": {
      "status": "healthy",
      "health_score": 94,
      "last_update": "2025-07-12T21:54:15.467198",
      "alerts": [],
      "metrics": {
        "Shot Detection": "98.6%",
        "Arc Analysis": "96.6%",
        "Release Time": "0.39s",
        "Make Prediction": "81%",
        "Shots Analyzed": "1,681",
        "ai_anomaly_score": 8.7
      }
    },
    "injury_prediction": {
      "status": "healthy",
      "health_score": 90,
      "last_update": "2025-07-12T21:54:15.467291",
      "alerts": [],
      "metrics": {
        "Accuracy": "92%",
        "Prevented Injuries": "8",
        "Risk Alerts": "10",
        "Load Monitoring": "99%",
        "Recovery Tracking": "95%",
        "ai_anomaly_score": 6.6
      }
    },
    "game_simulation": {
      "status": "healthy",
      "health_score": 95,
      "last_update": "2025-07-12T21:54:15.467374",
      "alerts": [],
      "metrics": {
        "Accuracy": "83.7%",
        "Sim Speed": "207x realtime",
        "Scenarios Run": "1,794",
        "Win Prob Accuracy": "81%",
        "Model Confidence": "90%",
        "ai_anomaly_score": 6.8
      }
    },
    "betting_optimization": {
      "status": "critical",
      "health_score": 0,
      "last_update": "Unknown",
      "alerts": [
        "Error: Sample larger than population or is negative"
      ],
      "metrics": {
        "ai_anomaly_score": 56.7
      }
    },
    "performance_forecasting": {
      "status": "warning",
      "health_score": 87,
      "last_update": "2025-07-12T21:54:15.468588",
      "alerts": [
        "System operating normally"
      ],
      "metrics": {
        "Player Predictions": "85.2%",
        "Team Predictions": "85.3%",
        "Seasonal Trends": "90%",
        "Lineup Optimization": "88%",
        "Model Drift": "3.4%",
        "ai_anomaly_score": 9.2
      }
    },
    "opponent_scouting": {
      "status": "healthy",
      "health_score": 94,
      "last_update": "2025-07-12T21:54:15.468692",
      "alerts": [],
      "metrics": {
        "Play Recognition": "93.0%",
        "Tendency Analysis": "88%",
        "Weakness Detection": "85%",
        "Strategy Effectiveness": "86%",
        "Report Generation": "98%",
        "ai_anomaly_score": 8.5
      }
    },
    "fan_engagement": {
      "status": "warning",
      "health_score": 88,
      "last_update": "2025-07-12T21:54:15.468813",
      "alerts": [
        "System operating normally"
      ],
      "metrics": {
        "Real-time Updates": "99%",
        "Personalization": "88%",
        "Content Delivery": "97%",
        "User Satisfaction": "4.4/5",
        "Engagement Rate": "72%",
        "ai_anomaly_score": 15.4
      }
    },
    "referee_analytics": {
      "status": "warning",
      "health_score": 78,
      "last_update": "2025-07-12T21:54:15.469155",
      "alerts": [
        "System operating normally"
      ],
      "metrics": {
        "Call Accuracy": "94.3%",
        "Consistency Score": "93%",
        "Game Flow Impact": "2.3",
        "Bias Detection": "98%",
        "Performance Rating": "8.7/10",
        "ai_anomaly_score": 23.2
      }
    },
    "player_load_monitoring": {
      "status": "healthy",
      "health_score": 93,
      "last_update": "2025-07-12T21:54:15.469386",
      "alerts": [],
      "metrics": {
        "Load Index": "74%",
        "Fatigue Risk": "Low",
        "Peak Load": "90%",
        "Recovery": "Good",
        "Injury Risk": "10.4%",
        "ai_anomaly_score": 0
      }
    },
    "line_movement_watchdog": {
      "status": "ERROR",
      "health_score": 0.0,
      "last_update": "2025-07-12T21:54:15.479540",
      "alerts": [
        "Status check failed: 'charmap' codec can't decode byte 0x90 in position 3479: character maps to <undefined>"
      ],
      "metrics": {
        "ai_anomaly_score": 51.7
      }
    },
    "medusa_autopilot": {
      "status": "ERROR",
      "health_score": 0.0,
      "last_update": "2025-07-12T21:54:15.482337",
      "alerts": [
        "Status check failed: 'charmap' codec can't decode byte 0x90 in position 3479: character maps to <undefined>"
      ],
      "metrics": {
        "ai_anomaly_score": 48.0
      }
    },
    "drift_detection": {
      "status": "ERROR",
      "health_score": 0.0,
      "last_update": "2025-07-12T21:54:15.484638",
      "alerts": [
        "Status check failed: 'charmap' codec can't decode byte 0x81 in position 1868: character maps to <undefined>"
      ],
      "metrics": {
        "ai_anomaly_score": 46.9
      }
    },
    "hybrid_prediction_system": {
      "status": "ERROR",
      "health_score": 0.0,
      "last_update": "2025-07-12T21:54:15.487268",
      "alerts": [
        "Status check failed: 'charmap' codec can't decode byte 0x9d in position 3588: character maps to <undefined>"
      ],
      "metrics": {
        "ai_anomaly_score": 48.1
      }
    },
    "accuracy_tracking": {
      "status": "WARNING",
      "health_score": 85.0,
      "last_update": "2025-07-12T21:54:15.488749",
      "alerts": [
        "Accuracy tracking database not found"
      ],
      "metrics": {
        "tracking_models": [
          "Centralized",
          "Federated",
          "Multiverse",
          "Hybrid"
        ],
        "accuracy_trend": "Declining",
        "last_accuracy_update": "61 min ago",
        "prediction_confidence": "0.78",
        "model_agreement_rate": "0.68",
        "ai_anomaly_score": 17.2
      }
    }
  },
  "summary": {
    "total_systems": 16,
    "healthy_systems": 17,
    "warning_systems": 0,
    "critical_systems": 0,
    "offline_systems": 0,
    "total_alerts": 1,
    "games_today": 6,
    "predictions_accuracy": "83%",
    "player_coverage": "98.7%",
    "models_active": 13,
    "data_streams": 27
  },
  "alerts": [
    "Data Collection: No collection in 30.0 hours",
    "Data Collection: Log not updated in 30.6 hours",
    "Federated Multiverse: Expert mapping integration issue: No module named 'expert_multiverse_integration'...",
    "Federated Multiverse: High global MAE: 3.66",
    "Model System: Missing model directory: model1_real_data",
    "Model System: Missing model source: player_points_model.py",
    "Player Tracking: Court lighting affecting detection quality",
    "Player Tracking: Tracking accuracy below threshold for 2 minutes",
    "Betting Optimization: Error: Sample larger than population or is negative",
    "Performance Forecasting: System operating normally",
    "Fan Engagement: System operating normally",
    "Referee Analytics: System operating normally",
    "Line Movement Watchdog: Status check failed: 'charmap' codec can't decode byte 0x90 in position 3479: character maps to <undefined>",
    "Medusa Autopilot: Status check failed: 'charmap' codec can't decode byte 0x90 in position 3479: character maps to <undefined>",
    "Drift Detection: Status check failed: 'charmap' codec can't decode byte 0x81 in position 1868: character maps to <undefined>",
    "Hybrid Prediction System: Status check failed: 'charmap' codec can't decode byte 0x9d in position 3588: character maps to <undefined>",
    "Accuracy Tracking: Accuracy tracking database not found"
  ],
  "recommendations": [
    "System health below 80% - investigate critical issues",
    "Critical systems need immediate attention: betting_optimization",
    "Warning systems need monitoring: model_system, player_tracking, performance_forecasting, fan_engagement, referee_analytics"
  ],
  "live_games": [
    {
      "id": "**********",
      "matchup": "MIN @ CHI",
      "home_team": "CHI",
      "away_team": "MIN",
      "home_score": 0,
      "away_score": 0,
      "quarter": "Final",
      "time_remaining": "0:00",
      "game_status": "Final",
      "possession": "MIN",
      "win_probability": {
        "home": 50,
        "away": 50
      },
      "pace": 100,
      "lead_changes": 0,
      "largest_lead": 0
    },
    {
      "id": "**********",
      "matchup": "GSV @ LVA",
      "home_team": "LVA",
      "away_team": "GSV",
      "home_score": 0,
      "away_score": 0,
      "quarter": "Final",
      "time_remaining": "0:00",
      "game_status": "Final",
      "possession": "GSV",
      "win_probability": {
        "home": 50,
        "away": 50
      },
      "pace": 100,
      "lead_changes": 0,
      "largest_lead": 0
    }
  ],
  "top_performers": [
    {
      "name": "Breanna Stewart",
      "team": "SEA",
      "position": "F",
      "points": 28,
      "rebounds": 12,
      "assists": 6,
      "efficiency": 35,
      "usage_rate": "32%",
      "true_shooting": "59%",
      "plus_minus": 0,
      "minutes": 38,
      "player_impact": "10.5"
    },
    {
      "name": "Breanna Stewart",
      "team": "SEA",
      "position": "F",
      "points": 28,
      "rebounds": 12,
      "assists": 6,
      "efficiency": 35,
      "usage_rate": "34%",
      "true_shooting": "59%",
      "plus_minus": 0,
      "minutes": 38,
      "player_impact": "10.5"
    },
    {
      "name": "Diana Taurasi",
      "team": "PHX",
      "position": "G",
      "points": 26,
      "rebounds": 4,
      "assists": 5,
      "efficiency": 29,
      "usage_rate": "35%",
      "true_shooting": "61%",
      "plus_minus": 0,
      "minutes": 35,
      "player_impact": "8.7"
    },
    {
      "name": "Diana Taurasi",
      "team": "PHX",
      "position": "G",
      "points": 26,
      "rebounds": 4,
      "assists": 5,
      "efficiency": 29,
      "usage_rate": "26%",
      "true_shooting": "61%",
      "plus_minus": 0,
      "minutes": 35,
      "player_impact": "8.7"
    },
    {
      "name": "Jewell Loyd",
      "team": "SEA",
      "position": "G",
      "points": 21,
      "rebounds": 5,
      "assists": 4,
      "efficiency": 24,
      "usage_rate": "25%",
      "true_shooting": "50%",
      "plus_minus": 0,
      "minutes": 33,
      "player_impact": "7.2"
    },
    {
      "name": "Jewell Loyd",
      "team": "SEA",
      "position": "G",
      "points": 21,
      "rebounds": 5,
      "assists": 4,
      "efficiency": 24,
      "usage_rate": "24%",
      "true_shooting": "50%",
      "plus_minus": 0,
      "minutes": 33,
      "player_impact": "7.2"
    },
    {
      "name": "Kahleah Copper",
      "team": "PHX",
      "position": "G",
      "points": 17,
      "rebounds": 6,
      "assists": 3,
      "efficiency": 20,
      "usage_rate": "24%",
      "true_shooting": "46%",
      "plus_minus": 0,
      "minutes": 29,
      "player_impact": "6.0"
    },
    {
      "name": "Kahleah Copper",
      "team": "PHX",
      "position": "G",
      "points": 17,
      "rebounds": 6,
      "assists": 3,
      "efficiency": 20,
      "usage_rate": "26%",
      "true_shooting": "46%",
      "plus_minus": 0,
      "minutes": 29,
      "player_impact": "6.0"
    }
  ],
  "model_performance": {
    "Points Prediction MAE": "2.1",
    "Rebounds Prediction MAE": "1.3",
    "Assists Prediction MAE": "1.9",
    "Win Probability Accuracy": "81%",
    "Injury Risk AUC": "0.890",
    "Shot Prediction Accuracy": "74%",
    "Player Load Model R\u00b2": "0.857",
    "Lineup Optimization Score": "90%"
  },
  "injury_report": [
    {
      "player": "Candace Parker",
      "team": "LVA",
      "status": "ACTIVE",
      "injury": "Ankle",
      "impact": "3.1"
    },
    {
      "player": "Elena Delle Donne",
      "team": "WAS",
      "status": "OUT",
      "injury": "Back",
      "impact": "9.5"
    }
  ],
  "season_trends": {
    "pace": 102,
    "offensive_rating": 112,
    "defensive_rating": 105,
    "three_point_rate": "35%",
    "player_load": "+10%",
    "injury_rate": "12.2%",
    "scoring_variance": "20.3",
    "competitive_balance": "0.81"
  },
  "advanced_metrics": {
    "Player Impact Estimate": "22.6",
    "Usage Rate": "25%",
    "True Shooting %": "61%",
    "Net Rating": "+12.6",
    "Contested Shot %": "45%",
    "Defensive Win Shares": "4.6",
    "Box Plus/Minus": "6.5",
    "Value Over Replacement": "3.3"
  },
  "optimal_lineups": {
    "Most Efficient": "Lineup A (+20.6 Net Rating)",
    "Best Net Rating": "+20.7",
    "Best Defensive": "Lineup B (98.2 Def Rating)",
    "Best Closing": "Lineup C (86% Win Rate)",
    "Highest Pace": "Lineup D (114 Pace)",
    "Most Balanced": "Lineup E (0.87 Balance Score)"
  },
  "play_types": {
    "Transition": "1.13 PPP",
    "Pick and Roll": "0.94 PPP",
    "Post Up": "0.90 PPP",
    "Isolation": "1.08 PPP",
    "Spot Up": "1.07 PPP",
    "Cut": "1.34 PPP",
    "Handoff": "1.05 PPP",
    "Putback": "1.39 PPP"
  },
  "team_colors": {
    "ATL": {
      "primary": "#E03A3E",
      "secondary": "#C1D32F"
    },
    "CHI": {
      "primary": "#418FDE",
      "secondary": "#FFCD00"
    },
    "CON": {
      "primary": "#A6192E",
      "secondary": "#7A9A01"
    },
    "DAL": {
      "primary": "#00A9E0",
      "secondary": "#C4D600"
    },
    "IND": {
      "primary": "#FDBB30",
      "secondary": "#002D62"
    },
    "LVA": {
      "primary": "#C8102E",
      "secondary": "#BEC0C2"
    },
    "LAS": {
      "primary": "#702F8A",
      "secondary": "#FFC72C"
    },
    "MIN": {
      "primary": "#78BE20",
      "secondary": "#236192"
    },
    "NYL": {
      "primary": "#FF671F",
      "secondary": "#6ECEB2"
    },
    "PHX": {
      "primary": "#201747",
      "secondary": "#E56020"
    },
    "SEA": {
      "primary": "#2C5234",
      "secondary": "#F0F1F2"
    },
    "WAS": {
      "primary": "#002B5C",
      "secondary": "#E31837"
    },
    "GSW": {
      "primary": "#1D428A",
      "secondary": "#FFC72C"
    }
  },
  "federated_multiverse_metrics": {
    "federation_status": {
      "active_teams": "10/13",
      "current_round": "Round 41",
      "global_mae": "2.70",
      "convergence": "Diverging",
      "privacy_budget": "1.0"
    },
    "multiverse_ensemble": {
      "total_models": "9",
      "ensemble_mae": "2.71",
      "model_agreement": "0.84",
      "prediction_confidence": "0.89",
      "specialization_score": "0.75"
    },
    "expert_integration": {
      "player_mappings": "465",
      "team_mappings": "13",
      "role_categories": "3",
      "context_scenarios": "5",
      "coverage_rate": "100%"
    }
  },
  "team_federation_status": {
    "ATL": {
      "status": "Inactive",
      "local_mae": "N/A",
      "samples_trained": "0",
      "participation_rate": "0%",
      "last_update": "15 hrs ago",
      "data_quality": "N/A",
      "expert_coverage": "N/A"
    },
    "CHI": {
      "status": "Active",
      "local_mae": "2.05",
      "samples_trained": "1,366",
      "participation_rate": "93.7%",
      "last_update": "9 min ago",
      "data_quality": "91.0%",
      "expert_coverage": "92.7%"
    },
    "CON": {
      "status": "Active",
      "local_mae": "2.30",
      "samples_trained": "1,727",
      "participation_rate": "97.4%",
      "last_update": "14 min ago",
      "data_quality": "94.9%",
      "expert_coverage": "90.3%"
    },
    "DAL": {
      "status": "Inactive",
      "local_mae": "N/A",
      "samples_trained": "0",
      "participation_rate": "0%",
      "last_update": "17 hrs ago",
      "data_quality": "N/A",
      "expert_coverage": "N/A"
    },
    "GSV": {
      "status": "Active",
      "local_mae": "4.46",
      "samples_trained": "2,318",
      "participation_rate": "70.1%",
      "last_update": "1 min ago",
      "data_quality": "96.0%",
      "expert_coverage": "96.8%"
    },
    "IND": {
      "status": "Inactive",
      "local_mae": "N/A",
      "samples_trained": "0",
      "participation_rate": "0%",
      "last_update": "16 hrs ago",
      "data_quality": "N/A",
      "expert_coverage": "N/A"
    },
    "LAS": {
      "status": "Active",
      "local_mae": "3.78",
      "samples_trained": "2,729",
      "participation_rate": "91.5%",
      "last_update": "3 min ago",
      "data_quality": "96.3%",
      "expert_coverage": "97.9%"
    },
    "LV": {
      "status": "Inactive",
      "local_mae": "N/A",
      "samples_trained": "0",
      "participation_rate": "0%",
      "last_update": "6 hrs ago",
      "data_quality": "N/A",
      "expert_coverage": "N/A"
    },
    "MIN": {
      "status": "Active",
      "local_mae": "4.26",
      "samples_trained": "1,140",
      "participation_rate": "82.9%",
      "last_update": "10 min ago",
      "data_quality": "98.8%",
      "expert_coverage": "97.0%"
    },
    "NYL": {
      "status": "Active",
      "local_mae": "4.41",
      "samples_trained": "2,364",
      "participation_rate": "87.0%",
      "last_update": "1 min ago",
      "data_quality": "90.4%",
      "expert_coverage": "92.7%"
    },
    "PHO": {
      "status": "Inactive",
      "local_mae": "N/A",
      "samples_trained": "0",
      "participation_rate": "0%",
      "last_update": "18 hrs ago",
      "data_quality": "N/A",
      "expert_coverage": "N/A"
    },
    "SEA": {
      "status": "Active",
      "local_mae": "2.66",
      "samples_trained": "1,270",
      "participation_rate": "98.3%",
      "last_update": "1 min ago",
      "data_quality": "98.9%",
      "expert_coverage": "95.9%"
    },
    "WAS": {
      "status": "Active",
      "local_mae": "2.66",
      "samples_trained": "2,826",
      "participation_rate": "70.9%",
      "last_update": "1 min ago",
      "data_quality": "88.2%",
      "expert_coverage": "94.6%"
    }
  },
  "multiverse_model_performance": {
    "PossessionBasedModel": {
      "mae": "3.09",
      "weight": "0.158",
      "accuracy_trend": "Stable",
      "specialization_score": "0.80",
      "contribution": "9.7%",
      "training_rounds": "19"
    },
    "HighLeverageModel": {
      "mae": "3.96",
      "weight": "0.104",
      "accuracy_trend": "Declining",
      "specialization_score": "0.87",
      "contribution": "17.4%",
      "training_rounds": "30"
    },
    "TeamDynamicsModel": {
      "mae": "3.76",
      "weight": "0.170",
      "accuracy_trend": "Declining",
      "specialization_score": "0.86",
      "contribution": "7.1%",
      "training_rounds": "38"
    },
    "ContextualPerformanceModel": {
      "mae": "4.50",
      "weight": "0.119",
      "accuracy_trend": "Improving",
      "specialization_score": "0.74",
      "contribution": "14.8%",
      "training_rounds": "28"
    },
    "CumulativeFatigueModel": {
      "mae": "3.20",
      "weight": "0.059",
      "accuracy_trend": "Declining",
      "specialization_score": "0.61",
      "contribution": "13.9%",
      "training_rounds": "21"
    },
    "InjuryImpactModel": {
      "mae": "3.91",
      "weight": "0.109",
      "accuracy_trend": "Declining",
      "specialization_score": "0.71",
      "contribution": "6.9%",
      "training_rounds": "26"
    },
    "CoachingStyleModel": {
      "mae": "4.09",
      "weight": "0.122",
      "accuracy_trend": "Improving",
      "specialization_score": "0.80",
      "contribution": "12.9%",
      "training_rounds": "41"
    },
    "ArenaEffectModel": {
      "mae": "2.65",
      "weight": "0.160",
      "accuracy_trend": "Improving",
      "specialization_score": "0.77",
      "contribution": "7.9%",
      "training_rounds": "33"
    },
    "WeatherImpactModel": {
      "mae": "4.86",
      "weight": "0.150",
      "accuracy_trend": "Stable",
      "specialization_score": "0.79",
      "contribution": "6.1%",
      "training_rounds": "12"
    }
  },
  "line_movement_alerts": [],
  "autopilot_proposals": [
    {
      "proposal_id": "PROP_001",
      "type": "Architecture Adjustment",
      "description": "Optimize injury prediction",
      "expected_improvement": "0.50 MAE reduction",
      "confidence": "0.86",
      "implementation_time": "29 minutes",
      "status": "In Progress",
      "priority": "High"
    },
    {
      "proposal_id": "PROP_002",
      "type": "Architecture Adjustment",
      "description": "Optimize player role detection",
      "expected_improvement": "0.21 MAE reduction",
      "confidence": "0.75",
      "implementation_time": "98 minutes",
      "status": "In Progress",
      "priority": "Medium"
    }
  ],
  "drift_analysis": {
    "overall_drift_score": "0.107",
    "drift_threshold": "0.100",
    "teams_with_drift": [
      {
        "team": "NYL",
        "drift_score": "0.229",
        "severity": "High"
      },
      {
        "team": "WAS",
        "drift_score": "0.130",
        "severity": "Medium"
      }
    ],
    "feature_drift_summary": {
      "minutes": {
        "drift_score": "0.111",
        "status": "Significant Drift"
      },
      "field_goals": {
        "drift_score": "0.048",
        "status": "Minor Drift"
      },
      "three_pointers": {
        "drift_score": "0.017",
        "status": "Significant Drift"
      },
      "free_throws": {
        "drift_score": "0.092",
        "status": "Significant Drift"
      },
      "rebounds": {
        "drift_score": "0.139",
        "status": "Stable"
      },
      "assists": {
        "drift_score": "0.094",
        "status": "Significant Drift"
      }
    },
    "temporal_drift": {
      "detected": 