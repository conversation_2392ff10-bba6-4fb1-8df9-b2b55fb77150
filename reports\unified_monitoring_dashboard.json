{"timestamp": "2025-07-12T21:44:40.242225", "overall_status": "healthy", "overall_health_score": 92.9, "systems": {"data_collection": {"status": "healthy", "health_score": 100, "last_update": "2025-07-11T15:56:45.923585", "alerts": ["No collection in 29.8 hours", "Log not updated in 30.5 hours"], "metrics": {"collector": "Available", "collections_7d": 2, "last_date": "2025-07-11", "last_time": "2025-07-11T15:56", "records": 0, "new_games": 0, "total_games": 128, "games_7d": 128, "api_requests_today": 0, "endpoints_processed": 0, "log_size_kb": 0.17, "log_last_modified": "2025-07-11 15:16:10", "ai_anomaly_score": 20.6}}, "federated_learning": {"status": "healthy", "health_score": 100.0, "last_update": "2025-07-11T14:57:24.147475", "alerts": [], "metrics": {"teams_monitored": 13, "teams_with_drift": 0, "fairness_violations": 0, "schema_status": "valid", "config_status": "compliant", "ai_anomaly_score": 2.5}}, "federated_multiverse": {"status": "WARNING", "health_score": 80.0, "last_update": "2025-07-12T21:44:39.835175", "alerts": ["Expert mapping integration issue: No module named 'expert_multiverse_integration'..."], "metrics": {"integration_file": "Available", "team_scripts": 13, "master_launcher": "Available", "active_teams": 8, "total_teams": 13, "participation_rate": 0.62, "current_round": 16, "global_mae": 2.53, "convergence_status": "stable", "multiverse_models": 9, "privacy_budget": 1.0, "expert_integration": "Error", "ai_anomaly_score": 18.4}}, "data_infrastructure": {"status": "healthy", "health_score": 100, "last_update": "2025-07-12T21:44:40.221920", "alerts": [], "metrics": {"dataset_size_mb": 88.53, "columns": 642, "total_rows": "29,237", "data_completeness": "642 columns, 29,237 rows", "federated_teams": 13, "federated_coverage": "13/13 teams", "team_isolated_files": 39, "team_splits_status": "39/39 files (13 teams × 3 splits)", "ai_anomaly_score": 0}}, "model_system": {"status": "warning", "health_score": 70, "last_update": "2025-07-12T21:44:40.222678", "alerts": ["Missing model directory: model1_real_data", "Missing model source: player_points_model.py"], "metrics": {"model_directories": 26, "model_source_files": 3, "ai_anomaly_score": 26.3}}, "backup_system": {"status": "healthy", "health_score": 100, "last_update": "2025-07-12T21:44:40.224515", "alerts": [], "metrics": {"backup_files": 1, "latest_backup_age_hours": 30.5, "latest_backup_file": "master_dataset_backup_2025-07-11.csv", "total_backup_size_mb": 88.53, "model_backup_dirs": 0, "ai_anomaly_score": 5.4}}, "api_health": {"status": "healthy", "health_score": 100, "last_update": "2025-07-12T21:44:40.224953", "alerts": [], "metrics": {"api_requests_today": 0, "api_credits_remaining": 500, "last_api_request": "2025-07-11T17:10:00.000000", "config_files_available": 2, "config_files": ["unified_collector_config.json", "wnba_2025_season_config.json"], "api_log_files": 0, "ai_anomaly_score": 9.2}}, "system_resources": {"status": "healthy", "health_score": 100, "last_update": "2025-07-12T21:44:40.240282", "alerts": [], "metrics": {"data_size_mb": 575.19, "models_size_mb": 367.66, "logs_size_mb": 0.03, "backups_size_mb": 95.92, "reports_size_mb": 0.04, "total_disk_usage_mb": 1038.84, "total_disk_usage_gb": 1.01, "log_files": 2, "log_file_sizes": ["unified_monitoring.log: 0.35KB", "unified_wnba_collector.log: 0.17KB"], "ai_anomaly_score": 10.5}}, "configuration": {"status": "healthy", "health_score": 100, "last_update": "2025-07-12T21:44:40.241801", "alerts": [], "metrics": {"available_configs": 4, "missing_configs": 0, "config_files_list": ["unified_collector_config.json", "wnba_2025_season_config.json", "federated_config.json", "clean_features_list.json"], "python_configs": 2, "requirements_files": 2, "environment_variables": 0, "ai_anomaly_score": 5.1}}, "player_tracking": {"status": "healthy", "health_score": 97, "last_update": "2025-07-12T21:44:40.241876", "alerts": [], "metrics": {"Players Tracked": "96%", "Accuracy": "98.5%", "Latency": "82ms", "Court Coverage": "99%", "Data Points/Sec": "2,953", "ai_anomaly_score": 3.0}}, "shot_analysis": {"status": "warning", "health_score": 77, "last_update": "2025-07-12T21:44:40.241920", "alerts": ["Rim detection accuracy below 98%", "Shot arc detection variance increased 12%"], "metrics": {"Shot Detection": "98.8%", "Arc Analysis": "97.6%", "Release Time": "0.44s", "Make Prediction": "82%", "Shots Analyzed": "1,507", "ai_anomaly_score": 24.6}}, "injury_prediction": {"status": "healthy", "health_score": 96, "last_update": "2025-07-12T21:44:40.241946", "alerts": [], "metrics": {"Accuracy": "90%", "Prevented Injuries": "14", "Risk Alerts": "6", "Load Monitoring": "96%", "Recovery Tracking": "92%", "ai_anomaly_score": 3.2}}, "game_simulation": {"status": "healthy", "health_score": 92, "last_update": "2025-07-12T21:44:40.241970", "alerts": [], "metrics": {"Accuracy": "84.3%", "Sim Speed": "261x realtime", "Scenarios Run": "2,386", "Win Prob Accuracy": "79%", "Model Confidence": "91%", "ai_anomaly_score": 0}}, "betting_optimization": {"status": "warning", "health_score": 86, "last_update": "2025-07-12T21:44:40.241995", "alerts": ["System operating normally"], "metrics": {"Line Accuracy": "78.5%", "Market Coverage": "99%", "Update Frequency": "21s", "Profit Margin": "5.2%", "Risk Assessment": "94%", "ai_anomaly_score": 10.9}}, "performance_forecasting": {"status": "healthy", "health_score": 90, "last_update": "2025-07-12T21:44:40.242017", "alerts": [], "metrics": {"Player Predictions": "80.1%", "Team Predictions": "82.1%", "Seasonal Trends": "87%", "Lineup Optimization": "94%", "Model Drift": "2.1%", "ai_anomaly_score": 9.1}}, "opponent_scouting": {"status": "healthy", "health_score": 97, "last_update": "2025-07-12T21:44:40.242039", "alerts": [], "metrics": {"Play Recognition": "95.3%", "Tendency Analysis": "92%", "Weakness Detection": "88%", "Strategy Effectiveness": "85%", "Report Generation": "99%", "ai_anomaly_score": 1.5}}, "fan_engagement": {"status": "healthy", "health_score": 94, "last_update": "2025-07-12T21:44:40.242060", "alerts": [], "metrics": {"Real-time Updates": "99%", "Personalization": "87%", "Content Delivery": "96%", "User Satisfaction": "4.3/5", "Engagement Rate": "71%", "ai_anomaly_score": 7.9}}, "referee_analytics": {"status": "healthy", "health_score": 94, "last_update": "2025-07-12T21:44:40.242082", "alerts": [], "metrics": {"Call Accuracy": "94.9%", "Consistency Score": "93%", "Game Flow Impact": "2.2", "Bias Detection": "98%", "Performance Rating": "8.9/10", "ai_anomaly_score": 7.0}}, "player_load_monitoring": {"status": "healthy", "health_score": 92, "last_update": "2025-07-12T21:44:40.242102", "alerts": [], "metrics": {"Load Index": "75%", "Fatigue Risk": "Medium", "Peak Load": "94%", "Recovery": "Good", "Injury Risk": "11.5%", "ai_anomaly_score": 2.2}}}, "summary": {"total_systems": 17, "healthy_systems": 16, "warning_systems": 1, "critical_systems": 1, "offline_systems": 0, "total_alerts": 1, "games_today": 5, "predictions_accuracy": "79%", "player_coverage": "98.7%", "models_active": 14, "data_streams": 30}, "alerts": ["Data Collection: No collection in 29.8 hours", "Data Collection: Log not updated in 30.5 hours", "Federated Multiverse: Expert mapping integration issue: No module named 'expert_multiverse_integration'...", "Model System: Missing model directory: model1_real_data", "Model System: Missing model source: player_points_model.py", "Shot Analysis: Rim detection accuracy below 98%", "Shot Analysis: Shot arc detection variance increased 12%", "Betting Optimization: System operating normally"], "recommendations": ["Warning systems need monitoring: model_system, shot_analysis, betting_optimization"], "live_games": [{"id": "**********", "matchup": "MIN @ CHI", "home_team": "CHI", "away_team": "MIN", "home_score": 0, "away_score": 0, "quarter": "Final", "time_remaining": "0:00", "game_status": "Final", "possession": "MIN", "win_probability": {"home": 50, "away": 50}, "pace": 100, "lead_changes": 0, "largest_lead": 0}, {"id": "1022500132", "matchup": "GSV @ LVA", "home_team": "LVA", "away_team": "GSV", "home_score": 0, "away_score": 0, "quarter": "Final", "time_remaining": "0:00", "game_status": "Final", "possession": "GSV", "win_probability": {"home": 50, "away": 50}, "pace": 100, "lead_changes": 0, "largest_lead": 0}], "top_performers": [{"name": "<PERSON><PERSON><PERSON>", "team": "SEA", "position": "F", "points": 28, "rebounds": 12, "assists": 6, "efficiency": 35, "usage_rate": "29%", "true_shooting": "59%", "plus_minus": 0, "minutes": 38, "player_impact": "10.5"}, {"name": "<PERSON><PERSON><PERSON>", "team": "SEA", "position": "F", "points": 28, "rebounds": 12, "assists": 6, "efficiency": 35, "usage_rate": "23%", "true_shooting": "59%", "plus_minus": 0, "minutes": 38, "player_impact": "10.5"}, {"name": "<PERSON>", "team": "PHX", "position": "G", "points": 26, "rebounds": 4, "assists": 5, "efficiency": 29, "usage_rate": "27%", "true_shooting": "61%", "plus_minus": 0, "minutes": 35, "player_impact": "8.7"}, {"name": "<PERSON>", "team": "PHX", "position": "G", "points": 26, "rebounds": 4, "assists": 5, "efficiency": 29, "usage_rate": "26%", "true_shooting": "61%", "plus_minus": 0, "minutes": 35, "player_impact": "8.7"}, {"name": "<PERSON><PERSON>", "team": "SEA", "position": "G", "points": 21, "rebounds": 5, "assists": 4, "efficiency": 24, "usage_rate": "23%", "true_shooting": "50%", "plus_minus": 0, "minutes": 33, "player_impact": "7.2"}, {"name": "<PERSON><PERSON>", "team": "SEA", "position": "G", "points": 21, "rebounds": 5, "assists": 4, "efficiency": 24, "usage_rate": "24%", "true_shooting": "50%", "plus_minus": 0, "minutes": 33, "player_impact": "7.2"}, {"name": "Kahleah Copper", "team": "PHX", "position": "G", "points": 17, "rebounds": 6, "assists": 3, "efficiency": 20, "usage_rate": "24%", "true_shooting": "46%", "plus_minus": 0, "minutes": 29, "player_impact": "6.0"}, {"name": "Kahleah Copper", "team": "PHX", "position": "G", "points": 17, "rebounds": 6, "assists": 3, "efficiency": 20, "usage_rate": "22%", "true_shooting": "46%", "plus_minus": 0, "minutes": 29, "player_impact": "6.0"}], "model_performance": {"Points Prediction MAE": "1.9", "Rebounds Prediction MAE": "1.8", "Assists Prediction MAE": "1.9", "Win Probability Accuracy": "84%", "Injury Risk AUC": "0.895", "Shot Prediction Accuracy": "80%", "Player Load Model R²": "0.818", "Lineup Optimization Score": "91%"}, "injury_report": [{"player": "<PERSON>", "team": "SEA", "status": "LOAD_MGMT", "injury": "Knee", "impact": "6.8"}, {"player": "<PERSON><PERSON><PERSON><PERSON> G<PERSON>r", "team": "PHX", "status": "PROBABLE", "injury": "<PERSON>rist", "impact": "4.5"}], "season_trends": {"pace": 101, "offensive_rating": 108, "defensive_rating": 103, "three_point_rate": "33%", "player_load": "+7%", "injury_rate": "9.5%", "scoring_variance": "20.3", "competitive_balance": "0.66"}, "advanced_metrics": {"Player Impact Estimate": "21.2", "Usage Rate": "23%", "True Shooting %": "62%", "Net Rating": "+15.7", "Contested Shot %": "35%", "Defensive Win Shares": "3.8", "Box Plus/Minus": "7.0", "Value Over Replacement": "3.6"}, "optimal_lineups": {"Most Efficient": "Lineup A (+21.0 Net Rating)", "Best Net Rating": "+25.4", "Best Defensive": "Lineup B (97.9 Def Rating)", "Best Closing": "Lineup C (82% Win Rate)", "Highest Pace": "Lineup D (112 Pace)", "Most Balanced": "Lineup E (0.92 Balance Score)"}, "play_types": {"Transition": "1.11 PPP", "Pick and Roll": "0.93 PPP", "Post Up": "0.92 PPP", "Isolation": "0.97 PPP", "Spot Up": "1.04 PPP", "Cut": "1.17 PPP", "Handoff": "1.01 PPP", "Putback": "1.43 PPP"}, "team_colors": {"ATL": {"primary": "#E03A3E", "secondary": "#C1D32F"}, "CHI": {"primary": "#418FDE", "secondary": "#FFCD00"}, "CON": {"primary": "#A6192E", "secondary": "#7A9A01"}, "DAL": {"primary": "#00A9E0", "secondary": "#C4D600"}, "IND": {"primary": "#FDBB30", "secondary": "#002D62"}, "LVA": {"primary": "#C8102E", "secondary": "#BEC0C2"}, "LAS": {"primary": "#702F8A", "secondary": "#FFC72C"}, "MIN": {"primary": "#78BE20", "secondary": "#236192"}, "NYL": {"primary": "#FF671F", "secondary": "#6ECEB2"}, "PHX": {"primary": "#201747", "secondary": "#E56020"}, "SEA": {"primary": "#2C5234", "secondary": "#F0F1F2"}, "WAS": {"primary": "#002B5C", "secondary": "#E31837"}, "GSW": {"primary": "#1D428A", "secondary": "#FFC72C"}}, "federated_multiverse_metrics": {"federation_status": {"active_teams": "8/13", "current_round": "Round 29", "global_mae": "2.82", "convergence": "Diverging", "privacy_budget": "1.0"}, "multiverse_ensemble": {"total_models": "9", "ensemble_mae": "3.23", "model_agreement": "0.89", "prediction_confidence": "0.92", "specialization_score": "0.75"}, "expert_integration": {"player_mappings": "465", "team_mappings": "13", "role_categories": "3", "context_scenarios": "5", "coverage_rate": "100%"}}, "team_federation_status": {"ATL": {"status": "Active", "local_mae": "4.19", "samples_trained": "1,210", "participation_rate": "78.6%", "last_update": "7 min ago", "data_quality": "86.0%", "expert_coverage": "98.5%"}, "CHI": {"status": "Active", "local_mae": "4.46", "samples_trained": "1,059", "participation_rate": "92.1%", "last_update": "1 min ago", "data_quality": "90.6%", "expert_coverage": "97.6%"}, "CON": {"status": "Active", "local_mae": "4.41", "samples_trained": "1,666", "participation_rate": "72.4%", "last_update": "5 min ago", "data_quality": "89.8%", "expert_coverage": "94.1%"}, "DAL": {"status": "Inactive", "local_mae": "N/A", "samples_trained": "0", "participation_rate": "0%", "last_update": "8 hrs ago", "data_quality": "N/A", "expert_coverage": "N/A"}, "GSV": {"status": "Active", "local_mae": "4.10", "samples_trained": "2,338", "participation_rate": "76.1%", "last_update": "3 min ago", "data_quality": "91.4%", "expert_coverage": "92.4%"}, "IND": {"status": "Active", "local_mae": "2.81", "samples_trained": "2,961", "participation_rate": "93.7%", "last_update": "10 min ago", "data_quality": "89.7%", "expert_coverage": "91.4%"}, "LAS": {"status": "Active", "local_mae": "3.81", "samples_trained": "1,058", "participation_rate": "76.4%", "last_update": "10 min ago", "data_quality": "92.8%", "expert_coverage": "95.6%"}, "LV": {"status": "Active", "local_mae": "2.65", "samples_trained": "2,813", "participation_rate": "78.4%", "last_update": "11 min ago", "data_quality": "86.9%", "expert_coverage": "98.7%"}, "MIN": {"status": "Active", "local_mae": "2.87", "samples_trained": "2,387", "participation_rate": "84.3%", "last_update": "3 min ago", "data_quality": "93.1%", "expert_coverage": "93.2%"}, "NYL": {"status": "Active", "local_mae": "4.03", "samples_trained": "2,303", "participation_rate": "84.5%", "last_update": "11 min ago", "data_quality": "90.9%", "expert_coverage": "92.8%"}, "PHO": {"status": "Active", "local_mae": "2.43", "samples_trained": "2,092", "participation_rate": "75.6%", "last_update": "6 min ago", "data_quality": "88.3%", "expert_coverage": "97.0%"}, "SEA": {"status": "Active", "local_mae": "4.08", "samples_trained": "2,064", "participation_rate": "74.3%", "last_update": "9 min ago", "data_quality": "90.0%", "expert_coverage": "92.7%"}, "WAS": {"status": "Active", "local_mae": "2.44", "samples_trained": "2,845", "participation_rate": "83.8%", "last_update": "2 min ago", "data_quality": "100.0%", "expert_coverage": "90.6%"}}, "multiverse_model_performance": {"PossessionBasedModel": {"mae": "2.66", "weight": "0.060", "accuracy_trend": "Improving", "specialization_score": "0.63", "contribution": "19.8%", "training_rounds": "19"}, "HighLeverageModel": {"mae": "3.16", "weight": "0.111", "accuracy_trend": "Stable", "specialization_score": "0.65", "contribution": "5.8%", "training_rounds": "41"}, "TeamDynamicsModel": {"mae": "3.34", "weight": "0.083", "accuracy_trend": "Stable", "specialization_score": "0.91", "contribution": "16.1%", "training_rounds": "14"}, "ContextualPerformanceModel": {"mae": "2.87", "weight": "0.067", "accuracy_trend": "Improving", "specialization_score": "0.61", "contribution": "19.5%", "training_rounds": "34"}, "CumulativeFatigueModel": {"mae": "4.33", "weight": "0.192", "accuracy_trend": "Declining", "specialization_score": "0.83", "contribution": "19.8%", "training_rounds": "34"}, "InjuryImpactModel": {"mae": "3.11", "weight": "0.134", "accuracy_trend": "Improving", "specialization_score": "0.60", "contribution": "9.1%", "training_rounds": "13"}, "CoachingStyleModel": {"mae": "2.67", "weight": "0.185", "accuracy_trend": "Improving", "specialization_score": "0.78", "contribution": "11.2%", "training_rounds": "37"}, "ArenaEffectModel": {"mae": "3.13", "weight": "0.113", "accuracy_trend": "Improving", "specialization_score": "0.74", "contribution": "5.8%", "training_rounds": "44"}, "WeatherImpactModel": {"mae": "4.72", "weight": "0.188", "accuracy_trend": "Improving", "specialization_score": "0.67", "contribution": "16.0%", "training_rounds": "21"}}}