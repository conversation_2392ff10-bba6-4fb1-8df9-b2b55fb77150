#!/usr/bin/env python3
"""
⚙️ FEDERATED LEARNING CONFIGURATION
===================================

Centralized configuration for WNBA federated learning to ensure:
- Consistent model architecture across all clients
- Synchronized hyperparameters
- Standardized training configuration
- Robust aggregation settings

FEDERATED LEARNING PRINCIPLE:
- All clients use identical model architecture
- Synchronized hyperparameters across teams
- Consistent training procedures
- Centralized configuration management
"""

import json
from pathlib import Path
from typing import Dict, Any, List
import logging

logger = logging.getLogger(__name__)

class FederatedConfig:
    """
    Centralized configuration for WNBA federated learning.
    Ensures all clients use identical settings.
    """
    
    def __init__(self):
        # Model Architecture (must be identical across all clients)
        self.MODEL_CONFIG = {
            'input_dim': 25,  # Updated for enhanced dataset with all features
            'hidden_dims': [128, 64, 32],
            'output_dim': 1,
            'dropout_rate': 0.3,
            'activation': 'relu',
            'batch_norm': True,
            'model_type': 'neural_network'
        }
        
        # Training Hyperparameters (synchronized across all clients)
        self.TRAINING_CONFIG = {
            'learning_rate': 0.001,
            'batch_size': 64,
            'local_epochs': 5,  # Epochs per federated round
            'optimizer': 'adam',
            'loss_function': 'mse',
            'weight_decay': 1e-5,
            'gradient_clipping': 1.0
        }
        
        # Federated Learning Settings
        self.FEDERATED_CONFIG = {
            'num_rounds': 10,
            'min_clients': 2,
            'max_clients': 13,  # All WNBA teams
            'client_fraction': 1.0,  # Use all available clients
            'aggregation_strategy': 'fedavg',
            'server_timeout': 300,  # 5 minutes
            'client_timeout': 180   # 3 minutes
        }
        
        # Data Configuration
        self.DATA_CONFIG = {
            'temporal_split': True,
            'train_years': list(range(2015, 2023)),  # 2015-2022
            'val_years': [2023],
            'test_years': [2024, 2025],
            'feature_standardization': True,
            'target_scaling': False
        }
        
        # Aggregation Weights (based on data quality/size)
        self.TEAM_WEIGHTS = {
            'ATL': 1.0, 'CHI': 1.0, 'CON': 1.0, 'DAL': 1.0,
            'IND': 1.0, 'LAS': 1.0, 'LV': 1.0, 'MIN': 1.0,
            'NYL': 1.0, 'PHO': 1.0, 'SEA': 1.0, 'WAS': 1.0,
            'GSV': 0.5  # Lower weight for GSV (limited historical data)
        }
        
        # Monitoring and Logging
        self.MONITORING_CONFIG = {
            'log_level': 'INFO',
            'metrics_tracking': True,
            'convergence_threshold': 0.001,
            'early_stopping_patience': 3,
            'save_checkpoints': True,
            'checkpoint_frequency': 2  # Every 2 rounds
        }
        
        # Security and Privacy
        self.PRIVACY_CONFIG = {
            'differential_privacy': False,
            'noise_multiplier': 0.1,
            'max_grad_norm': 1.0,
            'secure_aggregation': False,
            'data_isolation_verified': True
        }
        
        logger.info("⚙️ Federated configuration initialized")
    
    def get_model_config(self) -> Dict[str, Any]:
        """Get model architecture configuration."""
        return self.MODEL_CONFIG.copy()
    
    def get_training_config(self) -> Dict[str, Any]:
        """Get training hyperparameters."""
        return self.TRAINING_CONFIG.copy()
    
    def get_federated_config(self) -> Dict[str, Any]:
        """Get federated learning settings."""
        return self.FEDERATED_CONFIG.copy()
    
    def get_data_config(self) -> Dict[str, Any]:
        """Get data configuration."""
        return self.DATA_CONFIG.copy()
    
    def get_team_weight(self, team_id: str) -> float:
        """Get aggregation weight for a specific team."""
        return self.TEAM_WEIGHTS.get(team_id, 1.0)
    
    def get_all_team_weights(self) -> Dict[str, float]:
        """Get all team aggregation weights."""
        return self.TEAM_WEIGHTS.copy()
    
    def update_input_dim(self, input_dim: int):
        """Update model input dimension based on actual features."""
        self.MODEL_CONFIG['input_dim'] = input_dim
        logger.info(f"   🔧 Updated input_dim to {input_dim}")
    
    def save_config(self, filepath: str = "federated_config.json"):
        """Save configuration to file for sharing across clients."""
        config_data = {
            'model_config': self.MODEL_CONFIG,
            'training_config': self.TRAINING_CONFIG,
            'federated_config': self.FEDERATED_CONFIG,
            'data_config': self.DATA_CONFIG,
            'team_weights': self.TEAM_WEIGHTS,
            'monitoring_config': self.MONITORING_CONFIG,
            'privacy_config': self.PRIVACY_CONFIG
        }
        
        with open(filepath, 'w') as f:
            json.dump(config_data, f, indent=2)
        
        logger.info(f"💾 Configuration saved to {filepath}")
    
    def load_config(self, filepath: str = "federated_config.json"):
        """Load configuration from file."""
        if not Path(filepath).exists():
            logger.warning(f"⚠️ Config file {filepath} not found, using defaults")
            return
        
        with open(filepath, 'r') as f:
            config_data = json.load(f)
        
        self.MODEL_CONFIG.update(config_data.get('model_config', {}))
        self.TRAINING_CONFIG.update(config_data.get('training_config', {}))
        self.FEDERATED_CONFIG.update(config_data.get('federated_config', {}))
        self.DATA_CONFIG.update(config_data.get('data_config', {}))
        self.TEAM_WEIGHTS.update(config_data.get('team_weights', {}))
        self.MONITORING_CONFIG.update(config_data.get('monitoring_config', {}))
        self.PRIVACY_CONFIG.update(config_data.get('privacy_config', {}))
        
        logger.info(f"📁 Configuration loaded from {filepath}")
    
    def validate_config(self) -> bool:
        """Validate configuration for federated learning."""
        logger.info("🔍 Validating federated configuration...")
        
        valid = True
        
        # Check model config
        if self.MODEL_CONFIG['input_dim'] <= 0:
            logger.error("❌ Invalid input_dim")
            valid = False
        
        # Check training config
        if self.TRAINING_CONFIG['learning_rate'] <= 0:
            logger.error("❌ Invalid learning_rate")
            valid = False
        
        if self.TRAINING_CONFIG['local_epochs'] <= 0:
            logger.error("❌ Invalid local_epochs")
            valid = False
        
        # Check federated config
        if self.FEDERATED_CONFIG['num_rounds'] <= 0:
            logger.error("❌ Invalid num_rounds")
            valid = False
        
        if self.FEDERATED_CONFIG['min_clients'] <= 0:
            logger.error("❌ Invalid min_clients")
            valid = False
        
        # Check team weights
        if len(self.TEAM_WEIGHTS) != 13:
            logger.warning(f"⚠️ Expected 13 team weights, got {len(self.TEAM_WEIGHTS)}")
        
        if valid:
            logger.info("✅ Configuration validation passed")
        else:
            logger.error("❌ Configuration validation failed")
        
        return valid
    
    def get_summary(self) -> str:
        """Get configuration summary for logging."""
        return f"""
🔧 FEDERATED LEARNING CONFIGURATION SUMMARY
============================================
📊 Model: {self.MODEL_CONFIG['hidden_dims']} -> {self.MODEL_CONFIG['output_dim']}
🎯 Training: LR={self.TRAINING_CONFIG['learning_rate']}, Epochs={self.TRAINING_CONFIG['local_epochs']}, Batch={self.TRAINING_CONFIG['batch_size']}
🤝 Federated: {self.FEDERATED_CONFIG['num_rounds']} rounds, {self.FEDERATED_CONFIG['min_clients']}-{self.FEDERATED_CONFIG['max_clients']} clients
📅 Data: {self.DATA_CONFIG['train_years'][0]}-{self.DATA_CONFIG['train_years'][-1]} train, {self.DATA_CONFIG['val_years']} val, {self.DATA_CONFIG['test_years']} test
🏀 Teams: {len(self.TEAM_WEIGHTS)} teams with weighted aggregation
🔒 Privacy: Isolation verified, DP={self.PRIVACY_CONFIG['differential_privacy']}
"""

# Global configuration instance
federated_config = FederatedConfig()

def get_federated_config() -> FederatedConfig:
    """Get the global federated configuration instance."""
    return federated_config

def initialize_federated_config(config_file: str = "federated_config.json") -> FederatedConfig:
    """Initialize federated configuration from file or defaults."""
    config = FederatedConfig()
    config.load_config(config_file)
    
    if not config.validate_config():
        logger.error("❌ Configuration validation failed")
        raise ValueError("Invalid federated configuration")
    
    return config

if __name__ == "__main__":
    # Test and save federated configuration
    print("⚙️ TESTING FEDERATED CONFIGURATION")
    print("=" * 45)
    
    config = FederatedConfig()
    
    # Display summary
    print(config.get_summary())
    
    # Validate
    if config.validate_config():
        print("✅ Configuration validation passed")
        
        # Save to file
        config.save_config()
        print("💾 Configuration saved to federated_config.json")
        
        print("\n🏆 FEDERATED CONFIGURATION READY!")
        print("   ✅ Consistent model architecture")
        print("   ✅ Synchronized hyperparameters")
        print("   ✅ Robust aggregation settings")
        print("   ✅ All 13 WNBA teams supported")
    else:
        print("❌ Configuration validation failed")
