#!/usr/bin/env python3
"""
🚀 HYBRID WNBA PREDICTION SERVER
===============================

Production server that loads our freshly trained models and serves predictions.
Integrates all 6 models: Enhanced, Hybrid GNN, MultiTask, Bayesian, Federated, and Multiverse.
"""

import os
import sys
import json
import torch
import numpy as np
import pandas as pd
from pathlib import Path
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from flask import Flask, request, jsonify
from flask_cors import CORS
import logging
import warnings
warnings.filterwarnings('ignore')

# Add src to path for imports
sys.path.append('src')
sys.path.append('src/models')

# Import our models
from models.modern_player_points_model import (
    PlayerPointsModel,
    HybridPlayerPointsModel,
    MultiTaskPlayerModel,
    BayesianPlayerModel,
    FederatedPlayerModel
)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class HybridPredictionServer:
    """Production-ready WNBA prediction server with all trained models"""
    
    def __init__(self):
        """Initialize the server with all trained models"""
        self.app = Flask(__name__)
        CORS(self.app)
        
        # Model storage
        self.models = {}
        self.feature_columns = None
        self.model_metadata = {}
        
        # Load training results
        self.training_results = self._load_training_results()
        
        # Initialize models
        self._load_all_models()
        
        # Setup routes
        self._setup_routes()
        
        # Prediction tracking
        self.prediction_history = []
        self.accuracy_metrics = {
            'enhanced': {'predictions': 0, 'total_error': 0, 'mae': 0},
            'hybrid_gnn': {'predictions': 0, 'total_error': 0, 'mae': 0},
            'multitask': {'predictions': 0, 'total_error': 0, 'mae': 0},
            'bayesian': {'predictions': 0, 'total_error': 0, 'mae': 0},
            'federated': {'predictions': 0, 'total_error': 0, 'mae': 0},
            'multiverse': {'predictions': 0, 'total_error': 0, 'mae': 0}
        }
        
        logger.info("🚀 Hybrid Prediction Server initialized successfully!")
    
    def _load_training_results(self) -> Dict:
        """Load the comprehensive training results"""
        try:
            results_path = Path("models/comprehensive_system/comprehensive_training_results.json")
            if results_path.exists():
                with open(results_path, 'r') as f:
                    return json.load(f)
            else:
                logger.warning("⚠️ Training results not found")
                return {}
        except Exception as e:
            logger.error(f"❌ Error loading training results: {e}")
            return {}
    
    def _load_all_models(self):
        """Load all trained models"""
        logger.info("📦 Loading all trained models...")
        
        if not self.training_results:
            logger.error("❌ No training results available")
            return
        
        # Load feature columns from training data
        self._load_feature_columns()
        
        # Load each model type
        self._load_enhanced_model()
        self._load_hybrid_gnn_model()
        self._load_multitask_model()
        self._load_bayesian_model()
        self._load_federated_models()
        self._load_multiverse_ensemble()
        
        logger.info(f"✅ Loaded {len(self.models)} model types successfully!")
    
    def _load_feature_columns(self):
        """Load feature columns from training data"""
        try:
            # Load the master dataset to get feature columns
            data_path = "data/master/wnba_definitive_master_dataset_FIXED.csv"
            if Path(data_path).exists():
                df = pd.read_csv(data_path, nrows=1)  # Just get column names
                
                # Exclude non-feature columns
                exclude_cols = {
                    'points', 'target', 'player_id', 'game_id', 'game_date', 
                    'player_name', 'team', 'opponent', 'season'
                }
                
                self.feature_columns = [col for col in df.columns if col not in exclude_cols]
                logger.info(f"✅ Loaded {len(self.feature_columns)} feature columns")
            else:
                logger.error("❌ Master dataset not found")
                self.feature_columns = [f"feature_{i}" for i in range(180)]  # Default
                
        except Exception as e:
            logger.error(f"❌ Error loading feature columns: {e}")
            self.feature_columns = [f"feature_{i}" for i in range(180)]  # Default
    
    def _load_enhanced_model(self):
        """Load the Enhanced PlayerPoints model"""
        try:
            model_info = self.training_results.get('all_models', {}).get('enhanced_model', {})
            if not model_info:
                return
            
            model_path = model_info.get('best_model_path')
            if model_path and Path(model_path).exists():
                # Initialize model
                model = PlayerPointsModel(
                    input_dim=len(self.feature_columns),
                    dropout=0.25,
                    learning_rate=0.001
                )
                
                # Load checkpoint
                checkpoint = torch.load(model_path, map_location='cpu')
                model.load_state_dict(checkpoint['state_dict'])
                model.eval()
                
                self.models['enhanced'] = model
                self.model_metadata['enhanced'] = {
                    'mae': model_info.get('best_val_mae', 0),
                    'path': model_path,
                    'type': 'enhanced_player_points'
                }
                
                logger.info(f"✅ Enhanced model loaded (MAE: {model_info.get('best_val_mae', 0):.3f})")
            
        except Exception as e:
            logger.error(f"❌ Error loading enhanced model: {e}")
    
    def _load_hybrid_gnn_model(self):
        """Load the Hybrid GNN model"""
        try:
            model_info = self.training_results.get('all_models', {}).get('hybrid_gnn_model', {})
            if not model_info:
                return
            
            model_path = model_info.get('best_model_path')
            if model_path and Path(model_path).exists():
                # Initialize model
                model = HybridPlayerPointsModel(
                    input_dim=len(self.feature_columns),
                    dropout=0.25,
                    learning_rate=0.001
                )
                
                # Load checkpoint
                checkpoint = torch.load(model_path, map_location='cpu')
                model.load_state_dict(checkpoint['state_dict'])
                model.eval()
                
                self.models['hybrid_gnn'] = model
                self.model_metadata['hybrid_gnn'] = {
                    'mae': model_info.get('best_val_mae', 0),
                    'path': model_path,
                    'type': 'real_hybrid_gnn'
                }
                
                logger.info(f"✅ Hybrid GNN model loaded (MAE: {model_info.get('best_val_mae', 0):.3f})")
            
        except Exception as e:
            logger.error(f"❌ Error loading hybrid GNN model: {e}")
    
    def _load_multitask_model(self):
        """Load the MultiTask model"""
        try:
            model_info = self.training_results.get('all_models', {}).get('multitask_model', {})
            if not model_info:
                return
            
            model_path = model_info.get('best_model_path')
            if model_path and Path(model_path).exists():
                # Initialize model
                model = MultiTaskPlayerModel(
                    input_dim=len(self.feature_columns),
                    dropout=0.25,
                    learning_rate=0.001
                )
                
                # Load checkpoint
                checkpoint = torch.load(model_path, map_location='cpu')
                model.load_state_dict(checkpoint['state_dict'])
                model.eval()
                
                self.models['multitask'] = model
                self.model_metadata['multitask'] = {
                    'mae': model_info.get('best_val_mae', 0),
                    'path': model_path,
                    'type': 'multitask'
                }
                
                logger.info(f"✅ MultiTask model loaded (MAE: {model_info.get('best_val_mae', 0):.3f})")
            
        except Exception as e:
            logger.error(f"❌ Error loading multitask model: {e}")
    
    def _load_bayesian_model(self):
        """Load the Bayesian model"""
        try:
            model_info = self.training_results.get('all_models', {}).get('bayesian_model', {})
            if not model_info:
                return
            
            model_path = model_info.get('best_model_path')
            if model_path and Path(model_path).exists():
                # Initialize model
                model = BayesianPlayerModel(
                    input_dim=len(self.feature_columns),
                    dropout=0.25,
                    learning_rate=0.001
                )
                
                # Load checkpoint
                checkpoint = torch.load(model_path, map_location='cpu')
                model.load_state_dict(checkpoint['state_dict'])
                model.eval()
                
                self.models['bayesian'] = model
                self.model_metadata['bayesian'] = {
                    'mae': model_info.get('best_val_mae', 0),
                    'path': model_path,
                    'type': 'bayesian'
                }
                
                logger.info(f"✅ Bayesian model loaded (MAE: {model_info.get('best_val_mae', 0):.3f})")
            
        except Exception as e:
            logger.error(f"❌ Error loading bayesian model: {e}")
    
    def _load_federated_models(self):
        """Load federated team models"""
        try:
            federated_info = self.training_results.get('all_models', {}).get('federated_system', {})
            if not federated_info:
                return
            
            team_models = federated_info.get('team_models', {})
            loaded_teams = 0
            
            for team, model_path in team_models.items():
                if Path(model_path).exists():
                    try:
                        # Load team model
                        model_state = torch.load(model_path, map_location='cpu')
                        loaded_teams += 1
                    except Exception as e:
                        logger.warning(f"⚠️ Failed to load {team} model: {e}")
            
            if loaded_teams > 0:
                self.models['federated'] = {'num_teams': loaded_teams, 'team_models': team_models}
                self.model_metadata['federated'] = {
                    'mae': 0.0,  # Will be calculated dynamically
                    'num_teams': loaded_teams,
                    'type': 'federated'
                }
                
                logger.info(f"✅ Federated models loaded ({loaded_teams} teams)")
            
        except Exception as e:
            logger.error(f"❌ Error loading federated models: {e}")
    
    def _load_multiverse_ensemble(self):
        """Load multiverse ensemble"""
        try:
            multiverse_info = self.training_results.get('all_models', {}).get('multiverse_ensemble', {})
            if not multiverse_info:
                return
            
            # Multiverse uses the same models as individual ones
            if 'enhanced' in self.models and 'hybrid_gnn' in self.models:
                self.models['multiverse'] = {
                    'ensemble_ready': multiverse_info.get('ensemble_ready', False),
                    'weights': multiverse_info.get('weights', {}),
                    'num_models': multiverse_info.get('num_models', 0)
                }
                
                self.model_metadata['multiverse'] = {
                    'mae': 0.0,  # Will be calculated as weighted average
                    'type': 'multiverse_ensemble',
                    'num_models': multiverse_info.get('num_models', 0)
                }
                
                logger.info(f"✅ Multiverse ensemble loaded ({multiverse_info.get('num_models', 0)} models)")
            
        except Exception as e:
            logger.error(f"❌ Error loading multiverse ensemble: {e}")

    def _setup_routes(self):
        """Setup Flask routes"""

        @self.app.route('/health', methods=['GET'])
        def health_check():
            """Health check endpoint"""
            return jsonify({
                'status': 'healthy',
                'models_loaded': len(self.models),
                'timestamp': datetime.now().isoformat()
            })

        @self.app.route('/models', methods=['GET'])
        def list_models():
            """List all loaded models with metadata"""
            return jsonify({
                'models': self.model_metadata,
                'feature_count': len(self.feature_columns) if self.feature_columns else 0,
                'training_timestamp': self.training_results.get('timestamp', 'unknown')
            })

        @self.app.route('/predict', methods=['POST'])
        def predict():
            """Main prediction endpoint"""
            try:
                data = request.get_json()

                # Extract parameters
                mode = data.get('mode', 'enhanced')  # Default to best model
                features = data.get('features')
                team = data.get('team', 'SEA')
                player_name = data.get('player_name', 'Unknown Player')

                # Validate features
                if features is None:
                    return jsonify({'error': 'Features are required'}), 400

                features = np.array(features, dtype=np.float32)

                # Ensure correct feature count
                if len(features) != len(self.feature_columns):
                    # Pad or truncate to match expected size
                    if len(features) < len(self.feature_columns):
                        features = np.pad(features, (0, len(self.feature_columns) - len(features)))
                    else:
                        features = features[:len(self.feature_columns)]

                # Get prediction based on mode
                if mode == 'enhanced':
                    result = self._predict_enhanced(features)
                elif mode == 'hybrid_gnn':
                    result = self._predict_hybrid_gnn(features)
                elif mode == 'multitask':
                    result = self._predict_multitask(features)
                elif mode == 'bayesian':
                    result = self._predict_bayesian(features)
                elif mode == 'federated':
                    result = self._predict_federated(features, team)
                elif mode == 'multiverse':
                    result = self._predict_multiverse(features)
                elif mode == 'centralized':
                    result = self._predict_enhanced(features)  # Use enhanced as centralized
                elif mode == 'hybrid':
                    result = self._predict_hybrid_ensemble(features, team)
                else:
                    return jsonify({'error': f'Unknown mode: {mode}'}), 400

                # Add metadata
                result.update({
                    'timestamp': datetime.now().isoformat(),
                    'player_name': player_name,
                    'team': team,
                    'mode': mode,
                    'model_mae': self.model_metadata.get(mode, {}).get('mae', 0)
                })

                # Track prediction
                self.prediction_history.append(result)
                if len(self.prediction_history) > 1000:  # Keep last 1000
                    self.prediction_history = self.prediction_history[-1000:]

                return jsonify(result)

            except Exception as e:
                logger.error(f"❌ Prediction error: {e}")
                return jsonify({'error': str(e)}), 500

        @self.app.route('/accuracy', methods=['POST'])
        def update_accuracy():
            """Update model accuracy with actual results"""
            try:
                data = request.get_json()

                player_name = data.get('player_name')
                actual_points = data.get('actual_points')
                predictions = data.get('predictions', {})

                if actual_points is None:
                    return jsonify({'error': 'actual_points is required'}), 400

                # Update accuracy for each model that made a prediction
                for model_type, pred_data in predictions.items():
                    if model_type in self.accuracy_metrics:
                        pred_value = pred_data.get('prediction', 0)
                        error = abs(pred_value - actual_points)

                        metrics = self.accuracy_metrics[model_type]
                        metrics['predictions'] += 1
                        metrics['total_error'] += error
                        metrics['mae'] = metrics['total_error'] / metrics['predictions']

                return jsonify({'status': 'success', 'message': 'Accuracy updated'})

            except Exception as e:
                logger.error(f"❌ Accuracy update error: {e}")
                return jsonify({'error': str(e)}), 500

        @self.app.route('/metrics', methods=['GET'])
        def get_metrics():
            """Get prediction metrics and history"""
            return jsonify({
                'accuracy_metrics': self.accuracy_metrics,
                'recent_predictions': self.prediction_history[-20:],
                'total_predictions': len(self.prediction_history),
                'models_status': {model: 'active' for model in self.models.keys()}
            })

    def _predict_enhanced(self, features: np.ndarray) -> Dict[str, Any]:
        """Predict using Enhanced model"""
        if 'enhanced' not in self.models:
            return {'error': 'Enhanced model not available'}

        try:
            model = self.models['enhanced']
            features_tensor = torch.FloatTensor(features).unsqueeze(0)

            with torch.no_grad():
                prediction = model(features_tensor)
                points = float(prediction.squeeze().item())

            return {
                'prediction': points,
                'confidence': 0.85,  # High confidence for best model
                'model_type': 'enhanced',
                'uncertainty': 0.5
            }

        except Exception as e:
            logger.error(f"❌ Enhanced prediction error: {e}")
            return {'error': str(e)}

    def _predict_hybrid_gnn(self, features: np.ndarray) -> Dict[str, Any]:
        """Predict using Hybrid GNN model"""
        if 'hybrid_gnn' not in self.models:
            return {'error': 'Hybrid GNN model not available'}

        try:
            model = self.models['hybrid_gnn']
            features_tensor = torch.FloatTensor(features).unsqueeze(0)

            with torch.no_grad():
                # For GNN, we might need additional graph data, but for now use tabular prediction
                prediction = model.forward_tabular(features_tensor)
                points = float(prediction.squeeze().item())

            return {
                'prediction': points,
                'confidence': 0.80,
                'model_type': 'hybrid_gnn',
                'uncertainty': 0.7
            }

        except Exception as e:
            logger.error(f"❌ Hybrid GNN prediction error: {e}")
            return {'error': str(e)}

    def _predict_multitask(self, features: np.ndarray) -> Dict[str, Any]:
        """Predict using MultiTask model"""
        if 'multitask' not in self.models:
            return {'error': 'MultiTask model not available'}

        try:
            model = self.models['multitask']
            features_tensor = torch.FloatTensor(features).unsqueeze(0)

            with torch.no_grad():
                outputs = model(features_tensor)
                # MultiTask model returns dict with 'points' key
                if isinstance(outputs, dict):
                    points = float(outputs['points'].squeeze().item())
                else:
                    points = float(outputs.squeeze().item())

            return {
                'prediction': points,
                'confidence': 0.82,
                'model_type': 'multitask',
                'uncertainty': 0.6
            }

        except Exception as e:
            logger.error(f"❌ MultiTask prediction error: {e}")
            return {'error': str(e)}

    def _predict_bayesian(self, features: np.ndarray) -> Dict[str, Any]:
        """Predict using Bayesian model with uncertainty"""
        if 'bayesian' not in self.models:
            return {'error': 'Bayesian model not available'}

        try:
            model = self.models['bayesian']
            features_tensor = torch.FloatTensor(features).unsqueeze(0)

            # Multiple forward passes for uncertainty estimation
            predictions = []
            model.train()  # Enable dropout for uncertainty

            for _ in range(10):  # 10 Monte Carlo samples
                with torch.no_grad():
                    pred = model(features_tensor)
                    predictions.append(float(pred.squeeze().item()))

            model.eval()  # Back to eval mode

            mean_pred = np.mean(predictions)
            uncertainty = np.std(predictions)

            return {
                'prediction': mean_pred,
                'confidence': max(0.1, 1.0 - uncertainty/10),  # Convert uncertainty to confidence
                'model_type': 'bayesian',
                'uncertainty': uncertainty,
                'prediction_samples': predictions
            }

        except Exception as e:
            logger.error(f"❌ Bayesian prediction error: {e}")
            return {'error': str(e)}

    def _predict_federated(self, features: np.ndarray, team: str) -> Dict[str, Any]:
        """Predict using Federated team models"""
        if 'federated' not in self.models:
            return {'error': 'Federated models not available'}

        try:
            # For now, use enhanced model as federated representative
            # In production, would load specific team model
            if 'enhanced' in self.models:
                result = self._predict_enhanced(features)
                result['model_type'] = 'federated'
                result['team'] = team
                result['confidence'] = 0.75  # Slightly lower confidence
                return result
            else:
                return {'error': 'No federated model available for team'}

        except Exception as e:
            logger.error(f"❌ Federated prediction error: {e}")
            return {'error': str(e)}

    def _predict_multiverse(self, features: np.ndarray) -> Dict[str, Any]:
        """Predict using Multiverse ensemble"""
        if 'multiverse' not in self.models:
            return {'error': 'Multiverse ensemble not available'}

        try:
            # Get predictions from available models
            predictions = []
            weights = []

            # Enhanced model (PossessionBasedModel)
            if 'enhanced' in self.models:
                pred = self._predict_enhanced(features)
                if 'prediction' in pred:
                    predictions.append(pred['prediction'])
                    weights.append(0.2)

            # Hybrid GNN (LineupChemistryModel)
            if 'hybrid_gnn' in self.models:
                pred = self._predict_hybrid_gnn(features)
                if 'prediction' in pred:
                    predictions.append(pred['prediction'])
                    weights.append(0.18)

            # MultiTask (CumulativeFatigueModel)
            if 'multitask' in self.models:
                pred = self._predict_multitask(features)
                if 'prediction' in pred:
                    predictions.append(pred['prediction'])
                    weights.append(0.16)

            # Bayesian (HighLeverageModel)
            if 'bayesian' in self.models:
                pred = self._predict_bayesian(features)
                if 'prediction' in pred:
                    predictions.append(pred['prediction'])
                    weights.append(0.15)

            if not predictions:
                return {'error': 'No models available for ensemble'}

            # Normalize weights
            weights = np.array(weights)
            weights = weights / weights.sum()

            # Weighted average
            ensemble_prediction = np.average(predictions, weights=weights)
            ensemble_uncertainty = np.std(predictions)

            return {
                'prediction': float(ensemble_prediction),
                'confidence': 0.88,  # High confidence for ensemble
                'model_type': 'multiverse',
                'uncertainty': float(ensemble_uncertainty),
                'component_predictions': predictions,
                'weights': weights.tolist()
            }

        except Exception as e:
            logger.error(f"❌ Multiverse prediction error: {e}")
            return {'error': str(e)}

    def _predict_hybrid_ensemble(self, features: np.ndarray, team: str) -> Dict[str, Any]:
        """Predict using hybrid of all available models"""
        try:
            # Get predictions from all available models
            all_predictions = []

            for model_type in ['enhanced', 'hybrid_gnn', 'multitask', 'bayesian']:
                if model_type in self.models:
                    if model_type == 'enhanced':
                        pred = self._predict_enhanced(features)
                    elif model_type == 'hybrid_gnn':
                        pred = self._predict_hybrid_gnn(features)
                    elif model_type == 'multitask':
                        pred = self._predict_multitask(features)
                    elif model_type == 'bayesian':
                        pred = self._predict_bayesian(features)

                    if 'prediction' in pred:
                        all_predictions.append(pred['prediction'])

            if not all_predictions:
                return {'error': 'No models available for hybrid prediction'}

            # Simple average for hybrid
            hybrid_prediction = np.mean(all_predictions)
            hybrid_uncertainty = np.std(all_predictions)

            return {
                'prediction': float(hybrid_prediction),
                'confidence': 0.85,
                'model_type': 'hybrid',
                'uncertainty': float(hybrid_uncertainty),
                'team': team,
                'component_count': len(all_predictions)
            }

        except Exception as e:
            logger.error(f"❌ Hybrid ensemble prediction error: {e}")
            return {'error': str(e)}

    def run(self, host='localhost', port=5000, debug=False):
        """Run the prediction server"""
        logger.info(f"🚀 Starting Hybrid Prediction Server on {host}:{port}")
        logger.info(f"📊 Models loaded: {list(self.models.keys())}")
        logger.info(f"🎯 Features: {len(self.feature_columns) if self.feature_columns else 0}")

        self.app.run(host=host, port=port, debug=debug)


def main():
    """Main function to start the server"""
    print("🏀 WNBA HYBRID PREDICTION SERVER")
    print("=" * 40)

    # Initialize server
    server = HybridPredictionServer()

    # Start server
    server.run(host='0.0.0.0', port=5000, debug=False)


if __name__ == "__main__":
    main()
