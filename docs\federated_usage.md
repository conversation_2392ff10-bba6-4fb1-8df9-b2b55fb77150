# WNBA Federated Learning Usage Guide

## Quick Start

### 1. DDP (Distributed Data Parallel)
```bash
# Single machine, multiple GPUs
./launch_ddp_training.sh

# Or manually:
torchrun --nproc_per_node=2 train_model_1_real_data.py --federated
```

### 2. Flower Framework
```bash
# Terminal 1: Start server
python flower_config.py

# Terminal 2-4: Start clients (different teams)
python launch_flower_training.py --team ATL
python launch_flower_training.py --team CHI  
python launch_flower_training.py --team NYL
```

### 3. Multi-Node Setup
```bash
# Node 1 (master)
torchrun --nproc_per_node=2 --nnodes=2 --node_rank=0 \
         --master_addr=************* --master_port=29500 \
         train_model_1_real_data.py --federated

# Node 2 (worker)  
torchrun --nproc_per_node=2 --nnodes=2 --node_rank=1 \
         --master_addr=************* --master_port=29500 \
         train_model_1_real_data.py --federated
```

## Features
- ✅ Privacy-preserving training
- ✅ Multi-team collaboration  
- ✅ Real WNBA data integration
- ✅ Advanced model architectures
- ✅ Automatic model synchronization
