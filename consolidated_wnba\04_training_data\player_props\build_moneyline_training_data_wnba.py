import os
import pandas as pd
import asyncio
import logging
from vault_oracle.wells.nba_api_connector import BasketballDataConnector

"""
build_moneyline_training_data_wnba.py

Fetches historical WNBA games and builds a training dataset for Moneyline (game winner) prediction.
- Features: team stats, recent form, home/away, etc.
- Label: 1 if home team wins, 0 if away team wins.
- Output: CSV file ready for model training.
"""


# --- CONFIG ---
SEASON_START = 2005  # e.g., 2005 for 2005 season
SEASON_END = 2024  # e.g., 2024 for 2024 season
OUTPUT_CSV = "data/moneyline_training_data_wnba.csv"

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def fetch_games_and_features():
    """
    Main function to fetch WNBA games and build training features.
    Enhanced with better error handling and logging.
    """
    try:
        connector = BasketballDataConnector(league="wnba")
        logger.info(f"Fetching WNBA games from {SEASON_START} to {SEASON_END}...")
        
        games_list = await connector.get_historical_games_range(SEASON_START, SEASON_END)
        if not games_list or games_list[0].empty:
            logger.warning("No games found!")
            return
            
        games_df = games_list[0]
        logger.info(f"Fetched {len(games_df)} games.")

        # --- Reshape to per-game (home/away) format ---
        logger.info("Reshaping data to home/away format...")
        games_df["is_home"] = games_df["matchup"].str.contains(" vs. ")
        home_rows = games_df[games_df["is_home"]].copy()
        away_rows = games_df[~games_df["is_home"]].copy()
        
        if home_rows.empty or away_rows.empty:
            logger.error("Missing home or away games data")
            return
            
        merged = pd.merge(
            home_rows, away_rows, on=["game_id", "game_date"], suffixes=("_home", "_away")
        )
        logger.info(f"Merged {len(merged)} games successfully")

        # Add computed home_win column (1 if home win, 0 if not)
        merged["home_win"] = (
            merged["wl_home"].str.upper().map(lambda x: 1 if x == "W" else 0)
        )

        # Prepare team game logs for feature calculation
        logger.info("Preparing team game logs for feature calculation...")
        games_df["game_date"] = pd.to_datetime(games_df["game_date"])
        games_df = games_df.sort_values("game_date")
        team_game_logs = {}
        for team_id in games_df["team_id"].unique():
            team_games = games_df[games_df["team_id"] == team_id].copy()
            team_games = team_games.sort_values("game_date")
            team_game_logs[team_id] = team_games

        def get_recent_form(team_id, date, n=5):
            """Calculate recent form statistics for a team before a given date"""
            team_games = team_game_logs.get(team_id, pd.DataFrame())
            prev_games = team_games[team_games["game_date"] < date].tail(n)
            if prev_games.empty:
                return 0, 0, 0.0
            pts = prev_games["pts"]
            opp_pts = (
                prev_games["opp_pts"] if "opp_pts" in prev_games else prev_games["pts"]
            )
            wins = prev_games["wl"].apply(lambda x: 1 if x == "W" else 0)
            return pts.mean(), opp_pts.mean(), wins.mean()

        def get_rest_days(team_id, date):
            """Calculate rest days and back-to-back indicator"""
            team_games = team_game_logs.get(team_id, pd.DataFrame())
            prev_games = team_games[team_games["game_date"] < date]
            if prev_games.empty:
                return None, 0
            last_game = prev_games.iloc[-1]
            rest_days = (date - last_game["game_date"]).days
            b2b = 1 if rest_days == 1 else 0
            return rest_days, b2b

        def get_head_to_head(home_id, away_id, date, n=5):
            """Calculate head-to-head statistics between two teams"""
            h2h_games = (
                merged[
                    (
                        (
                            (merged["team_id_home"] == home_id)
                            & (merged["team_id_away"] == away_id)
                        )
                        | (
                            (merged["team_id_home"] == away_id)
                            & (merged["team_id_away"] == home_id)
                        )
                    )
                    & (pd.to_datetime(merged["game_date"]) < date)
                ]
                .sort_values("game_date")
                .tail(n)
            )
            if h2h_games.empty:
                return 0.0, 0.0
            home_wins = h2h_games.apply(
                lambda r: (
                    r["home_win"] if r["team_id_home"] == home_id else 1 - r["home_win"]
                ),
                axis=1,
            )
            margin = h2h_games.apply(
                lambda r: (
                    (r["pts_home"] - r["pts_away"])
                    if r["team_id_home"] == home_id
                    else (r["pts_away"] - r["pts_home"])
                ),
                axis=1,
            )
            return home_wins.mean(), margin.mean()

        # Build features for each game
        logger.info("Building features for each game...")
        features = []
        for idx, row in merged.iterrows():
            if idx % 100 == 0:
                logger.info(f"Processing game {idx+1}/{len(merged)}")
                
            date = pd.to_datetime(row["game_date"])
            home_id = row["team_id_home"]
            away_id = row["team_id_away"]
            
            # Recent form
            home_pts5, home_opp5, home_win5 = get_recent_form(home_id, date, 5)
            away_pts5, away_opp5, away_win5 = get_recent_form(away_id, date, 5)
            home_pts10, home_opp10, home_win10 = get_recent_form(home_id, date, 10)
            away_pts10, away_opp10, away_win10 = get_recent_form(away_id, date, 10)
            
            # Rest days
            home_rest, home_b2b = get_rest_days(home_id, date)
            away_rest, away_b2b = get_rest_days(away_id, date)
            
            # Head-to-head
            h2h_win, h2h_margin = get_head_to_head(home_id, away_id, date, 5)
            
            features.append(
                {
                    "game_id": row["game_id"],
                    "game_date": row["game_date"],
                    "home_team_id": home_id,
                    "away_team_id": away_id,
                    "home_team_pts": row["pts_home"],
                    "away_team_pts": row["pts_away"],
                    "home_win": 1 if str(row["wl_home"]).upper() == "W" else 0,
                    # Recent form
                    "home_avg_pts_5": home_pts5,
                    "home_avg_opp_pts_5": home_opp5,
                    "home_win_pct_5": home_win5,
                    "away_avg_pts_5": away_pts5,
                    "away_avg_opp_pts_5": away_opp5,
                    "away_win_pct_5": away_win5,
                    "home_avg_pts_10": home_pts10,
                    "home_avg_opp_pts_10": home_opp10,
                    "home_win_pct_10": home_win10,
                    "away_avg_pts_10": away_pts10,
                    "away_avg_opp_pts_10": away_opp10,
                    "away_win_pct_10": away_win10,
                    # Rest days                    "home_rest_days": home_rest,
                    "home_b2b": home_b2b,
                    "away_rest_days": away_rest,
                    "away_b2b": away_b2b,
                    # Head-to-head
                    "h2h_home_win_pct_5": h2h_win,
                    "h2h_home_margin_5": h2h_margin,
                    # Enhanced features
                    "home_avg_margin_5": home_pts5 - home_opp5 if home_pts5 and home_opp5 else 0,
                    "away_avg_margin_5": away_pts5 - away_opp5 if away_pts5 and away_opp5 else 0,
                    "home_avg_margin_10": home_pts10 - home_opp10 if home_pts10 and home_opp10 else 0,
                    "away_avg_margin_10": away_pts10 - away_opp10 if away_pts10 and away_opp10 else 0,
                    # Season context
                    "season_year": date.year,
                    "month": date.month,
                    "day_of_week": date.weekday(),
                    # Placeholders for advanced stats (could be populated later)
                    "home_off_rating": None,
                    "home_def_rating": None,
                    "home_net_rating": None,
                    "home_pace": None,
                    "home_home_win_pct": None,
                    "away_off_rating": None,
                    "away_def_rating": None,
                    "away_net_rating": None,
                    "away_pace": None,
                    "away_away_win_pct": None,
                    "home_injuries": None,
                    "away_injuries": None,
                }
            )
            
        # Convert to DataFrame and save
        logger.info("Converting features to DataFrame and saving...")
        features_df = pd.DataFrame(features)
        
        # Ensure output directory exists
        os.makedirs(os.path.dirname(OUTPUT_CSV), exist_ok=True)
        
        # Save to CSV
        features_df.to_csv(OUTPUT_CSV, index=False)
        logger.info(f"Successfully saved WNBA training data to {OUTPUT_CSV} ({len(features_df)} rows)")
        
        # Log some basic statistics
        logger.info(f"Dataset statistics:")
        logger.info(f"  Total games: {len(features_df)}")
        logger.info(f"  Home wins: {features_df['home_win'].sum()}")
        logger.info(f"  Away wins: {len(features_df) - features_df['home_win'].sum()}")
        logger.info(f"  Home win percentage: {features_df['home_win'].mean():.3f}")
        logger.info(f"  Date range: {features_df['game_date'].min()} to {features_df['game_date'].max()}")
        
        return features_df
        
    except Exception as e:
        logger.error(f"Error in fetch_games_and_features: {str(e)}")
        raise


def validate_training_data(df):
    """
    Validate the generated training data for completeness and quality.
    """
    logger.info("Validating training data...")
    
    # Check for required columns
    required_cols = ['game_id', 'game_date', 'home_team_id', 'away_team_id', 'home_win']
    missing_cols = [col for col in required_cols if col not in df.columns]
    if missing_cols:
        logger.error(f"Missing required columns: {missing_cols}")
        return False
    
    # Check for missing values in critical columns
    for col in required_cols:
        null_count = df[col].isnull().sum()
        if null_count > 0:
            logger.warning(f"Column {col} has {null_count} null values")
    
    # Check data quality
    total_games = len(df)
    if total_games == 0:
        logger.error("No games in dataset")
        return False
    
    # Check home win distribution
    home_wins = df['home_win'].sum()
    home_win_pct = home_wins / total_games
    if home_win_pct < 0.4 or home_win_pct > 0.6:
        logger.warning(f"Unusual home win percentage: {home_win_pct:.3f}")
    
    logger.info("Training data validation completed successfully")
    return True


if __name__ == "__main__":
    try:
        logger.info("Starting WNBA moneyline training data collection...")
        result = asyncio.run(fetch_games_and_features())
        
        if result is not None:
            # Validate the data
            if validate_training_data(result):
                logger.info("WNBA moneyline training data collection completed successfully!")
            else:
                logger.error("Data validation failed")
        else:
            logger.error("No data was generated")
            
    except Exception as e:
        logger.error(f"Script execution failed: {str(e)}")
        raise
