#!/usr/bin/env python3
"""
Show exactly what data was used for testing
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta

def show_test_data():
    """Show the actual test data used"""
    
    print("🔍 ANALYZING TEST DATA - LAST 7 DAYS")
    print("=" * 50)
    
    # Load the data
    try:
        df = pd.read_csv('data/master/wnba_definitive_master_dataset_FIXED.csv')
        df['game_date'] = pd.to_datetime(df['game_date'])
        
        print(f"📊 Total dataset: {len(df)} records")
        print(f"📅 Full date range: {df['game_date'].min()} to {df['game_date'].max()}")
        
        # Filter for last 7 days (July 5-12, 2025)
        end_date = datetime(2025, 7, 12)
        start_date = end_date - timedelta(days=7)
        
        recent_games = df[
            (df['game_date'] >= start_date) & 
            (df['game_date'] <= end_date)
        ].copy()
        
        print(f"\n🎯 TEST PERIOD: {start_date.date()} to {end_date.date()}")
        print(f"✅ Found {len(recent_games)} player performances")
        
        if len(recent_games) > 0:
            print("\n📅 GAMES BY DATE:")
            games_by_date = recent_games.groupby(recent_games['game_date'].dt.date).size()
            for date, count in games_by_date.items():
                print(f"   {date}: {count} player performances")
            
            print("\n🏀 SAMPLE TEST DATA:")
            sample_cols = ['game_date', 'player_name', 'team', 'points']
            available_cols = [col for col in sample_cols if col in recent_games.columns]
            
            if 'points' not in recent_games.columns and 'target' in recent_games.columns:
                available_cols = [col for col in available_cols if col != 'points'] + ['target']
            
            print(recent_games[available_cols].head(10).to_string(index=False))
            
            print(f"\n📊 STATISTICS:")
            points_col = 'points' if 'points' in recent_games.columns else 'target'
            if points_col in recent_games.columns:
                points = recent_games[points_col].dropna()
                print(f"   Average points: {points.mean():.1f}")
                print(f"   Min points: {points.min():.1f}")
                print(f"   Max points: {points.max():.1f}")
                print(f"   Std deviation: {points.std():.1f}")
            
            print(f"\n🏀 TEAMS REPRESENTED:")
            if 'team' in recent_games.columns:
                team_counts = recent_games['team'].value_counts()
                for team, count in team_counts.items():
                    print(f"   {team}: {count} performances")
            
            print(f"\n🎮 WHAT THIS MEANS:")
            print(f"   ✅ These are REAL WNBA player performances")
            print(f"   ✅ From actual games in July 2025")
            print(f"   ✅ Each row = one player's performance in one game")
            print(f"   ✅ Models predict points scored by each player")
            print(f"   ✅ Test compares predicted vs actual points")
            
        else:
            print("\n⚠️ No games found in target period")
            print("📊 Most recent games available:")
            recent_available = df.nlargest(10, 'game_date')
            sample_cols = ['game_date', 'player_name', 'team']
            available_cols = [col for col in sample_cols if col in recent_available.columns]
            print(recent_available[available_cols].to_string(index=False))
        
        # Show data quality
        print(f"\n🔍 DATA QUALITY CHECK:")
        print(f"   Total features: {len(df.columns)}")
        print(f"   Missing values: {df.isnull().sum().sum()}")
        print(f"   Data types: {df.dtypes.value_counts().to_dict()}")
        
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    show_test_data()
