{"timestamp": "2025-07-12T20:00:11.026941", "expert_dataset_path": "data/master/wnba_expert_dataset.csv", "total_scripts_found": 42, "updated_scripts": 27, "skipped_scripts": 15, "failed_scripts": 0, "updated_files": ["train_ultimate_clean.py", "test_new_model_last_7_days.py", "final_real_model_test.py", "train_real_wnba_models.py", "train_correct_models.py", "train_all_advanced_models.py", "train_federated_multiverse_system.py", "train_ultimate_wnba_model.py", "train_production_model_1.py", "fix_model_testing.py", "simple_model_test.py", "train_actual_federated_models.py", "update_all_models_to_expert_dataset.py", "archive\\test_scripts\\validate_step1_model.py", "test_models_last_7_days.py", "direct_model_test.py", "train_noise_reduced_model.py", "train_production_model_1_improved.py", "test_real_model_last_7_days.py", "expert_model_fix.py", "consolidated_wnba\\01_player_data\\basic_stats\\retrain_wnba_neural_model_fixed.py", "reconstruct_training_features.py", "proper_model_retraining.py", "consolidated_wnba\\01_player_data\\basic_stats\\train_wnba_stratified_fixed.py", "train_final_clean_model.py", "test_enhanced_model_last_7_days.py", "test_real_trained_models.py"], "failed_files": [], "detailed_results": [{"file": "consolidated_wnba\\01_player_data\\basic_stats\\train_wnba_feature_filtered.py", "status": "skipped", "reason": "No dataset references found", "replacements": 0}, {"file": "train_ultimate_clean.py", "status": "updated", "replacements": 1, "backup": "backups\\model_scripts_backup\\train_ultimate_clean_20250712_200010.py"}, {"file": "consolidated_wnba\\01_player_data\\basic_stats\\train_wnba_comprehensive_audit.py", "status": "skipped", "reason": "No dataset references found", "replacements": 0}, {"file": "test_new_model_last_7_days.py", "status": "updated", "replacements": 1, "backup": "backups\\model_scripts_backup\\test_new_model_last_7_days_20250712_200010.py"}, {"file": "final_real_model_test.py", "status": "updated", "replacements": 1, "backup": "backups\\model_scripts_backup\\final_real_model_test_20250712_200010.py"}, {"file": "train_real_wnba_models.py", "status": "updated", "replacements": 1, "backup": "backups\\model_scripts_backup\\train_real_wnba_models_20250712_200010.py"}, {"file": "train_correct_models.py", "status": "updated", "replacements": 1, "backup": "backups\\model_scripts_backup\\train_correct_models_20250712_200010.py"}, {"file": "train_all_advanced_models.py", "status": "updated", "replacements": 1, "backup": "backups\\model_scripts_backup\\train_all_advanced_models_20250712_200010.py"}, {"file": "train_model_1_real_data.py", "status": "skipped", "reason": "No dataset references found", "replacements": 0}, {"file": "consolidated_wnba\\01_player_data\\basic_stats\\fix_wnba_model_overfitting.py", "status": "skipped", "reason": "No dataset references found", "replacements": 0}, {"file": "consolidated_wnba\\04_training_data\\player_props\\build_moneyline_training_data_wnba.py", "status": "skipped", "reason": "No dataset references found", "replacements": 0}, {"file": "consolidated_wnba\\01_player_data\\basic_stats\\train_wnba_priority.py", "status": "skipped", "reason": "No dataset references found", "replacements": 0}, {"file": "train_federated_multiverse_system.py", "status": "updated", "replacements": 1, "backup": "backups\\model_scripts_backup\\train_federated_multiverse_system_20250712_200010.py"}, {"file": "train_ultimate_wnba_model.py", "status": "updated", "replacements": 1, "backup": "backups\\model_scripts_backup\\train_ultimate_wnba_model_20250712_200010.py"}, {"file": "archive\\test_scripts\\model1_preflight_checklist_part2.py", "status": "skipped", "reason": "No dataset references found", "replacements": 0}, {"file": "train_production_model_1.py", "status": "updated", "replacements": 2, "backup": "backups\\model_scripts_backup\\train_production_model_1_20250712_200010.py"}, {"file": "fix_model_testing.py", "status": "updated", "replacements": 1, "backup": "backups\\model_scripts_backup\\fix_model_testing_20250712_200010.py"}, {"file": "simple_model_test.py", "status": "updated", "replacements": 1, "backup": "backups\\model_scripts_backup\\simple_model_test_20250712_200010.py"}, {"file": "train_actual_federated_models.py", "status": "updated", "replacements": 1, "backup": "backups\\model_scripts_backup\\train_actual_federated_models_20250712_200010.py"}, {"file": "consolidated_wnba\\01_player_data\\basic_stats\\train_wnba_full_dataset.py", "status": "skipped", "reason": "No dataset references found", "replacements": 0}, {"file": "update_all_models_to_expert_dataset.py", "status": "updated", "replacements": 10, "backup": "backups\\model_scripts_backup\\update_all_models_to_expert_dataset_20250712_200010.py"}, {"file": "archive\\test_scripts\\validate_step1_model.py", "status": "updated", "replacements": 1, "backup": "backups\\model_scripts_backup\\validate_step1_model_20250712_200010.py"}, {"file": "test_models_last_7_days.py", "status": "updated", "replacements": 1, "backup": "backups\\model_scripts_backup\\test_models_last_7_days_20250712_200010.py"}, {"file": "direct_model_test.py", "status": "updated", "replacements": 1, "backup": "backups\\model_scripts_backup\\direct_model_test_20250712_200010.py"}, {"file": "train_noise_reduced_model.py", "status": "updated", "replacements": 1, "backup": "backups\\model_scripts_backup\\train_noise_reduced_model_20250712_200010.py"}, {"file": "train_production_model_1_improved.py", "status": "updated", "replacements": 1, "backup": "backups\\model_scripts_backup\\train_production_model_1_improved_20250712_200010.py"}, {"file": "test_real_model_last_7_days.py", "status": "updated", "replacements": 1, "backup": "backups\\model_scripts_backup\\test_real_model_last_7_days_20250712_200010.py"}, {"file": "consolidated_wnba\\01_player_data\\basic_stats\\retrain_wnba_models_new_format.py", "status": "skipped", "reason": "No dataset references found", "replacements": 0}, {"file": "expert_model_fix.py", "status": "updated", "replacements": 1, "backup": "backups\\model_scripts_backup\\expert_model_fix_20250712_200010.py"}, {"file": "src\\models\\game_totals_model.py", "status": "skipped", "reason": "No dataset references found", "replacements": 0}, {"file": "consolidated_wnba\\01_player_data\\basic_stats\\retrain_wnba_neural_model_fixed.py", "status": "updated", "replacements": 1, "backup": "backups\\model_scripts_backup\\retrain_wnba_neural_model_fixed_20250712_200010.py"}, {"file": "reconstruct_training_features.py", "status": "updated", "replacements": 1, "backup": "backups\\model_scripts_backup\\reconstruct_training_features_20250712_200010.py"}, {"file": "proper_model_retraining.py", "status": "updated", "replacements": 1, "backup": "backups\\model_scripts_backup\\proper_model_retraining_20250712_200010.py"}, {"file": "src\\models\\modern_player_points_model.py", "status": "skipped", "reason": "No dataset references found", "replacements": 0}, {"file": "consolidated_wnba\\01_player_data\\basic_stats\\train_wnba_overfitting_fixed.py", "status": "skipped", "reason": "No dataset references found", "replacements": 0}, {"file": "consolidated_wnba\\01_player_data\\basic_stats\\train_wnba_stratified_fixed.py", "status": "updated", "replacements": 1, "backup": "backups\\model_scripts_backup\\train_wnba_stratified_fixed_20250712_200011.py"}, {"file": "archive\\test_scripts\\model1_preflight_checklist.py", "status": "skipped", "reason": "No dataset references found", "replacements": 0}, {"file": "train_final_clean_model.py", "status": "updated", "replacements": 1, "backup": "backups\\model_scripts_backup\\train_final_clean_model_20250712_200011.py"}, {"file": "test_enhanced_model_last_7_days.py", "status": "updated", "replacements": 1, "backup": "backups\\model_scripts_backup\\test_enhanced_model_last_7_days_20250712_200011.py"}, {"file": "test_real_trained_models.py", "status": "updated", "replacements": 1, "backup": "backups\\model_scripts_backup\\test_real_trained_models_20250712_200011.py"}, {"file": "consolidated_wnba\\01_player_data\\basic_stats\\train_wnba_models.py", "status": "skipped", "reason": "No dataset references found", "replacements": 0}, {"file": "scripts\\automation\\launch_flower_training.py", "status": "skipped", "reason": "No dataset references found", "replacements": 0}]}