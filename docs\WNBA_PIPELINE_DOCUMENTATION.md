# WNBA Player Points Prediction Pipeline - Production Documentation

## Overview

This is a production-grade machine learning pipeline for predicting WNBA player points with advanced features including Neural Architecture Search, uncertainty quantification, fairness testing, and federated learning capabilities.

## 🏗️ Architecture

### Core Components

1. **Model1Trainer** - Main training orchestrator
2. **WNBAConfig** - Centralized configuration management
3. **DriftDetector** - Monitors data distribution changes
4. **DynamicModelUpdater** - Handles online learning
5. **MultiverseEnsemblePredictor** - Advanced ensemble system

### Advanced Features

- ✅ **Real Data Only** - No synthetic data generation
- ✅ **Neural Architecture Search** - Automated model optimization
- ✅ **Uncertainty Quantification** - Prediction intervals
- ✅ **Fairness Testing** - Bias detection across teams/roles
- ✅ **Online Learning** - Continuous model updates
- ✅ **Federated Learning** - Multi-team collaboration
- ✅ **Counterfactual Explanations** - What-if analysis

## 📊 Data Requirements

### Input Data Format

```python
# Required columns for training data
required_columns = [
    'player_name',      # str: Player full name
    'team_abbreviation', # str: WNBA team (ATL, CHI, CON, etc.)
    'points',           # float: Points scored in game
    'rebounds',         # float: Rebounds in game
    'assists',          # float: Assists in game
    'minutes',          # float: Minutes played
    'year',             # int: Season year (2015-2025)
    'game_date',        # str: Game date (YYYY-MM-DD)
]
```

### Data Sources

1. **Primary Dataset**: `consolidated_wnba/01_player_data/basic_stats/`
2. **Mappings**: `consolidated_wnba/mappings/`
3. **Stadium Data**: `wnba_stadium_locations.csv`

## 🚀 Quick Start

### Basic Training

```bash
# Production training with all features
python train_model_1_real_data.py --production

# Individual features
python train_model_1_real_data.py --nas
python train_model_1_real_data.py --bayesian
python train_model_1_real_data.py --federated --server
```

### Configuration

```python
from wnba_config import WNBAConfig

# Load default configuration
config = WNBAConfig()

# Customize settings
config.models.LIGHTGBM_PARAMS['learning_rate'] = 0.1
config.advanced.NAS_MAX_TRIALS = 100

# Save custom configuration
config.save_config(Path("custom_config.json"))
```

## 🔧 Configuration Management

### Key Configuration Classes

1. **WNBATeamConfig** - Team-specific settings
2. **WNBAPlayerConfig** - Player role thresholds
3. **WNBAGameConfig** - Game type classifications
4. **WNBAFeatureConfig** - Feature engineering settings
5. **WNBAModelConfig** - Model hyperparameters
6. **WNBAAdvancedConfig** - Advanced ML features
7. **WNBAProductionConfig** - Production deployment

### Configurable Thresholds

```python
# Player role classification
ELITE_STARTER_THRESHOLDS = (30.0, 18.0, 1200.0)  # min, ppg, total_min
ROTATION_PLAYER_THRESHOLDS = (15.0, 8.0, 660.0)
BENCH_PLAYER_THRESHOLDS = (5.0, 1.0, 220.0)

# Travel distance categories
TRAVEL_DISTANCE_THRESHOLDS = {
    'short': (0.0, 800.0),
    'medium': (800.0, 1500.0),
    'long': (1500.0, float('inf'))
}

# Game intensity scoring
INTENSITY_SCORES = {
    'Regular': 0.8,
    'Playoffs': 1.2,
    'Championship': 1.6,
    'All-Star': 0.6
}
```

## 🧪 Testing

### Running Unit Tests

```bash
# Run all tests
python test_wnba_pipeline.py

# Run specific test class
python -m unittest test_wnba_pipeline.TestFeatureEngineering

# Run with verbose output
python test_wnba_pipeline.py -v
```

### Test Coverage

- ✅ Configuration management
- ✅ Feature engineering functions
- ✅ Model evaluation metrics
- ✅ Data validation
- ✅ Drift detection
- ✅ Fairness testing

## 📈 Model Performance

### Expected Performance Metrics

| Model Type | MAE (points) | R² Score | Coverage |
|------------|--------------|----------|----------|
| Elite Players | < 2.8 | > 0.75 | > 0.85 |
| Rotation Players | < 2.3 | > 0.70 | > 0.80 |
| Bench Players | < 1.5 | > 0.65 | > 0.75 |

### Validation Approach

1. **Temporal Splits**: 2015-2022 train, 2023 validation, 2024-2025 test
2. **Cross-Validation**: 5-fold time series CV
3. **Fairness Testing**: Group-wise error analysis
4. **Uncertainty Quantification**: 90% prediction intervals

## 🔍 Advanced Features

### Neural Architecture Search (NAS)

```python
# Enable NAS with Ray Tune
trainer = Model1Trainer()
results = trainer.train_production_model(
    epochs=1000,
    enable_nas=True,
    nas_trials=50
)
```

### Uncertainty Quantification

```python
# Get prediction intervals
intervals = calculate_prediction_intervals(
    model, X_test, confidence_level=0.9
)
print(f"Coverage: {intervals['coverage']:.2f}")
```

### Fairness Testing

```python
# Test for bias across teams and roles
fairness_results = test_fairness(
    y_true, y_pred, test_data,
    protected_attributes=['team_abbrev', 'role_score']
)
print(f"Fairness score: {fairness_results['overall_fairness_score']:.2f}")
```

### Drift Detection

```python
# Monitor data distribution changes
detector = DriftDetector(threshold=0.05)
detector.fit(reference_data)
drift_result = detector.detect_drift(new_data)
```

## 🤝 Federated Learning

### Server Setup

```bash
# Start federated server
python train_model_1_real_data.py --federated --server
```

### Client Setup

```bash
# Join as team client
python train_model_1_real_data.py --federated --client --team ATL
```

## 📊 Feature Engineering

### Automatic Features

1. **Rolling Averages** - 3, 5, 10 game windows
2. **EWMA** - Exponentially weighted moving averages
3. **Travel Distance** - Real stadium coordinates
4. **Weather Data** - Live weather API integration
5. **Game Context** - Intensity, back-to-back, altitude
6. **Role Embeddings** - Continuous role scoring

### Custom Features

```python
# Add custom features in create_advanced_features()
def create_custom_feature(df):
    df['custom_metric'] = df['points'] / df['minutes']
    return df
```

## 🚀 Production Deployment

### Model Artifacts

- `model_artifacts/` - Trained models
- `scalers/` - Feature scalers
- `drift_detectors/` - Drift monitoring
- `configs/` - Configuration files

### Performance Monitoring

```python
# Monitor model performance
metrics = ModelMetrics()
current_mae = metrics.calculate_mae(y_true, y_pred)

if current_mae > config.production.ACCEPTABLE_MAE:
    # Trigger model retraining
    model_updater.update_model(new_data)
```

## 🔧 Troubleshooting

### Common Issues

1. **Missing Dependencies**
   ```bash
   pip install ray[tune] alibi qiskit torch-geometric torchbnn
   ```

2. **Data Format Issues**
   - Ensure team abbreviations match official WNBA teams
   - Verify date formats (YYYY-MM-DD)
   - Check for missing required columns

3. **Memory Issues**
   - Reduce batch size in config
   - Use data sampling for large datasets
   - Enable gradient checkpointing

### Debug Mode

```bash
# Enable debug logging
python train_model_1_real_data.py --production --debug
```

## 📚 API Reference

### Main Classes

- `Model1Trainer` - Primary training interface
- `WNBAConfig` - Configuration management
- `DriftDetector` - Data drift monitoring
- `DynamicModelUpdater` - Online learning
- `MultiverseEnsemblePredictor` - Ensemble predictions

### Key Functions

- `prepare_real_training_data()` - Data preparation
- `train_production_model()` - Model training
- `test_multiverse_ensemble_system()` - System testing
- `dynamic_difficulty_score()` - Performance evaluation
- `test_fairness()` - Bias detection

## 📄 License

This project is proprietary software for WNBA analytics applications.

## 👥 Contributing

1. Follow PEP 8 style guidelines
2. Add comprehensive docstrings
3. Include unit tests for new features
4. Update configuration as needed
5. Document breaking changes

## 📞 Support

For technical support or questions:
- Create an issue in the repository
- Contact the WNBA Analytics Team
- Review the troubleshooting guide

---

**Version**: 2.0.0  
**Last Updated**: 2025-07-11  
**Maintainer**: WNBA Analytics Team
