"""
WNBA Player Points Prediction Configuration

Centralized configuration management for all hyperparameters, thresholds,
and constants used throughout the WNBA prediction pipeline.

Author: WNBA Analytics Team
Version: 2.0.0
Date: 2025-07-11
"""

from dataclasses import dataclass, field
from typing import Dict, List, Tuple, Optional, Any
from pathlib import Path


@dataclass
class WNBATeamConfig:
    """Configuration for WNBA team-specific settings"""
    
    # Official WNBA teams with abbreviations
    OFFICIAL_TEAMS: List[str] = field(default_factory=lambda: [
        'ATL', 'CHI', 'CON', 'DAL', 'GSV', 'IND', 
        'LAS', 'LV', 'MIN', 'NYL', 'PHO', 'SEA', 'WAS'
    ])
    
    # Team relocations and abbreviation changes
    TEAM_RELOCATIONS: Dict[str, str] = field(default_factory=lambda: {
        'UTA': 'LV',  # Utah Starzz → Las Vegas Aces
        'SA': 'LV',   # San Antonio Silver Stars → Las Vegas Aces
        'SAS': 'LV',  # San Antonio Stars → Las Vegas Aces
        'ORL': 'CON', # Orlando Miracle → Connecticut Sun
        'DET': 'DAL', # Detroit Shock → Dallas Wings
        'TUL': 'DAL'  # Tulsa Shock → Dallas Wings
    })
    
    # Arena altitudes (feet above sea level)
    ARENA_ALTITUDES: Dict[str, float] = field(default_factory=lambda: {
        'SEA': 52, 'MIN': 845, 'IND': 707, 'PHO': 1086, 'LAS': 239,
        'LV': 2000, 'WAS': 46, 'CHI': 593, 'CON': 1000, 'DAL': 426,
        'ATL': 1023, 'NYL': 35, 'GSV': 52
    })


@dataclass
class WNBAPlayerConfig:
    """Configuration for WNBA player-specific settings"""
    
    # Player role thresholds (minutes per game, points per game, total minutes)
    ELITE_STARTER_THRESHOLDS: Tuple[float, float, float] = (30.0, 18.0, 1200.0)
    ROTATION_PLAYER_THRESHOLDS: Tuple[float, float, float] = (15.0, 8.0, 660.0)
    BENCH_PLAYER_THRESHOLDS: Tuple[float, float, float] = (5.0, 1.0, 220.0)
    
    # Role scoring weights for continuous role calculation
    ROLE_SCORING_WEIGHTS: Dict[str, float] = field(default_factory=lambda: {
        'season_avg_points': 0.4,
        'usage_rate': 0.3,
        'minutes': 0.2,
        'defensive_impact': 0.1
    })
    
    # Validation targets for role-based MAE
    ROLE_VALIDATION_TARGETS: Dict[str, float] = field(default_factory=lambda: {
        'Elite': 2.8,
        'Rotation': 2.3,
        'Bench': 1.5
    })
    
    # 2025 WNBA scoring benchmarks
    SCORING_BENCHMARKS: Dict[str, Tuple[float, float]] = field(default_factory=lambda: {
        'Elite': (18.0, 25.0),      # Elite stars 18-25+ PPG
        'Rotation': (8.0, 15.0),   # Rotation players 8-15 PPG
        'Bench': (2.0, 7.0)        # Bench players 2-7 PPG
    })


@dataclass
class WNBAGameConfig:
    """Configuration for WNBA game-specific settings"""
    
    # Game type classifications
    GAME_TYPES: List[str] = field(default_factory=lambda: [
        'Regular', 'Playoffs', 'Championship', 'All-Star', 'Exhibition'
    ])
    
    # Game intensity scoring (0.6-1.6 scale)
    INTENSITY_SCORES: Dict[str, float] = field(default_factory=lambda: {
        'Regular': 0.8,
        'Playoffs': 1.2,
        'Championship': 1.6,
        'All-Star': 0.6,
        'Exhibition': 0.6
    })
    
    # Travel distance categories (miles)
    TRAVEL_DISTANCE_THRESHOLDS: Dict[str, Tuple[float, float]] = field(default_factory=lambda: {
        'short': (0.0, 800.0),
        'medium': (800.0, 1500.0),
        'long': (1500.0, float('inf'))
    })
    
    # Back-to-back game impact factors
    BACK_TO_BACK_IMPACT: float = 0.85  # 15% performance reduction
    
    # Altitude adjustment factors
    ALTITUDE_ADJUSTMENT_THRESHOLD: float = 1000.0  # feet
    ALTITUDE_IMPACT_FACTOR: float = 0.02  # 2% per 1000 feet


@dataclass
class WNBAFeatureConfig:
    """Configuration for feature engineering settings"""
    
    # Rolling window sizes for moving averages
    ROLLING_WINDOWS: List[int] = field(default_factory=lambda: [3, 5, 10])
    
    # EWMA spans for exponential weighted moving averages
    EWMA_SPANS: List[int] = field(default_factory=lambda: [5, 10])
    
    # Weather feature settings
    WEATHER_CACHE_DURATION: int = 86400  # 24 hours in seconds
    WEATHER_API_TIMEOUT: int = 10  # seconds
    WEATHER_BATCH_SIZE: int = 100  # requests per batch
    
    # Feature selection thresholds
    FEATURE_IMPORTANCE_THRESHOLD: float = 0.001
    CORRELATION_THRESHOLD: float = 0.95
    
    # Missing value handling
    MISSING_VALUE_THRESHOLD: float = 0.5  # Drop features with >50% missing
    
    # Outlier detection
    OUTLIER_Z_SCORE_THRESHOLD: float = 3.0
    OUTLIER_IQR_MULTIPLIER: float = 1.5


@dataclass
class WNBAModelConfig:
    """Configuration for model training and evaluation"""
    
    # Temporal splits for training/validation/test
    TRAIN_YEARS: Tuple[int, int] = (2015, 2022)  # 2015-2022 inclusive
    VALIDATION_YEAR: int = 2023
    TEST_YEARS: Tuple[int, int] = (2024, 2025)   # 2024-2025 inclusive
    
    # LightGBM hyperparameters
    LIGHTGBM_PARAMS: Dict[str, Any] = field(default_factory=lambda: {
        'objective': 'regression',
        'metric': 'mae',
        'boosting_type': 'gbdt',
        'num_leaves': 31,
        'learning_rate': 0.05,
        'feature_fraction': 0.9,
        'bagging_fraction': 0.8,
        'bagging_freq': 5,
        'verbose': -1,
        'random_state': 42
    })
    
    # Neural network hyperparameters
    NEURAL_NETWORK_PARAMS: Dict[str, Any] = field(default_factory=lambda: {
        'hidden_sizes': [256, 128, 64],
        'dropout_rate': 0.3,
        'learning_rate': 0.001,
        'batch_size': 64,
        'max_epochs': 100,
        'patience': 10
    })
    
    # Ensemble weights
    ENSEMBLE_WEIGHTS: Dict[str, float] = field(default_factory=lambda: {
        'lightgbm': 0.4,
        'neural_network': 0.3,
        'random_forest': 0.2,
        'linear_regression': 0.1
    })
    
    # Cross-validation settings
    CV_FOLDS: int = 5
    CV_SCORING: str = 'neg_mean_absolute_error'
    
    # Early stopping
    EARLY_STOPPING_ROUNDS: int = 50
    EARLY_STOPPING_PATIENCE: int = 10


@dataclass
class WNBAAdvancedConfig:
    """Configuration for advanced ML features"""
    
    # Neural Architecture Search (NAS)
    NAS_SEARCH_SPACE: Dict[str, Any] = field(default_factory=lambda: {
        'hidden_size_1': [64, 128, 256, 512],
        'hidden_size_2': [32, 64, 128, 256],
        'dropout_rate': [0.1, 0.2, 0.3, 0.4, 0.5],
        'learning_rate': [0.0001, 0.001, 0.01],
        'batch_size': [32, 64, 128]
    })
    
    NAS_MAX_TRIALS: int = 50
    NAS_TIMEOUT: int = 3600  # 1 hour
    
    # Uncertainty quantification
    QUANTILE_LEVELS: List[float] = field(default_factory=lambda: [0.05, 0.5, 0.95])
    PREDICTION_INTERVAL_COVERAGE: float = 0.9  # 90% coverage
    
    # Fairness testing
    FAIRNESS_PROTECTED_ATTRIBUTES: List[str] = field(default_factory=lambda: [
        'team_abbrev', 'role_score'
    ])
    FAIRNESS_THRESHOLD: float = 0.1  # 10% maximum bias
    
    # Drift detection
    DRIFT_DETECTION_THRESHOLD: float = 0.05
    DRIFT_WINDOW_SIZE: int = 1000
    DRIFT_UPDATE_FREQUENCY: int = 100  # samples
    
    # Online learning
    ONLINE_LEARNING_BATCH_SIZE: int = 50
    ONLINE_LEARNING_DECAY: float = 0.95
    
    # Counterfactual explanations
    COUNTERFACTUAL_TARGET_CHANGE: float = 5.0  # points
    COUNTERFACTUAL_NUM_EXAMPLES: int = 5
    COUNTERFACTUAL_MAX_ITERATIONS: int = 100


@dataclass
class WNBAProductionConfig:
    """Configuration for production deployment"""
    
    # Model versioning
    MODEL_VERSION: str = "2.0.0"
    MODEL_NAME: str = "wnba_player_points_predictor"
    
    # File paths
    OUTPUT_DIR: Path = field(default_factory=lambda: Path("models/production"))
    DATA_DIR: Path = field(default_factory=lambda: Path("data"))
    LOGS_DIR: Path = field(default_factory=lambda: Path("logs"))
    
    # Logging configuration
    LOG_LEVEL: str = "INFO"
    LOG_FORMAT: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    
    # Performance thresholds
    ACCEPTABLE_MAE: float = 3.0  # points
    ACCEPTABLE_R2: float = 0.7
    ACCEPTABLE_COVERAGE: float = 0.85  # prediction interval coverage
    
    # Resource limits
    MAX_MEMORY_GB: float = 8.0
    MAX_TRAINING_TIME_HOURS: float = 4.0
    MAX_PREDICTION_TIME_MS: float = 100.0
    
    # API settings
    API_TIMEOUT: int = 30  # seconds
    API_RETRY_ATTEMPTS: int = 3
    API_RATE_LIMIT: int = 500  # requests per month


@dataclass
class WNBAConfig:
    """Master configuration class combining all WNBA settings"""
    
    teams: WNBATeamConfig = field(default_factory=WNBATeamConfig)
    players: WNBAPlayerConfig = field(default_factory=WNBAPlayerConfig)
    games: WNBAGameConfig = field(default_factory=WNBAGameConfig)
    features: WNBAFeatureConfig = field(default_factory=WNBAFeatureConfig)
    models: WNBAModelConfig = field(default_factory=WNBAModelConfig)
    advanced: WNBAAdvancedConfig = field(default_factory=WNBAAdvancedConfig)
    production: WNBAProductionConfig = field(default_factory=WNBAProductionConfig)
    
    def save_config(self, filepath: Path) -> None:
        """Save configuration to JSON file"""
        config_dict = {
            'teams': self.teams.__dict__,
            'players': self.players.__dict__,
            'games': self.games.__dict__,
            'features': self.features.__dict__,
            'models': self.models.__dict__,
            'advanced': self.advanced.__dict__,
            'production': {k: str(v) if isinstance(v, Path) else v 
                          for k, v in self.production.__dict__.items()}
        }
        
        with open(filepath, 'w') as f:
            import json
            json.dump(config_dict, f, indent=2, default=str)
    
    @classmethod
    def load_config(cls, filepath: Path) -> 'WNBAConfig':
        """Load configuration from JSON file"""
        with open(filepath, 'r') as f:
            import json
            config_dict = json.load(f)
        
        # Convert paths back to Path objects
        if 'production' in config_dict:
            for key in ['OUTPUT_DIR', 'DATA_DIR', 'LOGS_DIR']:
                if key in config_dict['production']:
                    config_dict['production'][key] = Path(config_dict['production'][key])
        
        return cls(
            teams=WNBATeamConfig(**config_dict.get('teams', {})),
            players=WNBAPlayerConfig(**config_dict.get('players', {})),
            games=WNBAGameConfig(**config_dict.get('games', {})),
            features=WNBAFeatureConfig(**config_dict.get('features', {})),
            models=WNBAModelConfig(**config_dict.get('models', {})),
            advanced=WNBAAdvancedConfig(**config_dict.get('advanced', {})),
            production=WNBAProductionConfig(**config_dict.get('production', {}))
        )


# Default configuration instance
DEFAULT_CONFIG = WNBAConfig()


if __name__ == "__main__":
    # Example usage and configuration validation
    config = WNBAConfig()
    
    print("🔧 WNBA Configuration Validation")
    print("=" * 50)
    
    print(f"✅ Teams: {len(config.teams.OFFICIAL_TEAMS)} official WNBA teams")
    print(f"✅ Player roles: {len(config.players.ROLE_SCORING_WEIGHTS)} scoring weights")
    print(f"✅ Game types: {len(config.games.GAME_TYPES)} classifications")
    print(f"✅ Features: {len(config.features.ROLLING_WINDOWS)} rolling windows")
    print(f"✅ Models: {config.models.CV_FOLDS}-fold cross-validation")
    print(f"✅ Advanced: {config.advanced.NAS_MAX_TRIALS} NAS trials")
    print(f"✅ Production: v{config.production.MODEL_VERSION}")
    
    # Save example configuration
    config.save_config(Path("wnba_config_example.json"))
    print(f"\n📁 Example configuration saved to: wnba_config_example.json")
