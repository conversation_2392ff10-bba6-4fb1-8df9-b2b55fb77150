{"model_config": {"input_dim": 22, "hidden_dims": [128, 64, 32], "output_dim": 1, "dropout_rate": 0.3, "activation": "relu", "batch_norm": true, "model_type": "neural_network"}, "training_config": {"learning_rate": 0.001, "batch_size": 64, "local_epochs": 5, "optimizer": "adam", "loss_function": "mse", "weight_decay": 1e-05, "gradient_clipping": 1.0}, "federated_config": {"num_rounds": 10, "min_clients": 2, "max_clients": 13, "client_fraction": 1.0, "aggregation_strategy": "fedavg", "server_timeout": 300, "client_timeout": 180}, "data_config": {"temporal_split": true, "train_years": [2015, 2016, 2017, 2018, 2019, 2020, 2021, 2022], "val_years": [2023], "test_years": [2024, 2025], "feature_standardization": true, "target_scaling": false}, "team_weights": {"ATL": 1.0, "CHI": 1.0, "CON": 1.0, "DAL": 1.0, "IND": 1.0, "LAS": 1.0, "LV": 1.0, "MIN": 1.0, "NYL": 1.0, "PHO": 1.0, "SEA": 1.0, "WAS": 1.0, "GSV": 0.5}, "monitoring_config": {"log_level": "INFO", "metrics_tracking": true, "convergence_threshold": 0.001, "early_stopping_patience": 3, "save_checkpoints": true, "checkpoint_frequency": 2}, "privacy_config": {"differential_privacy": false, "noise_multiplier": 0.1, "max_grad_norm": 1.0, "secure_aggregation": false, "data_isolation_verified": true}}