#!/usr/bin/env python3
"""
🏀 TRAIN REAL WNBA MODELS - USING FULL MASTER DATASET
====================================================

Train our ACTUAL models using the FULL master dataset with 29,237 records
and 642 features of REAL WNBA data from 2020-2025.

Models to train:
1. PlayerPointsModel (Enhanced base model)
2. MultiTaskPlayerModel (Multi-task learning)
3. BayesianPlayerModel (Uncertainty quantification)
4. HybridPlayerPointsModel (Tabular + GNN)
5. FederatedPlayerModel (Federated learning)

Target: <2.0 MAE professional accuracy using ALL REAL DATA
"""

import pandas as pd
import numpy as np
import torch
import torch.nn as nn
import pytorch_lightning as pl
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
from sklearn.feature_selection import SelectKBest, f_regression
from sklearn.metrics import mean_absolute_error
import json
from datetime import datetime, timedelta
from pathlib import Path
import sys
import warnings
warnings.filterwarnings('ignore')

# Add src to path for our actual models
sys.path.append('src')
sys.path.append('src/models')

# Import our ACTUAL models
from models.modern_player_points_model import (
    PlayerPointsModel,
    HybridPlayerPointsModel, 
    MultiTaskPlayerModel,
    BayesianPlayerModel,
    FederatedPlayerModel,
    PlayerPointsDataset
)

class RealWNBAModelTrainer:
    """Train our actual WNBA models using the FULL master dataset"""
    
    def __init__(self):
        """Initialize the real WNBA model trainer"""
        print("🏀 TRAIN REAL WNBA MODELS - USING FULL MASTER DATASET")
        print("=" * 65)
        print("📊 Using 29,237 records with 642 features of REAL WNBA data")
        print("🎯 Target: <2.0 MAE professional accuracy")
        
    def load_full_master_dataset(self):
        """Load the complete master dataset with all real WNBA data"""
        try:
            print("\n📊 Loading FULL master dataset...")
            
            # Load the complete dataset
            df = pd.read_csv('data/master/wnba_definitive_master_dataset_FIXED.csv', low_memory=False)
            df['game_date'] = pd.to_datetime(df['game_date'], errors='coerce')
            
            print(f"   ✅ Loaded: {len(df)} records, {len(df.columns)} columns")
            print(f"   📅 Date range: {df['game_date'].min()} to {df['game_date'].max()}")
            
            # Check target variable
            if 'target' not in df.columns:
                print("❌ Target column not found")
                return None
            
            # Clean data - keep as much real data as possible
            print("   🧹 Cleaning real WNBA data...")
            
            # Remove only clearly invalid data
            df_clean = df[
                (df['target'].notna()) & 
                (df['target'] >= 0) & 
                (df['target'] <= 60) &  # Generous WNBA range
                (df['game_date'].notna())
            ].copy()
            
            print(f"   After cleaning: {len(df_clean)} records ({len(df_clean)/len(df)*100:.1f}% retained)")
            
            # Show data distribution
            print(f"   📊 Target stats: mean={df_clean['target'].mean():.1f}, std={df_clean['target'].std():.1f}")
            print(f"   📊 Target range: [{df_clean['target'].min():.1f}, {df_clean['target'].max():.1f}]")
            
            # Show years covered
            years = df_clean['game_date'].dt.year.value_counts().sort_index()
            print(f"   📅 Years covered: {dict(years)}")
            
            return df_clean
            
        except Exception as e:
            print(f"❌ Error loading master dataset: {e}")
            return None
    
    def prepare_features_from_master_dataset(self, df):
        """Prepare features from the master dataset"""
        try:
            print("\n🎯 Preparing features from master dataset...")
            
            # Define columns to exclude (metadata, not features)
            exclude_cols = {
                'target', 'points', 'player_name', 'team_abbrev', 'game_date', 
                'game_id', 'PLAYER_ID', 'PLAYER_NAME', 'TEAM_ID', 'TEAM_ABBREVIATION',
                'NICKNAME', 'TEAM_NAME', 'team_name', 'SEASON_ID', 'GAME_ID', 
                'GAME_DATE', 'MATCHUP', 'data_source', 'collection_date'
            }
            
            # Get all potential feature columns
            potential_features = [col for col in df.columns if col not in exclude_cols]
            print(f"   Potential features: {len(potential_features)}")
            
            # Convert to numeric and handle missing values
            feature_data = {}
            valid_features = []
            
            for col in potential_features:
                try:
                    # Convert to numeric
                    series = pd.to_numeric(df[col], errors='coerce')
                    
                    # Check data quality
                    missing_pct = series.isnull().sum() / len(series)
                    unique_values = series.nunique()
                    
                    # Keep features with reasonable data quality
                    if missing_pct < 0.8 and unique_values > 1:  # <80% missing, has variance
                        # Fill missing values with median
                        median_val = series.median()
                        if pd.isna(median_val):
                            median_val = 0.0
                        
                        feature_data[col] = series.fillna(median_val).values
                        valid_features.append(col)
                        
                except Exception as e:
                    continue
            
            print(f"   Valid features after cleaning: {len(valid_features)}")
            
            # Create feature matrix
            X = np.column_stack([feature_data[col] for col in valid_features])
            X = X.astype(np.float32)
            
            # Get targets
            y = df['target'].values.astype(np.float32)
            
            # Feature selection to get the best features
            print("   🔍 Selecting best features...")
            
            # Use SelectKBest to get top features
            n_features = min(200, len(valid_features))  # Select top 200 features
            selector = SelectKBest(score_func=f_regression, k=n_features)
            X_selected = selector.fit_transform(X, y)
            
            # Get selected feature names
            selected_indices = selector.get_support(indices=True)
            selected_features = [valid_features[i] for i in selected_indices]
            
            print(f"   ✅ Selected {X_selected.shape[1]} best features")
            print(f"   📊 Feature matrix shape: {X_selected.shape}")
            print(f"   📊 Feature range: [{X_selected.min():.3f}, {X_selected.max():.3f}]")
            
            # Create role IDs based on minutes played
            role_ids = np.ones(len(df), dtype=int)  # Default to rotation
            
            # Try to find minutes column
            minutes_cols = ['MIN', 'minutes', 'adv_MIN']
            minutes_col = None
            for col in minutes_cols:
                if col in df.columns:
                    minutes_col = col
                    break
            
            if minutes_col:
                minutes = pd.to_numeric(df[minutes_col], errors='coerce').fillna(0)
                # Elite: >25 min, Rotation: 10-25 min, Bench: <10 min
                role_ids = np.where(minutes > 25, 2, np.where(minutes > 10, 1, 0))
                
                print(f"   📊 Role distribution: Elite={np.sum(role_ids==2)}, Rotation={np.sum(role_ids==1)}, Bench={np.sum(role_ids==0)}")
            else:
                print("   ⚠️ No minutes column found, using default roles")
            
            return X_selected, y, role_ids, selected_features, selector
            
        except Exception as e:
            print(f"❌ Error preparing features: {e}")
            return None
    
    def create_temporal_split(self, df, X, y, role_ids):
        """Create temporal train/test split"""
        print("\n📅 Creating temporal train/test split...")
        
        # Sort by date
        df_sorted = df.sort_values('game_date').reset_index(drop=True)
        
        # Use 80% for training (chronological)
        split_idx = int(0.8 * len(df_sorted))
        
        train_indices = df_sorted.index[:split_idx]
        test_indices = df_sorted.index[split_idx:]
        
        X_train = X[train_indices]
        X_test = X[test_indices]
        y_train = y[train_indices]
        y_test = y[test_indices]
        role_train = role_ids[train_indices]
        role_test = role_ids[test_indices]
        
        # Get date ranges
        train_dates = df_sorted.loc[train_indices, 'game_date']
        test_dates = df_sorted.loc[test_indices, 'game_date']
        
        print(f"   Train: {len(X_train)} samples ({train_dates.min().date()} to {train_dates.max().date()})")
        print(f"   Test: {len(X_test)} samples ({test_dates.min().date()} to {test_dates.max().date()})")
        print(f"   Train target: mean={np.mean(y_train):.1f}, std={np.std(y_train):.1f}")
        print(f"   Test target: mean={np.mean(y_test):.1f}, std={np.std(y_test):.1f}")
        
        return X_train, X_test, y_train, y_test, role_train, role_test
    
    def scale_features(self, X_train, X_test):
        """Scale features properly"""
        print("\n📏 Scaling features...")
        
        # Use StandardScaler
        scaler = StandardScaler()
        
        X_train_scaled = scaler.fit_transform(X_train)
        X_test_scaled = scaler.transform(X_test)
        
        print(f"   Scaled train: mean={np.mean(X_train_scaled):.3f}, std={np.mean(np.std(X_train_scaled, axis=0)):.3f}")
        print(f"   Scaled test: mean={np.mean(X_test_scaled):.3f}, std={np.mean(np.std(X_test_scaled, axis=0)):.3f}")
        
        return X_train_scaled, X_test_scaled, scaler
    
    def create_data_loaders(self, X_train, X_test, y_train, y_test, role_train, role_test):
        """Create PyTorch data loaders"""
        print("\n📦 Creating data loaders...")
        
        # Create datasets
        train_dataset = PlayerPointsDataset(X_train, y_train, role_train)
        test_dataset = PlayerPointsDataset(X_test, y_test, role_test)
        
        # Create data loaders
        train_loader = torch.utils.data.DataLoader(
            train_dataset,
            batch_size=128,  # Larger batch size for more data
            shuffle=True,
            num_workers=0,
            pin_memory=True
        )
        
        test_loader = torch.utils.data.DataLoader(
            test_dataset,
            batch_size=128,
            shuffle=False,
            num_workers=0,
            pin_memory=True
        )
        
        print(f"   Train loader: {len(train_loader)} batches")
        print(f"   Test loader: {len(test_loader)} batches")
        
        return train_loader, test_loader
    
    def train_enhanced_model(self, train_loader, test_loader, input_dim):
        """Train the Enhanced PlayerPointsModel"""
        print("\n🤖 Training Enhanced PlayerPointsModel...")
        
        # Create model
        model = PlayerPointsModel(
            input_dim=input_dim,
            dropout=0.2,  # Lower dropout for more data
            learning_rate=1e-3,
            use_role_embedding=True
        )
        
        # Setup trainer
        trainer = pl.Trainer(
            max_epochs=100,  # More epochs for better training
            accelerator='auto',
            devices=1,
            callbacks=[
                pl.callbacks.EarlyStopping(
                    monitor='val_loss',
                    patience=15,
                    mode='min'
                ),
                pl.callbacks.ModelCheckpoint(
                    monitor='val_loss',
                    mode='min',
                    save_top_k=1,
                    filename='enhanced_real_{epoch:02d}_{val_loss:.4f}',
                    dirpath='models/enhanced_real'
                )
            ],
            enable_progress_bar=True,
            log_every_n_steps=50
        )
        
        # Create directory
        Path('models/enhanced_real').mkdir(parents=True, exist_ok=True)
        
        # Train
        trainer.fit(model, train_loader, test_loader)
        
        # Get best model
        best_model_path = trainer.checkpoint_callback.best_model_path
        best_model = PlayerPointsModel.load_from_checkpoint(best_model_path)
        best_model.eval()
        
        print(f"   ✅ Enhanced model trained!")
        print(f"   📁 Best model: {best_model_path}")
        print(f"   🎯 Best val loss: {trainer.checkpoint_callback.best_model_score:.4f}")
        
        return best_model, best_model_path
    
    def test_model_thoroughly(self, model, test_loader, model_name):
        """Test model thoroughly on real data"""
        print(f"\n🧪 Testing {model_name} on real WNBA data...")
        
        model.eval()
        all_predictions = []
        all_targets = []
        
        with torch.no_grad():
            for batch in test_loader:
                if len(batch) == 3:
                    x, y, role_ids = batch
                    predictions = model(x, role_ids)
                else:
                    x, y = batch
                    predictions = model(x)
                
                # Handle different output types
                if isinstance(predictions, dict):
                    predictions = predictions['points']
                
                all_predictions.extend(predictions.cpu().numpy())
                all_targets.extend(y.cpu().numpy())
        
        all_predictions = np.array(all_predictions)
        all_targets = np.array(all_targets)
        
        # Calculate metrics
        mae = mean_absolute_error(all_targets, all_predictions)
        rmse = np.sqrt(np.mean((all_predictions - all_targets) ** 2))
        mape = np.mean(np.abs((all_targets - all_predictions) / np.maximum(all_targets, 1))) * 100
        correlation = np.corrcoef(all_predictions, all_targets)[0, 1]
        
        print(f"   📊 {model_name} Results on REAL WNBA data:")
        print(f"      🎯 MAE: {mae:.3f} points")
        print(f"      📈 RMSE: {rmse:.3f} points")
        print(f"      📊 MAPE: {mape:.1f}%")
        print(f"      🔗 Correlation: {correlation:.3f}")
        print(f"      ⚖️ Avg Target: {np.mean(all_targets):.1f} points")
        print(f"      🔮 Avg Predicted: {np.mean(all_predictions):.1f} points")
        print(f"      📊 Pred Std: {np.std(all_predictions):.1f} points")
        
        # Assessment
        if mae < 2.0:
            status = "🏆 EXCELLENT - Professional grade!"
        elif mae < 3.0:
            status = "✅ GOOD - Production ready"
        elif mae < 4.0:
            status = "⚠️ ACCEPTABLE - Needs monitoring"
        else:
            status = "❌ POOR - Requires improvement"
        
        print(f"      📊 Status: {status}")
        
        return {
            'mae': mae,
            'rmse': rmse,
            'mape': mape,
            'correlation': correlation,
            'status': status,
            'predictions': all_predictions.tolist(),
            'targets': all_targets.tolist(),
            'pred_std': np.std(all_predictions),
            'target_std': np.std(all_targets)
        }
    
    def run_real_wnba_training(self):
        """Run the complete real WNBA model training"""
        print("🚀 Starting REAL WNBA model training with full dataset...")
        
        # Step 1: Load full master dataset
        df = self.load_full_master_dataset()
        if df is None:
            return False
        
        # Step 2: Prepare features from master dataset
        feature_data = self.prepare_features_from_master_dataset(df)
        if feature_data is None:
            return False
        
        X, y, role_ids, features, selector = feature_data
        
        # Step 3: Create temporal split
        X_train, X_test, y_train, y_test, role_train, role_test = self.create_temporal_split(df, X, y, role_ids)
        
        # Step 4: Scale features
        X_train_scaled, X_test_scaled, scaler = self.scale_features(X_train, X_test)
        
        # Step 5: Create data loaders
        train_loader, test_loader = self.create_data_loaders(
            X_train_scaled, X_test_scaled, y_train, y_test, role_train, role_test
        )
        
        # Step 6: Train Enhanced Model
        input_dim = X_train_scaled.shape[1]
        enhanced_model, enhanced_path = self.train_enhanced_model(train_loader, test_loader, input_dim)
        enhanced_results = self.test_model_thoroughly(enhanced_model, test_loader, "Enhanced")
        
        # Step 7: Save results
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        training_summary = {
            'timestamp': datetime.now().isoformat(),
            'dataset_info': {
                'total_records': len(df),
                'training_samples': len(X_train),
                'test_samples': len(X_test),
                'features_selected': len(features),
                'input_dim': input_dim
            },
            'enhanced_model': {
                'path': enhanced_path,
                'mae': enhanced_results['mae'],
                'rmse': enhanced_results['rmse'],
                'correlation': enhanced_results['correlation'],
                'status': enhanced_results['status']
            },
            'features': features,
            'scaler_params': {
                'mean': scaler.mean_.tolist(),
                'scale': scaler.scale_.tolist()
            }
        }
        
        summary_path = f"real_wnba_training_summary_{timestamp}.json"
        with open(summary_path, 'w') as f:
            json.dump(training_summary, f, indent=2, default=float)
        
        print(f"\n💾 Training summary saved to: {summary_path}")
        
        # Final assessment
        mae = enhanced_results['mae']
        print(f"\n🏆 REAL WNBA MODEL TRAINING COMPLETE!")
        print("=" * 50)
        print(f"📊 Dataset: {len(df)} real WNBA records")
        print(f"🎯 Enhanced Model MAE: {mae:.3f} points")
        print(f"📈 Status: {enhanced_results['status']}")
        
        if mae < 2.0:
            print(f"\n🎉 SUCCESS: Professional-grade WNBA model achieved!")
            print(f"   ✅ Ready for production deployment")
            print(f"   ✅ Trained on ALL real WNBA data")
        elif mae < 3.0:
            print(f"\n✅ GOOD: Solid WNBA model trained on real data!")
            print(f"   ✅ Acceptable for production use")
        else:
            print(f"\n⚠️ NEEDS IMPROVEMENT: Model requires optimization")
        
        return mae < 3.0


def main():
    """Main function"""
    trainer = RealWNBAModelTrainer()
    success = trainer.run_real_wnba_training()
    
    if success:
        print("\n✅ REAL WNBA MODEL TRAINING COMPLETED!")
        print("🏀 Professional WNBA prediction model trained on ALL real data!")
    else:
        print("\n❌ Real WNBA model training failed")


if __name__ == "__main__":
    main()
