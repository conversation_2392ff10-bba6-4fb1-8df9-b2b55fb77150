#!/bin/bash
# Modern Flower SuperLink/SuperNode Federated Learning Launcher

echo "🌸 MODERN WNBA FEDERATED LEARNING WITH FLOWER CLI"
echo "=================================================="

# Check if Flower is installed
if ! command -v flower-superlink &> /dev/null; then
    echo "❌ Flower CLI not found. Install with:"
    echo "   pip install flwr[simulation]"
    exit 1
fi

echo "✅ Flower CLI detected"

# Function to start SuperLink (server)
start_superlink() {
    echo "🌸 Starting Flower SuperLink (Server)..."
    flower-superlink --insecure --port 9092 &
    SUPERLINK_PID=$!
    echo "   SuperLink PID: $SUPERLINK_PID"
    sleep 3
}

# Function to start SuperNode (client)
start_supernode() {
    local team_id=$1
    echo "🏀 Starting SuperNode for $team_id..."
    
    # Create client app for team
    cat > "client_app_${team_id}.py" << EOF
from flower_client import create_client

def client_fn(cid: str):
    return create_client(team_id="$team_id")
EOF
    
    flower-supernode --insecure --superlink 127.0.0.1:9092 &
    echo "   $team_id SuperNode started"
}

# Function to cleanup
cleanup() {
    echo "🧹 Cleaning up..."
    pkill -f flower-superlink
    pkill -f flower-supernode
    rm -f client_app_*.py
    echo "✅ Cleanup complete"
}

# Trap cleanup on exit
trap cleanup EXIT

# Main execution
case "${1:-help}" in
    "server")
        echo "🌸 Starting SuperLink server only..."
        start_superlink
        echo "✅ SuperLink running. Connect clients with:"
        echo "   ./launch_modern_federated.sh client ATL"
        wait $SUPERLINK_PID
        ;;
    
    "client")
        if [ -z "$2" ]; then
            echo "❌ Team ID required. Usage: $0 client TEAM_ID"
            echo "   Available teams: ATL, CHI, CON, DAL, GSV, IND, LAS, LV, MIN, NYL, PHO, SEA, WAS"
            exit 1
        fi
        
        team_id=$2
        echo "🏀 Starting client for $team_id..."
        
        if [ "$team_id" = "GSV" ]; then
            echo "🆕 Golden State Valkyries - newest WNBA team!"
        fi
        
        python flower_client.py --team $team_id --server localhost:8080
        ;;
    
    "demo")
        echo "🎭 Starting demo with 3 teams (ATL, CHI, GSV)..."
        
        # Start SuperLink
        start_superlink
        
        # Wait a bit for server to start
        sleep 5
        
        # Start clients in background
        echo "🏀 Starting demo teams..."
        python flower_client.py --team ATL --server localhost:8080 &
        python flower_client.py --team CHI --server localhost:8080 &
        python flower_client.py --team GSV --server localhost:8080 &
        
        # Wait for completion
        wait
        ;;
    
    "ray")
        echo "🚀 Starting Ray-based parallel federated learning..."
        
        # Check if Ray is available
        if ! python -c "import ray" 2>/dev/null; then
            echo "❌ Ray not available. Install with: pip install ray[default]"
            exit 1
        fi
        
        # Start server in background
        python flower_server.py --min-teams 3 --max-rounds 10 &
        SERVER_PID=$!
        
        # Wait for server to start
        sleep 5
        
        # Launch Ray-based clients
        python ray_federated_launcher.py --teams ATL CHI GSV NYL SEA --timeout 300
        
        # Cleanup server
        kill $SERVER_PID 2>/dev/null
        ;;
    
    "all13")
        echo "🏆 Starting ALL 13 WNBA TEAMS federated learning..."
        echo "   Including Golden State Valkyries (GSV)!"
        
        # Start server
        python flower_server.py --min-teams 5 --max-rounds 15 &
        SERVER_PID=$!
        
        sleep 5
        
        # Start all 13 teams with Ray
        python ray_federated_launcher.py --timeout 600
        
        # Cleanup
        kill $SERVER_PID 2>/dev/null
        ;;
    
    "help"|*)
        echo "🌸 MODERN WNBA FEDERATED LEARNING LAUNCHER"
        echo ""
        echo "Usage: $0 [command] [options]"
        echo ""
        echo "Commands:"
        echo "  server          Start Flower SuperLink server"
        echo "  client TEAM     Start client for specific team"
        echo "  demo            Demo with 3 teams (ATL, CHI, GSV)"
        echo "  ray             Ray-based parallel training (5 teams)"
        echo "  all13           All 13 WNBA teams federated learning"
        echo "  help            Show this help"
        echo ""
        echo "Examples:"
        echo "  $0 server                    # Start server"
        echo "  $0 client ATL                # Start Atlanta Dream client"
        echo "  $0 client GSV                # Start Golden State Valkyries client"
        echo "  $0 demo                      # Quick demo"
        echo "  $0 ray                       # Ray parallel training"
        echo "  $0 all13                     # Full 13-team federation"
        echo ""
        echo "Teams: ATL, CHI, CON, DAL, GSV, IND, LAS, LV, MIN, NYL, PHO, SEA, WAS"
        echo "🆕 GSV = Golden State Valkyries (newest team!)"
        ;;
esac
