#!/usr/bin/env python3
"""
Test Production Completeness for WNBA Player Points Prediction Pipeline

This script tests all required functionality to ensure production readiness.
"""

import sys
import traceback
from pathlib import Path

def test_real_data_availability():
    """Test that real WNBA data is available"""
    print("🔍 TESTING REAL DATA AVAILABILITY")
    print("-" * 50)
    
    real_data_sources = [
        "consolidated_wnba/01_player_data/basic_stats/complete_real_wnba_features_with_metadata_processed.csv",
        "consolidated_wnba/01_player_data/basic_stats/multi_year_wnba_dataset.csv",
        "consolidated_wnba/mappings/real_player_mappings.json",
        "consolidated_wnba/mappings/real_team_mappings.json",
        "wnba_stadium_locations.csv"
    ]
    
    available_sources = []
    missing_sources = []
    
    for source in real_data_sources:
        if Path(source).exists():
            file_size = Path(source).stat().st_size / (1024 * 1024)  # MB
            available_sources.append(f"{source} ({file_size:.1f} MB)")
            print(f"   ✅ {source}: {file_size:.1f} MB")
        else:
            missing_sources.append(source)
            print(f"   ❌ {source}: Missing")
    
    print(f"\n📊 REAL DATA SUMMARY:")
    print(f"   ✅ Available: {len(available_sources)}")
    print(f"   ❌ Missing: {len(missing_sources)}")
    
    if len(available_sources) >= 2:
        print(f"   🎯 SUFFICIENT REAL DATA AVAILABLE")
        return True
    else:
        print(f"   ⚠️ INSUFFICIENT REAL DATA")
        return False

def test_model_trainer_methods():
    """Test that all required Model1Trainer methods exist and are callable"""
    print("\n🔧 TESTING MODEL1TRAINER METHODS")
    print("-" * 50)
    
    try:
        from train_model_1_real_data import Model1Trainer
        
        trainer = Model1Trainer()
        
        required_methods = [
            'prepare_real_training_data',
            'validate_target_distribution', 
            'load_and_split_data',
            'train_multiverse_ensemble',
            'test_multiverse_ensemble_system',
            'train_production_model'
        ]
        
        available_methods = []
        missing_methods = []
        
        for method_name in required_methods:
            if hasattr(trainer, method_name):
                method = getattr(trainer, method_name)
                if callable(method):
                    available_methods.append(method_name)
                    print(f"   ✅ {method_name}: Available and callable")
                else:
                    missing_methods.append(f"{method_name} (not callable)")
                    print(f"   ❌ {method_name}: Not callable")
            else:
                missing_methods.append(method_name)
                print(f"   ❌ {method_name}: Missing")
        
        print(f"\n📊 METHOD SUMMARY:")
        print(f"   ✅ Available: {len(available_methods)}")
        print(f"   ❌ Missing: {len(missing_methods)}")
        
        if len(available_methods) == len(required_methods):
            print(f"   🎯 ALL REQUIRED METHODS AVAILABLE")
            return True
        else:
            print(f"   ⚠️ MISSING METHODS: {missing_methods}")
            return False
            
    except Exception as e:
        print(f"   ❌ Error importing Model1Trainer: {e}")
        return False

def test_advanced_features():
    """Test that advanced ML features are available"""
    print("\n🚀 TESTING ADVANCED ML FEATURES")
    print("-" * 50)
    
    try:
        from train_model_1_real_data import (
            DriftDetector, 
            DynamicModelUpdater, 
            ModelMetrics,
            dynamic_difficulty_score,
            test_fairness,
            calculate_prediction_intervals
        )
        
        advanced_features = [
            ('DriftDetector', DriftDetector),
            ('DynamicModelUpdater', DynamicModelUpdater),
            ('ModelMetrics', ModelMetrics),
            ('dynamic_difficulty_score', dynamic_difficulty_score),
            ('test_fairness', test_fairness),
            ('calculate_prediction_intervals', calculate_prediction_intervals)
        ]
        
        available_features = []
        missing_features = []
        
        for name, feature in advanced_features:
            if feature is not None:
                available_features.append(name)
                print(f"   ✅ {name}: Available")
            else:
                missing_features.append(name)
                print(f"   ❌ {name}: Missing")
        
        print(f"\n📊 ADVANCED FEATURES SUMMARY:")
        print(f"   ✅ Available: {len(available_features)}")
        print(f"   ❌ Missing: {len(missing_features)}")
        
        if len(available_features) == len(advanced_features):
            print(f"   🎯 ALL ADVANCED FEATURES AVAILABLE")
            return True
        else:
            print(f"   ⚠️ MISSING FEATURES: {missing_features}")
            return False
            
    except Exception as e:
        print(f"   ❌ Error importing advanced features: {e}")
        traceback.print_exc()
        return False

def test_data_preparation():
    """Test data preparation with real data"""
    print("\n📊 TESTING DATA PREPARATION")
    print("-" * 50)
    
    try:
        from train_model_1_real_data import Model1Trainer
        
        trainer = Model1Trainer()
        
        # Test data preparation
        print("   🔄 Testing prepare_real_training_data...")
        data_path = trainer.prepare_real_training_data()
        
        if data_path and Path(data_path).exists():
            file_size = Path(data_path).stat().st_size / (1024 * 1024)  # MB
            print(f"   ✅ Data preparation successful: {data_path} ({file_size:.1f} MB)")
            
            # Test data loading and splitting
            print("   🔄 Testing load_and_split_data...")
            train_df, val_df, test_df, feature_cols = trainer.load_and_split_data(data_path)
            
            print(f"   ✅ Data splitting successful:")
            print(f"     📊 Train: {len(train_df):,} samples")
            print(f"     📊 Val: {len(val_df):,} samples") 
            print(f"     📊 Test: {len(test_df):,} samples")
            print(f"     🔧 Features: {len(feature_cols)}")
            
            return True
        else:
            print(f"   ❌ Data preparation failed")
            return False
            
    except Exception as e:
        print(f"   ❌ Error in data preparation: {e}")
        traceback.print_exc()
        return False

def test_production_training():
    """Test production training pipeline"""
    print("\n🚀 TESTING PRODUCTION TRAINING PIPELINE")
    print("-" * 50)
    
    try:
        from train_model_1_real_data import Model1Trainer
        
        trainer = Model1Trainer()
        
        # Test that production training method exists and is callable
        if hasattr(trainer, 'train_production_model') and callable(trainer.train_production_model):
            print("   ✅ train_production_model method available")
            
            # Test method signature
            import inspect
            sig = inspect.signature(trainer.train_production_model)
            params = list(sig.parameters.keys())
            print(f"   ✅ Method parameters: {params}")
            
            expected_params = ['epochs', 'enable_nas', 'enable_uncertainty', 'enable_fairness', 'enable_online_learning']
            has_expected_params = all(param in params for param in expected_params)
            
            if has_expected_params:
                print(f"   ✅ All expected parameters present")
                return True
            else:
                missing_params = [p for p in expected_params if p not in params]
                print(f"   ⚠️ Missing parameters: {missing_params}")
                return False
        else:
            print(f"   ❌ train_production_model method not available")
            return False
            
    except Exception as e:
        print(f"   ❌ Error testing production training: {e}")
        return False

def main():
    """Main test function"""
    print("🧪 PRODUCTION COMPLETENESS TEST")
    print("=" * 60)
    
    tests = [
        ("Real Data Availability", test_real_data_availability),
        ("Model Trainer Methods", test_model_trainer_methods),
        ("Advanced ML Features", test_advanced_features),
        ("Data Preparation", test_data_preparation),
        ("Production Training", test_production_training)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results[test_name] = result
        except Exception as e:
            print(f"❌ Test '{test_name}' failed with error: {e}")
            results[test_name] = False
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 PRODUCTION COMPLETENESS SUMMARY")
    print("=" * 60)
    
    passed = sum(results.values())
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {status} {test_name}")
    
    print(f"\n🎯 OVERALL RESULT: {passed}/{total} tests passed")
    
    if passed == total:
        print("🏆 PRODUCTION READY - All tests passed!")
        print("🚀 Ready to start production training with real WNBA data")
        return True
    else:
        print("⚠️ NOT PRODUCTION READY - Some tests failed")
        print("🔧 Fix failing tests before production training")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
