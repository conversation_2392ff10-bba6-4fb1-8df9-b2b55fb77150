#!/usr/bin/env python3
"""
🔄 UPDATE ALL MODELS TO USE EXPERT DATASET
=========================================

Updates ALL model training scripts to use the single expert dataset
instead of scattered multiple datasets.

What this script does:
1. Finds all training scripts in the codebase
2. Updates dataset paths to use: data/master/wnba_expert_dataset.csv
3. Standardizes data loading across all models
4. Creates backup of original scripts
5. Validates that all models can load the expert dataset

Author: WNBA Analytics Team
Date: 2025-07-12
"""

import os
import re
import shutil
from pathlib import Path
import logging
from datetime import datetime
from typing import List, Dict, Tuple
import json

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ModelUpdater:
    """Updates all model training scripts to use the expert dataset"""

    def __init__(self):
        self.expert_dataset_path = "data/master/wnba_expert_dataset.csv"
        self.backup_dir = Path("backups/model_scripts_backup")
        self.updated_files = []
        self.failed_files = []

        # Old dataset paths to replace
        self.old_dataset_paths = [
            "data/master/wnba_complete_dataset_2015_2025.csv",
            "data/master/wnba_definitive_master_dataset_FIXED.csv",
            "consolidated_wnba/04_training_data/player_props/comprehensive_wnba_2015_2025_training_data.csv",
            "consolidated_wnba/04_training_data/player_props/wnba_training_data.csv",
            "consolidated_wnba/04_training_data/player_props/clean_wnba_training_data.csv",
            # Variations
            "wnba_complete_dataset_2015_2025.csv",
            "wnba_definitive_master_dataset_FIXED.csv",
            "comprehensive_wnba_2015_2025_training_data.csv",
            "wnba_training_data.csv",
            "clean_wnba_training_data.csv"
        ]

    def find_training_scripts(self) -> List[Path]:
        """Find all training scripts in the codebase"""
        logger.info("🔍 Finding all training scripts...")

        training_scripts = []

        # Search patterns for training scripts
        patterns = [
            "**/train*.py",
            "**/model*.py",
            "**/*training*.py",
            "**/*model*.py"
        ]

        # Search in key directories
        search_dirs = [
            Path("."),
            Path("src/models"),
            Path("src/data_modules"),
            Path("consolidated_wnba")
        ]

        for search_dir in search_dirs:
            if search_dir.exists():
                for pattern in patterns:
                    for file_path in search_dir.glob(pattern):
                        if file_path.is_file() and file_path.suffix == '.py':
                            # Skip backup files and __pycache__
                            if '__pycache__' not in str(file_path) and 'backup' not in str(file_path):
                                training_scripts.append(file_path)

        # Remove duplicates
        training_scripts = list(set(training_scripts))

        logger.info(f"   ✅ Found {len(training_scripts)} training scripts")
        return training_scripts

    def create_backup(self, file_path: Path) -> Path:
        """Create backup of original file"""
        self.backup_dir.mkdir(parents=True, exist_ok=True)

        # Create backup with timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_name = f"{file_path.stem}_{timestamp}{file_path.suffix}"
        backup_path = self.backup_dir / backup_name

        shutil.copy2(file_path, backup_path)
        return backup_path

    def update_dataset_paths(self, content: str) -> Tuple[str, int]:
        """Update dataset paths in file content"""
        updated_content = content
        replacements_made = 0

        for old_path in self.old_dataset_paths:
            # Replace both quoted and unquoted paths
            patterns = [
                f'"{old_path}"',
                f"'{old_path}'",
                f'r"{old_path}"',
                f"r'{old_path}'",
                old_path  # Unquoted
            ]

            for pattern in patterns:
                if pattern in updated_content:
                    updated_content = updated_content.replace(pattern, f'"{self.expert_dataset_path}"')
                    replacements_made += 1

        return updated_content, replacements_made

    def add_expert_dataset_comment(self, content: str) -> str:
        """Add comment about using expert dataset"""
        comment = f"""
# 🎯 UPDATED TO USE EXPERT DATASET
# This script now uses the consolidated expert dataset: {self.expert_dataset_path}
# Updated on: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
# Expert dataset contains: 49,512 high-quality records with 840 features
# All duplicates removed, data quality validated
"""

        # Add comment after imports
        lines = content.split('\n')
        insert_index = 0

        # Find where to insert (after imports, before main code)
        for i, line in enumerate(lines):
            if line.strip().startswith('import ') or line.strip().startswith('from '):
                insert_index = i + 1
            elif line.strip() and not line.strip().startswith('#'):
                break

        lines.insert(insert_index, comment)
        return '\n'.join(lines)

    def update_single_file(self, file_path: Path) -> Dict:
        """Update a single training script"""
        logger.info(f"🔧 Updating {file_path}")

        try:
            # Read original content
            with open(file_path, 'r', encoding='utf-8') as f:
                original_content = f.read()

            # Check if file contains dataset references
            has_dataset_refs = any(old_path in original_content for old_path in self.old_dataset_paths)

            if not has_dataset_refs:
                logger.info(f"   ⏭️ No dataset references found, skipping")
                return {
                    'file': str(file_path),
                    'status': 'skipped',
                    'reason': 'No dataset references found',
                    'replacements': 0
                }

            # Create backup
            backup_path = self.create_backup(file_path)
            logger.info(f"   💾 Backup created: {backup_path}")

            # Update dataset paths
            updated_content, replacements = self.update_dataset_paths(original_content)

            if replacements > 0:
                # Add expert dataset comment
                updated_content = self.add_expert_dataset_comment(updated_content)

                # Write updated content
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(updated_content)

                logger.info(f"   ✅ Updated with {replacements} replacements")

                return {
                    'file': str(file_path),
                    'status': 'updated',
                    'replacements': replacements,
                    'backup': str(backup_path)
                }
            else:
                logger.info(f"   ⏭️ No replacements needed")
                return {
                    'file': str(file_path),
                    'status': 'no_changes',
                    'replacements': 0
                }

        except Exception as e:
            logger.error(f"   ❌ Error updating {file_path}: {e}")
            return {
                'file': str(file_path),
                'status': 'failed',
                'error': str(e),
                'replacements': 0
            }

    def update_all_models(self) -> Dict:
        """Update all model training scripts"""
        logger.info("🚀 UPDATING ALL MODELS TO USE EXPERT DATASET")
        logger.info("=" * 60)

        # Find all training scripts
        training_scripts = self.find_training_scripts()

        if not training_scripts:
            logger.warning("❌ No training scripts found!")
            return {'success': False, 'message': 'No training scripts found'}

        # Update each script
        results = []
        updated_count = 0
        skipped_count = 0
        failed_count = 0

        for script_path in training_scripts:
            result = self.update_single_file(script_path)
            results.append(result)

            if result['status'] == 'updated':
                updated_count += 1
                self.updated_files.append(str(script_path))
            elif result['status'] == 'skipped' or result['status'] == 'no_changes':
                skipped_count += 1
            elif result['status'] == 'failed':
                failed_count += 1
                self.failed_files.append(str(script_path))

        # Create summary report
        summary = {
            'timestamp': datetime.now().isoformat(),
            'expert_dataset_path': self.expert_dataset_path,
            'total_scripts_found': len(training_scripts),
            'updated_scripts': updated_count,
            'skipped_scripts': skipped_count,
            'failed_scripts': failed_count,
            'updated_files': self.updated_files,
            'failed_files': self.failed_files,
            'detailed_results': results
        }

        # Save summary report
        summary_path = f"model_update_summary_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(summary_path, 'w') as f:
            json.dump(summary, f, indent=2)

        logger.info("=" * 60)
        logger.info("🎉 MODEL UPDATE COMPLETE!")
        logger.info(f"📊 Scripts found: {len(training_scripts)}")
        logger.info(f"✅ Scripts updated: {updated_count}")
        logger.info(f"⏭️ Scripts skipped: {skipped_count}")
        logger.info(f"❌ Scripts failed: {failed_count}")
        logger.info(f"📄 Summary report: {summary_path}")

        if updated_count > 0:
            logger.info("")
            logger.info("🎯 ALL UPDATED MODELS NOW USE THE EXPERT DATASET:")
            logger.info(f"   📁 {self.expert_dataset_path}")
            logger.info(f"   📊 49,512 high-quality records")
            logger.info(f"   🔧 840 features")
            logger.info(f"   🧹 All duplicates removed")

        return summary


def main():
    """Main function"""

    print("🔄 UPDATING ALL MODELS TO USE EXPERT DATASET")
    print("=" * 50)
    print("🎯 Goal: Replace all scattered dataset references with:")
    print("   📁 data/master/wnba_expert_dataset.csv")
    print("   📊 49,512 high-quality records")
    print("   🔧 840 features")
    print("   🧹 All duplicates removed")
    print()

    # Verify expert dataset exists
    expert_path = Path("data/master/wnba_expert_dataset.csv")
    if not expert_path.exists():
        print("❌ Expert dataset not found!")
        print("   Please run create_expert_dataset.py first")
        return False

    print(f"✅ Expert dataset found: {expert_path}")
    print(f"   Size: {expert_path.stat().st_size / (1024*1024):.1f} MB")
    print()

    try:
        # Create updater and run updates
        updater = ModelUpdater()
        summary = updater.update_all_models()

        if summary['updated_scripts'] > 0:
            print()
            print("🚀 NEXT STEPS:")
            print("1. Test all updated models with the expert dataset")
            print("2. Verify model performance is maintained or improved")
            print("3. Remove old scattered dataset files (optional)")
            print("4. Update documentation to reference expert dataset")

        return True

    except Exception as e:
        logger.error(f"❌ Error updating models: {e}")
        return False


if __name__ == "__main__":
    success = main()
    if not success:
        exit(1)