#!/usr/bin/env python3
"""
Quick noise analysis for WNBA dataset
"""

import pandas as pd
import numpy as np
from pathlib import Path

def analyze_noise():
    """Quick noise analysis"""
    
    print("🔍 WNBA Dataset Noise Analysis")
    print("=" * 40)
    
    # Load dataset
    data_path = Path("data/master/wnba_definitive_master_dataset_FIXED.csv")
    if not data_path.exists():
        print("❌ Dataset not found")
        return
    
    # Load sample
    df = pd.read_csv(data_path, nrows=1000)
    print(f"📊 Sample loaded: {df.shape}")
    
    # Check target column
    if 'target' in df.columns:
        target_mean = df['target'].mean()
        target_std = df['target'].std()
        target_min = df['target'].min()
        target_max = df['target'].max()
        
        print(f"🎯 Target stats:")
        print(f"   Mean: {target_mean:.2f}")
        print(f"   Std: {target_std:.2f}")
        print(f"   Range: {target_min:.1f} - {target_max:.1f}")
        
        # Check for noise
        extreme_high = (df['target'] > 40).sum()
        extreme_low = (df['target'] < 0).sum()
        print(f"🔍 Potential noise:")
        print(f"   Extreme high (>40): {extreme_high}")
        print(f"   Negative values: {extreme_low}")
    
    # Check missing data
    total_cells = len(df) * len(df.columns)
    missing_cells = df.isna().sum().sum()
    missing_pct = missing_cells / total_cells * 100
    print(f"❌ Missing data: {missing_pct:.2f}%")
    
    # Check duplicates
    duplicates = df.duplicated().sum()
    print(f"🔄 Duplicate rows: {duplicates}")
    
    # Feature analysis
    exclude_cols = [
        'target', 'player_name', 'team_abbrev', 'game_id', 'game_date', 
        'year', 'player_id', 'team_id', 'season'
    ]
    feature_cols = [col for col in df.columns if col not in exclude_cols]
    print(f"🔧 Features available: {len(feature_cols)}")
    
    # Check feature correlations (sample)
    if len(feature_cols) > 10:
        sample_features = feature_cols[:10]
        corr_matrix = df[sample_features].corr().abs()
        high_corr = (corr_matrix > 0.9).sum().sum() - len(sample_features)  # Exclude diagonal
        print(f"🔗 High correlations (sample): {high_corr} pairs")
    
    print("✅ Noise analysis complete!")
    
    # Recommendations
    print("\n🎯 NOISE REDUCTION RECOMMENDATIONS:")
    if extreme_high > 0 or extreme_low > 0:
        print("   📊 Remove extreme target values")
    if missing_pct > 5:
        print("   ❌ Address missing data")
    if duplicates > 0:
        print("   🔄 Remove duplicate rows")
    print("   🔧 Reduce features from 637 to ~200 best")
    print("   🔗 Remove highly correlated features")
    print("   📈 Apply feature selection")

if __name__ == "__main__":
    analyze_noise()
