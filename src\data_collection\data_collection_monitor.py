#!/usr/bin/env python3
"""
Data Collection Monitor & Management System

This script provides monitoring, management, and control capabilities
for the automated WNBA data collection system.

Features:
- Real-time monitoring dashboard
- Collection status reports
- Manual data collection triggers
- Gap detection and filling
- Performance analytics
- System health checks

Author: WNBA Analytics Team
Date: 2025-07-11
"""

import pandas as pd
import numpy as np
import sqlite3
import json
from datetime import datetime, timedelta, date
from pathlib import Path
import matplotlib.pyplot as plt
import seaborn as sns
from automated_nba_api_collector import WNBADataCollector

class DataCollectionMonitor:
    """Monitor and manage the automated data collection system"""
    
    def __init__(self, db_path="wnba_data_tracking.db"):
        self.db_path = db_path
        self.collector = WNBADataCollector(db_path)
    
    def get_collection_summary(self, days=30):
        """Get comprehensive collection summary"""
        
        print(f"📊 DATA COLLECTION SUMMARY (Last {days} days)")
        print("=" * 60)
        
        conn = sqlite3.connect(self.db_path)
        
        # Overall statistics
        summary_query = f'''
            SELECT 
                endpoint,
                COUNT(*) as total_attempts,
                SUM(CASE WHEN status = 'SUCCESS' THEN 1 ELSE 0 END) as successful,
                SUM(records_collected) as total_records,
                AVG(records_collected) as avg_records,
                MAX(collection_date) as last_collection
            FROM data_collection_log 
            WHERE collection_date >= date('now', '-{days} days')
            GROUP BY endpoint
        '''
        
        summary_df = pd.read_sql_query(summary_query, conn)
        
        if len(summary_df) > 0:
            print("📈 Collection Statistics:")
            for _, row in summary_df.iterrows():
                success_rate = (row['successful'] / row['total_attempts']) * 100
                print(f"   {row['endpoint']}:")
                print(f"     Success rate: {success_rate:.1f}% ({row['successful']}/{row['total_attempts']})")
                print(f"     Total records: {row['total_records']:,}")
                print(f"     Avg per collection: {row['avg_records']:.0f}")
                print(f"     Last collection: {row['last_collection']}")
                print()
        else:
            print("⚠️ No collection data found in the specified period")
        
        # Daily collection trends
        daily_query = f'''
            SELECT 
                collection_date,
                COUNT(*) as collections,
                SUM(records_collected) as daily_records
            FROM data_collection_log 
            WHERE collection_date >= date('now', '-{days} days')
            GROUP BY collection_date
            ORDER BY collection_date
        '''
        
        daily_df = pd.read_sql_query(daily_query, conn)
        
        if len(daily_df) > 0:
            print("📅 Daily Collection Trends:")
            print(f"   Average collections per day: {daily_df['collections'].mean():.1f}")
            print(f"   Average records per day: {daily_df['daily_records'].mean():.0f}")
            print(f"   Most productive day: {daily_df.loc[daily_df['daily_records'].idxmax(), 'collection_date']}")
            print(f"   Records that day: {daily_df['daily_records'].max():,}")
        
        conn.close()
        
        return summary_df, daily_df
    
    def detect_data_gaps(self):
        """Detect gaps in data collection"""
        
        print(f"\n🔍 DETECTING DATA GAPS")
        print("=" * 60)
        
        conn = sqlite3.connect(self.db_path)
        
        # Check for missing days
        gap_query = '''
            SELECT 
                endpoint,
                MAX(collection_date) as last_collection,
                julianday('now') - julianday(MAX(collection_date)) as days_since_last
            FROM data_collection_log 
            GROUP BY endpoint
        '''
        
        gap_df = pd.read_sql_query(gap_query, conn)
        
        gaps_found = []
        
        if len(gap_df) > 0:
            print("⚠️ Data Collection Gaps:")
            for _, row in gap_df.iterrows():
                days_gap = row['days_since_last']
                if days_gap > 2:  # More than 2 days
                    gaps_found.append({
                        'endpoint': row['endpoint'],
                        'last_collection': row['last_collection'],
                        'days_gap': days_gap
                    })
                    print(f"   {row['endpoint']}: {days_gap:.0f} days since last collection")
        
        if not gaps_found:
            print("✅ No significant data gaps detected")
        
        # Check for failed collections
        failed_query = '''
            SELECT 
                endpoint,
                collection_date,
                status
            FROM data_collection_log 
            WHERE status != 'SUCCESS' AND collection_date >= date('now', '-7 days')
            ORDER BY collection_date DESC
        '''
        
        failed_df = pd.read_sql_query(failed_query, conn)
        
        if len(failed_df) > 0:
            print(f"\n❌ Recent Failed Collections ({len(failed_df)}):")
            for _, row in failed_df.iterrows():
                print(f"   {row['collection_date']} - {row['endpoint']}: {row['status']}")
        else:
            print(f"\n✅ No failed collections in last 7 days")
        
        conn.close()
        
        return gaps_found, failed_df
    
    def fill_data_gaps(self, gaps):
        """Attempt to fill identified data gaps"""
        
        if not gaps:
            print("✅ No gaps to fill")
            return
        
        print(f"\n🔧 FILLING DATA GAPS")
        print("=" * 60)
        
        for gap in gaps:
            endpoint = gap['endpoint']
            print(f"🔄 Attempting to fill gap for {endpoint}")
            
            # Try to collect current data
            data = self.collector.fetch_data_from_endpoint(endpoint)
            
            if data is not None:
                print(f"   ✅ Successfully collected {len(data)} records")
            else:
                print(f"   ❌ Failed to collect data for {endpoint}")
    
    def manual_collection_trigger(self, endpoint=None):
        """Manually trigger data collection"""
        
        print(f"\n🔄 MANUAL DATA COLLECTION TRIGGER")
        print("=" * 60)
        
        if endpoint:
            print(f"🎯 Collecting data for: {endpoint}")
            data = self.collector.fetch_data_from_endpoint(endpoint)
            
            if data is not None:
                print(f"✅ Successfully collected {len(data)} records")
                return data
            else:
                print(f"❌ Failed to collect data")
                return None
        else:
            print(f"🎯 Collecting data from all endpoints")
            return self.collector.collect_daily_data()
    
    def system_health_check(self):
        """Perform comprehensive system health check"""
        
        print(f"\n🏥 SYSTEM HEALTH CHECK")
        print("=" * 60)
        
        health_status = {
            'database_accessible': False,
            'log_file_exists': False,
            'config_file_exists': False,
            'main_dataset_exists': False,
            'recent_collections': 0,
            'api_connectivity': False
        }
        
        # Check database
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM data_collection_log")
            health_status['database_accessible'] = True
            health_status['recent_collections'] = cursor.fetchone()[0]
            conn.close()
            print("✅ Database: Accessible")
        except Exception as e:
            print(f"❌ Database: Error - {e}")
        
        # Check log file
        if Path('wnba_data_collector.log').exists():
            health_status['log_file_exists'] = True
            print("✅ Log file: Exists")
        else:
            print("❌ Log file: Missing")
        
        # Check config file
        if Path('automated_collection_config.json').exists():
            health_status['config_file_exists'] = True
            print("✅ Config file: Exists")
        else:
            print("❌ Config file: Missing")
        
        # Check main dataset
        if Path('wnba_ultimate_expert_dataset_cleaned.csv').exists():
            health_status['main_dataset_exists'] = True
            print("✅ Main dataset: Exists")
        else:
            print("❌ Main dataset: Missing")
        
        # Test API connectivity
        try:
            test_data = self.collector.fetch_data_from_endpoint('player_stats')
            if test_data is not None:
                health_status['api_connectivity'] = True
                print("✅ NBA API: Accessible")
            else:
                print("⚠️ NBA API: No data returned")
        except Exception as e:
            print(f"❌ NBA API: Error - {e}")
        
        # Overall health score
        health_score = sum(health_status.values()) / len(health_status) * 100
        print(f"\n🎯 Overall Health Score: {health_score:.1f}%")
        
        if health_score >= 80:
            print("🟢 System Status: HEALTHY")
        elif health_score >= 60:
            print("🟡 System Status: WARNING")
        else:
            print("🔴 System Status: CRITICAL")
        
        return health_status
    
    def generate_performance_report(self):
        """Generate comprehensive performance report"""
        
        print(f"\n📊 PERFORMANCE REPORT")
        print("=" * 60)
        
        conn = sqlite3.connect(self.db_path)
        
        # Performance metrics
        perf_query = '''
            SELECT 
                DATE(created_at) as date,
                COUNT(*) as total_attempts,
                SUM(CASE WHEN status = 'SUCCESS' THEN 1 ELSE 0 END) as successful,
                SUM(records_collected) as total_records,
                AVG(records_collected) as avg_records_per_attempt
            FROM data_collection_log 
            WHERE created_at >= datetime('now', '-30 days')
            GROUP BY DATE(created_at)
            ORDER BY date
        '''
        
        perf_df = pd.read_sql_query(perf_query, conn)
        
        if len(perf_df) > 0:
            # Calculate success rate
            perf_df['success_rate'] = (perf_df['successful'] / perf_df['total_attempts']) * 100
            
            print("📈 30-Day Performance Metrics:")
            print(f"   Average success rate: {perf_df['success_rate'].mean():.1f}%")
            print(f"   Total records collected: {perf_df['total_records'].sum():,}")
            print(f"   Average daily collections: {perf_df['total_attempts'].mean():.1f}")
            print(f"   Best day success rate: {perf_df['success_rate'].max():.1f}%")
            print(f"   Worst day success rate: {perf_df['success_rate'].min():.1f}%")
            
            # Trend analysis
            recent_avg = perf_df.tail(7)['success_rate'].mean()
            older_avg = perf_df.head(7)['success_rate'].mean()
            trend = recent_avg - older_avg
            
            if trend > 5:
                print(f"📈 Trend: IMPROVING (+{trend:.1f}%)")
            elif trend < -5:
                print(f"📉 Trend: DECLINING ({trend:.1f}%)")
            else:
                print(f"➡️ Trend: STABLE ({trend:+.1f}%)")
        
        conn.close()
        
        return perf_df
    
    def create_monitoring_dashboard(self):
        """Create a comprehensive monitoring dashboard"""
        
        print(f"\n📊 CREATING MONITORING DASHBOARD")
        print("=" * 60)
        
        # Get data
        summary_df, daily_df = self.get_collection_summary()
        gaps, failed_df = self.detect_data_gaps()
        health_status = self.system_health_check()
        perf_df = self.generate_performance_report()
        
        # Create dashboard summary
        dashboard = {
            'timestamp': datetime.now().isoformat(),
            'summary': {
                'total_endpoints': len(summary_df) if len(summary_df) > 0 else 0,
                'avg_success_rate': summary_df['successful'].sum() / summary_df['total_attempts'].sum() * 100 if len(summary_df) > 0 else 0,
                'total_records_30d': summary_df['total_records'].sum() if len(summary_df) > 0 else 0,
                'data_gaps': len(gaps),
                'failed_collections_7d': len(failed_df),
                'health_score': sum(health_status.values()) / len(health_status) * 100
            },
            'recommendations': []
        }
        
        # Generate recommendations
        if dashboard['summary']['health_score'] < 80:
            dashboard['recommendations'].append("System health below 80% - investigate issues")
        
        if dashboard['summary']['data_gaps'] > 0:
            dashboard['recommendations'].append(f"Fill {dashboard['summary']['data_gaps']} data gaps")
        
        if dashboard['summary']['avg_success_rate'] < 90:
            dashboard['recommendations'].append("Success rate below 90% - check API connectivity")
        
        if not dashboard['recommendations']:
            dashboard['recommendations'].append("System operating optimally")
        
        # Save dashboard
        with open('monitoring_dashboard.json', 'w') as f:
            json.dump(dashboard, f, indent=2)
        
        print(f"✅ Dashboard created: monitoring_dashboard.json")
        print(f"📊 Health Score: {dashboard['summary']['health_score']:.1f}%")
        print(f"🎯 Recommendations: {len(dashboard['recommendations'])}")
        
        return dashboard

def main():
    """Main monitoring function"""
    
    print("📊 WNBA DATA COLLECTION MONITORING SYSTEM")
    print("=" * 70)
    print("🎯 Comprehensive monitoring and management")
    print()
    
    # Initialize monitor
    monitor = DataCollectionMonitor()
    
    # Create comprehensive dashboard
    dashboard = monitor.create_monitoring_dashboard()
    
    # Show key metrics
    print(f"\n🎯 KEY METRICS:")
    print(f"   Health Score: {dashboard['summary']['health_score']:.1f}%")
    print(f"   Success Rate: {dashboard['summary']['avg_success_rate']:.1f}%")
    print(f"   Records (30d): {dashboard['summary']['total_records_30d']:,}")
    print(f"   Data Gaps: {dashboard['summary']['data_gaps']}")
    
    print(f"\n💡 RECOMMENDATIONS:")
    for rec in dashboard['recommendations']:
        print(f"   • {rec}")
    
    print(f"\n🚀 MONITORING SYSTEM READY!")
    print(f"   📊 Dashboard: monitoring_dashboard.json")
    print(f"   📝 Logs: wnba_data_collector.log")
    print(f"   🗄️ Database: wnba_data_tracking.db")
    
    return monitor

if __name__ == "__main__":
    monitor = main()
