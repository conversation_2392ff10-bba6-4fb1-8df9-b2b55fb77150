#!/usr/bin/env python3
"""
🏀 TEST MODELS ON LAST 7 DAYS OF WNBA GAMES
==========================================

Test our freshly trained models on games from July 5-12, 2025
to evaluate real-world performance.
"""

import pandas as pd
import numpy as np
import json
import requests
from datetime import datetime, timedelta
from pathlib import Path
import time
import sys
import warnings
warnings.filterwarnings('ignore')

# Add src to path
sys.path.append('src')

class WNBAModelTester:
    """Test WNBA models on recent games"""
    
    def __init__(self):
        """Initialize the tester"""
        self.prediction_server_url = "http://localhost:5000"
        self.test_results = {}
        self.master_data = None
        
        print("🏀 WNBA MODEL TESTER - LAST 7 DAYS")
        print("=" * 45)
        print(f"📅 Testing period: July 5-12, 2025")
        print(f"🎯 Target: Real game performance")
        
    def load_master_data(self):
        """Load the master dataset"""
        try:
            data_path = "data/master/wnba_definitive_master_dataset_FIXED.csv"
            if not Path(data_path).exists():
                print(f"❌ Master dataset not found: {data_path}")
                return False
            
            print("📊 Loading master dataset...")
            self.master_data = pd.read_csv(data_path)
            
            # Convert game_date to datetime
            self.master_data['game_date'] = pd.to_datetime(self.master_data['game_date'])
            
            print(f"✅ Loaded {len(self.master_data)} records")
            print(f"📅 Date range: {self.master_data['game_date'].min()} to {self.master_data['game_date'].max()}")
            
            return True
            
        except Exception as e:
            print(f"❌ Error loading master data: {e}")
            return False
    
    def get_last_7_days_games(self):
        """Get games from the last 7 days"""
        if self.master_data is None:
            print("❌ Master data not loaded")
            return None
        
        # Define the test period (July 5-12, 2025)
        end_date = datetime(2025, 7, 12)
        start_date = end_date - timedelta(days=7)
        
        print(f"🔍 Filtering games from {start_date.date()} to {end_date.date()}")
        
        # Filter games in the last 7 days
        recent_games = self.master_data[
            (self.master_data['game_date'] >= start_date) & 
            (self.master_data['game_date'] <= end_date)
        ].copy()
        
        if len(recent_games) == 0:
            print("⚠️ No games found in the last 7 days")
            print("📊 Available date range in data:")
            print(f"   Min: {self.master_data['game_date'].min()}")
            print(f"   Max: {self.master_data['game_date'].max()}")
            
            # Use the most recent games available
            recent_games = self.master_data.nlargest(100, 'game_date').copy()
            print(f"🔄 Using {len(recent_games)} most recent games instead")
        
        print(f"✅ Found {len(recent_games)} games to test")
        
        # Show game summary
        if len(recent_games) > 0:
            games_by_date = recent_games.groupby(recent_games['game_date'].dt.date).size()
            print("📅 Games by date:")
            for date, count in games_by_date.items():
                print(f"   {date}: {count} player performances")
        
        return recent_games
    
    def prepare_features(self, game_data):
        """Prepare features for prediction"""
        try:
            # Exclude non-feature columns
            exclude_cols = {
                'points', 'target', 'player_id', 'game_id', 'game_date', 
                'player_name', 'team', 'opponent', 'season'
            }
            
            feature_cols = [col for col in game_data.columns if col not in exclude_cols]
            features = game_data[feature_cols].values
            
            # Handle missing values
            features = np.nan_to_num(features, nan=0.0)
            
            return features, feature_cols
            
        except Exception as e:
            print(f"❌ Error preparing features: {e}")
            return None, None
    
    def test_prediction_server(self):
        """Test if prediction server is running"""
        try:
            response = requests.get(f"{self.prediction_server_url}/health", timeout=5)
            if response.status_code == 200:
                print("✅ Prediction server is running")
                return True
            else:
                print(f"❌ Prediction server returned {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ Prediction server not available: {e}")
            print("💡 Start the server with: python hybrid_prediction_server.py")
            return False
    
    def get_model_prediction(self, features, mode, player_name="Test Player", team="SEA"):
        """Get prediction from a specific model"""
        try:
            payload = {
                'mode': mode,
                'features': features.tolist(),
                'player_name': player_name,
                'team': team
            }
            
            response = requests.post(
                f"{self.prediction_server_url}/predict",
                json=payload,
                timeout=10
            )
            
            if response.status_code == 200:
                return response.json()
            else:
                return {'error': f'Server returned {response.status_code}'}
                
        except Exception as e:
            return {'error': str(e)}
    
    def test_all_models(self, test_games):
        """Test all models on the test games"""
        print("\n🧪 TESTING ALL MODELS")
        print("-" * 30)
        
        # Model modes to test
        model_modes = ['enhanced', 'hybrid_gnn', 'multitask', 'bayesian', 'multiverse', 'hybrid']
        
        # Initialize results
        results = {mode: {'predictions': [], 'actual': [], 'errors': []} for mode in model_modes}
        
        # Sample games for testing (limit to avoid overwhelming)
        sample_size = min(50, len(test_games))
        test_sample = test_games.sample(n=sample_size, random_state=42)
        
        print(f"🎯 Testing on {len(test_sample)} player performances")
        
        for idx, (_, game) in enumerate(test_sample.iterrows()):
            if idx % 10 == 0:
                print(f"   Progress: {idx}/{len(test_sample)}")
            
            # Prepare features
            features, _ = self.prepare_features(game.to_frame().T)
            if features is None:
                continue
            
            features = features[0]  # Get single row
            actual_points = game.get('points', game.get('target', 0))
            player_name = game.get('player_name', 'Unknown')
            team = game.get('team', 'UNK')
            
            # Test each model
            for mode in model_modes:
                prediction_result = self.get_model_prediction(features, mode, player_name, team)
                
                if 'prediction' in prediction_result:
                    predicted_points = prediction_result['prediction']
                    error = abs(predicted_points - actual_points)
                    
                    results[mode]['predictions'].append(predicted_points)
                    results[mode]['actual'].append(actual_points)
                    results[mode]['errors'].append(error)
                else:
                    print(f"⚠️ {mode} model failed: {prediction_result.get('error', 'Unknown error')}")
        
        return results
    
    def calculate_metrics(self, results):
        """Calculate performance metrics"""
        print("\n📊 MODEL PERFORMANCE RESULTS")
        print("=" * 50)
        
        metrics = {}
        
        for mode, data in results.items():
            if len(data['predictions']) > 0:
                predictions = np.array(data['predictions'])
                actual = np.array(data['actual'])
                errors = np.array(data['errors'])
                
                mae = np.mean(errors)
                rmse = np.sqrt(np.mean((predictions - actual) ** 2))
                mape = np.mean(np.abs((actual - predictions) / np.maximum(actual, 1))) * 100
                
                metrics[mode] = {
                    'mae': mae,
                    'rmse': rmse,
                    'mape': mape,
                    'predictions_count': len(predictions),
                    'avg_actual': np.mean(actual),
                    'avg_predicted': np.mean(predictions)
                }
                
                print(f"\n🤖 {mode.upper()} MODEL:")
                print(f"   📊 Predictions: {len(predictions)}")
                print(f"   🎯 MAE: {mae:.3f} points")
                print(f"   📈 RMSE: {rmse:.3f} points")
                print(f"   📊 MAPE: {mape:.1f}%")
                print(f"   ⚖️ Avg Actual: {np.mean(actual):.1f} points")
                print(f"   🔮 Avg Predicted: {np.mean(predictions):.1f} points")
            else:
                print(f"\n❌ {mode.upper()} MODEL: No successful predictions")
                metrics[mode] = {'error': 'No predictions'}
        
        return metrics
    
    def save_results(self, metrics, results):
        """Save test results"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Save detailed results
        output_data = {
            'timestamp': datetime.now().isoformat(),
            'test_period': 'July 5-12, 2025',
            'metrics': metrics,
            'raw_results': {mode: {
                'predictions': data['predictions'],
                'actual': data['actual'],
                'errors': data['errors']
            } for mode, data in results.items() if len(data['predictions']) > 0}
        }
        
        output_path = f"model_test_results_{timestamp}.json"
        with open(output_path, 'w') as f:
            json.dump(output_data, f, indent=2)
        
        print(f"\n💾 Results saved to: {output_path}")
        
        return output_path
    
    def run_complete_test(self):
        """Run the complete testing pipeline"""
        print("🚀 Starting complete model test...")
        
        # Step 1: Load data
        if not self.load_master_data():
            return False
        
        # Step 2: Get test games
        test_games = self.get_last_7_days_games()
        if test_games is None or len(test_games) == 0:
            return False
        
        # Step 3: Test server
        if not self.test_prediction_server():
            return False
        
        # Step 4: Test all models
        results = self.test_all_models(test_games)
        
        # Step 5: Calculate metrics
        metrics = self.calculate_metrics(results)
        
        # Step 6: Save results
        output_path = self.save_results(metrics, results)
        
        print("\n🎉 MODEL TESTING COMPLETE!")
        print(f"📊 Results saved to: {output_path}")
        
        return True


def main():
    """Main function"""
    tester = WNBAModelTester()
    success = tester.run_complete_test()
    
    if success:
        print("\n✅ All tests completed successfully!")
    else:
        print("\n❌ Testing failed. Check the logs above.")


if __name__ == "__main__":
    main()
