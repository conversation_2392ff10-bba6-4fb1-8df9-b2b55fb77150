#!/usr/bin/env python3
"""
WNBA Neural Model Retraining - Fix Bias Issue
Retrain the neural model with properly balanced data to fix the bias where it always predicts away team wins.
"""

import sys
import os
import pandas as pd
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, TensorDataset
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import accuracy_score, classification_report, confusion_matrix
import logging
import json
from datetime import datetime

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class WNBANeuralNetwork(nn.Module):
    """Improved neural network for WNBA game outcome prediction"""
    
    def __init__(self, input_dim: int, hidden_dim: int = 64, dropout_rate: float = 0.3):
        super(WNBANeuralNetwork, self).__init__()
        
        self.network = nn.Sequential(
            nn.Linear(input_dim, hidden_dim),
            nn.BatchNorm1d(hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout_rate),
            
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.BatchNorm1d(hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(dropout_rate),
            
            nn.Linear(hidden_dim // 2, 2)  # Binary classification: home win (1) or away win (0)
        )
        
        # Initialize weights properly
        for module in self.modules():
            if isinstance(module, nn.Linear):
                nn.init.xavier_uniform_(module.weight)
                nn.init.zeros_(module.bias)
    
    def forward(self, x):
        return self.network(x)

def load_and_prepare_data():
    """Load and prepare WNBA training data with proper team-level game outcome labels"""
    logger.info("🏀 Loading WNBA training data...")

    # Load the comprehensive WNBA training data
    data_path = "data/ml_training/wnba_training_data.csv"
    if not os.path.exists(data_path):
        logger.error(f"Training data not found at {data_path}")
        return None, None, None, None

    df = pd.read_csv(data_path, low_memory=False)
    logger.info(f"📊 Loaded {len(df)} records from WNBA training data")

    # Filter for game-level data with GAME_ID and WL columns
    game_data = df[(df['GAME_ID'].notna()) & (df['WL'].notna())].copy()
    logger.info(f"🎯 Found {len(game_data)} player-game records with Win/Loss data")

    if len(game_data) == 0:
        logger.error("No game data with Win/Loss information found!")
        return None, None, None, None

    # Aggregate player stats to team level for each game
    logger.info("🔄 Aggregating player stats to team level...")

    # Define stats to aggregate
    stat_columns = ['PTS', 'FGM', 'FGA', 'FG3M', 'FG3A', 'FTM', 'FTA',
                   'OREB', 'DREB', 'REB', 'AST', 'TOV', 'STL', 'BLK', 'MIN']

    # Convert stats to numeric
    for col in stat_columns:
        if col in game_data.columns:
            game_data[col] = pd.to_numeric(game_data[col], errors='coerce')

    # Group by GAME_ID and TEAM_ID to get team totals for each game
    team_game_stats = game_data.groupby(['GAME_ID', 'TEAM_ID']).agg({
        'PTS': 'sum',
        'FGM': 'sum',
        'FGA': 'sum',
        'FG3M': 'sum',
        'FG3A': 'sum',
        'FTM': 'sum',
        'FTA': 'sum',
        'OREB': 'sum',
        'DREB': 'sum',
        'REB': 'sum',
        'AST': 'sum',
        'TOV': 'sum',
        'STL': 'sum',
        'BLK': 'sum',
        'MIN': 'sum',
        'WL': 'first',  # Win/Loss is same for all players on team
        'TEAM_ABBREVIATION': 'first'
    }).reset_index()

    # Calculate derived stats
    team_game_stats['FG_PCT'] = team_game_stats['FGM'] / team_game_stats['FGA'].replace(0, np.nan)
    team_game_stats['FG3_PCT'] = team_game_stats['FG3M'] / team_game_stats['FG3A'].replace(0, np.nan)
    team_game_stats['FT_PCT'] = team_game_stats['FTM'] / team_game_stats['FTA'].replace(0, np.nan)

    logger.info(f"📊 Created {len(team_game_stats)} team-game records")

    # Create target variable: 1 for Win, 0 for Loss
    team_game_stats['target'] = (team_game_stats['WL'] == 'W').astype(int)

    # Check class balance
    win_count = team_game_stats['target'].sum()
    loss_count = len(team_game_stats) - win_count
    logger.info(f"📈 Class distribution: Wins: {win_count} ({win_count/len(team_game_stats)*100:.1f}%), Losses: {loss_count} ({loss_count/len(team_game_stats)*100:.1f}%)")

    # Select features for prediction
    feature_columns = ['PTS', 'FGM', 'FGA', 'FG_PCT', 'FG3M', 'FG3A', 'FG3_PCT',
                      'FTM', 'FTA', 'FT_PCT', 'OREB', 'DREB', 'REB', 'AST', 'TOV', 'STL', 'BLK']

    # Prepare feature matrix
    X = team_game_stats[feature_columns].copy()
    y = team_game_stats['target'].values

    # Handle missing values
    X = X.fillna(X.median())

    # Remove any infinite values
    X = X.replace([np.inf, -np.inf], np.nan).fillna(X.median())

    logger.info(f"✅ Prepared team-level feature matrix: {X.shape}, target vector: {y.shape}")

    return X, y, feature_columns, team_game_stats

def train_model():
    """Train the WNBA neural model with proper data balancing"""
    logger.info("🚀 Starting WNBA Neural Model Retraining...")
    
    # Load and prepare data
    X, y, feature_names, game_data = load_and_prepare_data()
    if X is None:
        logger.error("Failed to load training data")
        return False
    
    # Split the data
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.2, random_state=42, stratify=y
    )
    
    # Further split training data for validation
    X_train, X_val, y_train, y_val = train_test_split(
        X_train, y_train, test_size=0.2, random_state=42, stratify=y_train
    )
    
    logger.info(f"📊 Data splits - Train: {len(X_train)}, Val: {len(X_val)}, Test: {len(X_test)}")
    
    # Scale the features
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train)
    X_val_scaled = scaler.transform(X_val)
    X_test_scaled = scaler.transform(X_test)
    
    # Convert to PyTorch tensors
    X_train_tensor = torch.FloatTensor(X_train_scaled)
    y_train_tensor = torch.LongTensor(y_train)
    X_val_tensor = torch.FloatTensor(X_val_scaled)
    y_val_tensor = torch.LongTensor(y_val)
    X_test_tensor = torch.FloatTensor(X_test_scaled)
    y_test_tensor = torch.LongTensor(y_test)
    
    # Create data loaders
    train_dataset = TensorDataset(X_train_tensor, y_train_tensor)
    val_dataset = TensorDataset(X_val_tensor, y_val_tensor)
    test_dataset = TensorDataset(X_test_tensor, y_test_tensor)
    
    train_loader = DataLoader(train_dataset, batch_size=32, shuffle=True)
    val_loader = DataLoader(val_dataset, batch_size=32, shuffle=False)
    test_loader = DataLoader(test_dataset, batch_size=32, shuffle=False)
    
    # Initialize model
    input_dim = X_train_scaled.shape[1]
    model = WNBANeuralNetwork(input_dim=input_dim, hidden_dim=64, dropout_rate=0.3)
    
    # Loss function with class weights to handle any remaining imbalance
    class_counts = np.bincount(y_train)
    class_weights = len(y_train) / (2 * class_counts)
    class_weights_tensor = torch.FloatTensor(class_weights)
    criterion = nn.CrossEntropyLoss(weight=class_weights_tensor)
    
    # Optimizer
    optimizer = optim.Adam(model.parameters(), lr=0.001, weight_decay=1e-4)
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, patience=10, factor=0.5)
    
    # Training loop
    num_epochs = 100
    best_val_acc = 0.0
    patience = 15
    patience_counter = 0
    
    logger.info("🎯 Starting training...")
    
    for epoch in range(num_epochs):
        # Training phase
        model.train()
        train_loss = 0.0
        train_correct = 0
        train_total = 0
        
        for batch_X, batch_y in train_loader:
            optimizer.zero_grad()
            outputs = model(batch_X)
            loss = criterion(outputs, batch_y)
            loss.backward()
            optimizer.step()
            
            train_loss += loss.item()
            _, predicted = torch.max(outputs.data, 1)
            train_total += batch_y.size(0)
            train_correct += (predicted == batch_y).sum().item()
        
        train_acc = train_correct / train_total
        
        # Validation phase
        model.eval()
        val_loss = 0.0
        val_correct = 0
        val_total = 0
        
        with torch.no_grad():
            for batch_X, batch_y in val_loader:
                outputs = model(batch_X)
                loss = criterion(outputs, batch_y)
                
                val_loss += loss.item()
                _, predicted = torch.max(outputs.data, 1)
                val_total += batch_y.size(0)
                val_correct += (predicted == batch_y).sum().item()
        
        val_acc = val_correct / val_total
        scheduler.step(val_loss)
        
        if epoch % 10 == 0:
            logger.info(f"Epoch {epoch}/{num_epochs} - Train Acc: {train_acc:.3f}, Val Acc: {val_acc:.3f}")
        
        # Early stopping
        if val_acc > best_val_acc:
            best_val_acc = val_acc
            patience_counter = 0
            # Save best model
            torch.save({
                'model_state_dict': model.state_dict(),
                'scaler_params': {
                    'mean_': scaler.mean_.tolist(),
                    'scale_': scaler.scale_.tolist(),
                    'n_features_in_': scaler.n_features_in_
                },
                'feature_names': feature_names,
                'input_dim': input_dim,
                'class_weights': class_weights.tolist()
            }, 'best_wnba_neural_model_fixed.pth')
        else:
            patience_counter += 1
            if patience_counter >= patience:
                logger.info(f"Early stopping at epoch {epoch}")
                break
    
    # Load best model for final evaluation
    checkpoint = torch.load('best_wnba_neural_model_fixed.pth')
    model.load_state_dict(checkpoint['model_state_dict'])
    
    # Final evaluation
    model.eval()
    test_predictions = []
    test_targets = []
    
    with torch.no_grad():
        for batch_X, batch_y in test_loader:
            outputs = model(batch_X)
            _, predicted = torch.max(outputs.data, 1)
            test_predictions.extend(predicted.cpu().numpy())
            test_targets.extend(batch_y.cpu().numpy())
    
    test_acc = accuracy_score(test_targets, test_predictions)
    
    logger.info("🎉 Training completed!")
    logger.info(f"📊 Best validation accuracy: {best_val_acc:.3f}")
    logger.info(f"📊 Final test accuracy: {test_acc:.3f}")
    
    # Print detailed classification report
    logger.info("\n📋 Classification Report:")
    print(classification_report(test_targets, test_predictions, target_names=['Loss', 'Win']))
    
    # Print confusion matrix
    logger.info("\n🔍 Confusion Matrix:")
    cm = confusion_matrix(test_targets, test_predictions)
    print(cm)
    
    # Save training results
    results = {
        'timestamp': datetime.now().isoformat(),
        'best_val_accuracy': float(best_val_acc),
        'test_accuracy': float(test_acc),
        'feature_count': len(feature_names),
        'training_samples': len(X_train),
        'model_path': 'best_wnba_neural_model_fixed.pth'
    }
    
    with open('wnba_neural_training_results.json', 'w') as f:
        json.dump(results, f, indent=2)
    
    logger.info("✅ Model saved as 'best_wnba_neural_model_fixed.pth'")
    logger.info("✅ Training results saved as 'wnba_neural_training_results.json'")
    
    return True

if __name__ == "__main__":
    success = train_model()
    if success:
        logger.info("🎯 WNBA Neural Model Retraining Completed Successfully!")
    else:
        logger.error("❌ Training failed!")
        sys.exit(1)
