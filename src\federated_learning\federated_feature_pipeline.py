#!/usr/bin/env python3
"""
🔧 FEDERATED FEATURE ENGINEERING PIPELINE
==========================================

Ensures consistent data schema across all WNBA teams for federated learning.
All teams use the same feature columns, types, and preprocessing steps.

FEDERATED LEARNING PRINCIPLE:
- Identical feature engineering across all clients
- Consistent data types and column names
- Synchronized preprocessing steps
- Compatible with Model 1 pipeline
"""

import pandas as pd
import numpy as np
from typing import List, Tuple, Dict, Any
from pathlib import Path
import logging

logger = logging.getLogger(__name__)

class FederatedFeaturePipeline:
    """
    Standardized feature engineering pipeline for federated WNBA learning.
    Ensures all teams have identical data schemas.
    """
    
    def __init__(self):
        # Define the EXACT feature schema based on our ENHANCED comprehensive dataset
        self.REQUIRED_COLUMNS = [
            # Core stats (available in enhanced dataset)
            'minutes', 'rebounds', 'assists', 'steals', 'blocks', 'turnovers',
            'usage_rate', 'efficiency', 'team_pace', 'season_phase',

            # Game context (available in enhanced dataset)
            'is_home', 'rest_days', 'back_to_back', 'altitude',

            # Rolling averages (3-game) - available in enhanced dataset
            'points_avg_3', 'rebounds_avg_3', 'assists_avg_3',

            # Rolling averages (5-game) - available in enhanced dataset
            'points_avg_5', 'rebounds_avg_5', 'assists_avg_5',

            # Rolling averages (10-game) - available in enhanced dataset
            'points_avg_10', 'rebounds_avg_10', 'assists_avg_10',

            # EWMA features - available in enhanced dataset
            'points_ewma_5', 'points_ewma_10'
        ]
        
        # Target column
        self.TARGET_COLUMN = 'target'
        
        # Identifier columns (excluded from features but preserved)
        self.IDENTIFIER_COLUMNS = [
            'player_id', 'team_abbrev', 'game_id', 'game_date', 'year', 'player_name'
        ]
        
        # Data types for consistency (based on our enhanced comprehensive dataset)
        self.COLUMN_DTYPES = {
            'minutes': 'float32',
            'rebounds': 'float32',
            'assists': 'float32',
            'steals': 'float32',
            'blocks': 'float32',
            'turnovers': 'float32',
            'usage_rate': 'float32',
            'efficiency': 'float32',
            'team_pace': 'float32',
            'season_phase': 'float32',
            'is_home': 'int8',
            'rest_days': 'int16',
            'back_to_back': 'int8',
            'altitude': 'float32',
            'points_avg_3': 'float32',
            'rebounds_avg_3': 'float32',
            'assists_avg_3': 'float32',
            'points_avg_5': 'float32',
            'rebounds_avg_5': 'float32',
            'assists_avg_5': 'float32',
            'points_avg_10': 'float32',
            'rebounds_avg_10': 'float32',
            'assists_avg_10': 'float32',
            'points_ewma_5': 'float32',
            'points_ewma_10': 'float32',
            'target': 'float32'
        }
        
        logger.info(f"🔧 Federated feature pipeline initialized")
        logger.info(f"   📊 Required features: {len(self.REQUIRED_COLUMNS)}")
        logger.info(f"   🎯 Target: {self.TARGET_COLUMN}")
        logger.info(f"   🏷️ Identifiers: {len(self.IDENTIFIER_COLUMNS)}")
    
    def validate_and_standardize(self, df: pd.DataFrame, team_id: str) -> pd.DataFrame:
        """
        Validate and standardize team data to ensure federated compatibility.
        
        Args:
            df: Team's raw data
            team_id: Team identifier for logging
            
        Returns:
            Standardized DataFrame with consistent schema
        """
        logger.info(f"🔧 Standardizing {team_id} data for federated learning...")
        
        df = df.copy()
        
        # 1. Ensure target column exists
        if self.TARGET_COLUMN not in df.columns:
            if 'points' in df.columns:
                df[self.TARGET_COLUMN] = df['points'].astype('float32')
                logger.info(f"   ✅ Created target from points column")
            else:
                raise ValueError(f"No target or points column found for {team_id}")
        
        # 2. Check for missing required columns
        missing_cols = [col for col in self.REQUIRED_COLUMNS if col not in df.columns]
        if missing_cols:
            logger.warning(f"   ⚠️ Missing columns for {team_id}: {missing_cols}")
            
            # Create missing columns with default values
            for col in missing_cols:
                if 'avg' in col or 'ewma' in col:
                    df[col] = 0.0  # Rolling averages start at 0
                elif col == 'is_home':
                    df[col] = 0  # Binary feature default to 0
                elif col == 'rest_days':
                    df[col] = 1  # Default 1 day rest
                elif col in ['usage_rate', 'efficiency', 'team_pace', 'season_phase']:
                    # Use reasonable defaults for these features
                    defaults = {
                        'usage_rate': 20.0,
                        'efficiency': 10.0,
                        'team_pace': 85.0,
                        'season_phase': 0.5
                    }
                    df[col] = defaults.get(col, 0.0)
                else:
                    # For other features, try to derive from base stats
                    base_col = col.replace('_avg_3', '').replace('_avg_5', '').replace('_avg_10', '')
                    if base_col in df.columns:
                        df[col] = df[base_col].mean()
                    else:
                        df[col] = 0.0

                logger.info(f"     🔄 Created missing column: {col}")
        
        # 3. Apply consistent data types
        for col, dtype in self.COLUMN_DTYPES.items():
            if col in df.columns:
                try:
                    df[col] = df[col].astype(dtype)
                except Exception as e:
                    logger.warning(f"   ⚠️ Could not convert {col} to {dtype}: {e}")
        
        # 4. Handle missing values consistently
        for col in self.REQUIRED_COLUMNS:
            if col in df.columns:
                if df[col].isna().any():
                    if col in ['is_home', 'back_to_back']:
                        df[col] = df[col].fillna(0)
                    elif col == 'rest_days':
                        df[col] = df[col].fillna(1)
                    else:
                        df[col] = df[col].fillna(df[col].median())
                    
                    logger.info(f"     🔄 Filled missing values in {col}")
        
        # 5. Ensure consistent column order
        final_columns = self.IDENTIFIER_COLUMNS + self.REQUIRED_COLUMNS + [self.TARGET_COLUMN]
        available_columns = [col for col in final_columns if col in df.columns]
        df = df[available_columns]
        
        # 6. Validation checks
        feature_count = len([col for col in self.REQUIRED_COLUMNS if col in df.columns])
        
        logger.info(f"   ✅ {team_id} data standardized:")
        logger.info(f"     📊 Features: {feature_count}/{len(self.REQUIRED_COLUMNS)}")
        logger.info(f"     🎯 Target: {'✅' if self.TARGET_COLUMN in df.columns else '❌'}")
        logger.info(f"     📋 Total columns: {len(df.columns)}")
        logger.info(f"     🔒 Schema consistency: {'✅' if feature_count == len(self.REQUIRED_COLUMNS) else '⚠️'}")
        
        return df
    
    def get_feature_columns(self) -> List[str]:
        """Get the standardized list of feature columns."""
        return self.REQUIRED_COLUMNS.copy()
    
    def get_schema_info(self) -> Dict[str, Any]:
        """Get complete schema information for federated coordination."""
        return {
            'required_columns': self.REQUIRED_COLUMNS,
            'target_column': self.TARGET_COLUMN,
            'identifier_columns': self.IDENTIFIER_COLUMNS,
            'column_dtypes': self.COLUMN_DTYPES,
            'total_features': len(self.REQUIRED_COLUMNS),
            'schema_version': '1.0'
        }
    
    def validate_federated_compatibility(self, team_dfs: Dict[str, pd.DataFrame]) -> bool:
        """
        Validate that all teams have compatible schemas for federated learning.
        
        Args:
            team_dfs: Dictionary of {team_id: dataframe}
            
        Returns:
            True if all teams are compatible
        """
        logger.info(f"🔍 Validating federated compatibility across {len(team_dfs)} teams...")
        
        reference_cols = None
        compatible = True
        
        for team_id, df in team_dfs.items():
            feature_cols = [col for col in df.columns if col in self.REQUIRED_COLUMNS]
            
            if reference_cols is None:
                reference_cols = set(feature_cols)
                logger.info(f"   📋 Reference schema from {team_id}: {len(feature_cols)} features")
            else:
                current_cols = set(feature_cols)
                if current_cols != reference_cols:
                    missing = reference_cols - current_cols
                    extra = current_cols - reference_cols
                    
                    logger.error(f"   ❌ Schema mismatch for {team_id}:")
                    if missing:
                        logger.error(f"     Missing: {missing}")
                    if extra:
                        logger.error(f"     Extra: {extra}")
                    
                    compatible = False
                else:
                    logger.info(f"   ✅ {team_id}: Schema compatible")
        
        if compatible:
            logger.info(f"   🏆 All teams have compatible schemas for federated learning!")
        else:
            logger.error(f"   ❌ Schema incompatibilities detected - federated learning not possible")
        
        return compatible

# Global instance for consistent usage
federated_pipeline = FederatedFeaturePipeline()

def standardize_team_data(df: pd.DataFrame, team_id: str) -> Tuple[pd.DataFrame, List[str]]:
    """
    Convenience function to standardize team data for federated learning.
    
    Args:
        df: Team's raw data
        team_id: Team identifier
        
    Returns:
        Tuple of (standardized_df, feature_columns)
    """
    standardized_df = federated_pipeline.validate_and_standardize(df, team_id)
    feature_columns = federated_pipeline.get_feature_columns()
    
    return standardized_df, feature_columns

if __name__ == "__main__":
    # Test the federated feature pipeline
    print("🔧 TESTING FEDERATED FEATURE PIPELINE")
    print("=" * 45)
    
    pipeline = FederatedFeaturePipeline()
    
    # Display schema info
    schema = pipeline.get_schema_info()
    print(f"📊 Schema Information:")
    print(f"   Features: {schema['total_features']}")
    print(f"   Target: {schema['target_column']}")
    print(f"   Version: {schema['schema_version']}")
    
    print(f"\n📋 Required Features:")
    for i, col in enumerate(schema['required_columns'], 1):
        print(f"   {i:2d}. {col}")
    
    print(f"\n🏆 FEDERATED FEATURE PIPELINE READY!")
    print(f"   ✅ Consistent schema across all teams")
    print(f"   ✅ Compatible with Model 1 pipeline")
    print(f"   ✅ Ready for production federated learning")
