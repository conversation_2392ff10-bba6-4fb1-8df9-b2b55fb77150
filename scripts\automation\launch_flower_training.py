#!/usr/bin/env python3
# Launch Flower federated training for WNBA prediction

import flwr as fl
import sys
from pathlib import Path

# Add current directory to path
sys.path.append(str(Path(__file__).parent))

from modern_player_points_model import FederatedPlayerModel
from train_model_1_real_data import Model1Trainer

class WNBAFlowerClient(fl.client.NumPyClient):
    def __init__(self, team_id: str):
        self.team_id = team_id
        self.trainer = Model1Trainer()
        self.model = None
    
    def get_parameters(self):
        if self.model is None:
            # Initialize model
            self.model = FederatedPlayerModel(
                input_dim=50,  # Will be updated with real data
                team_id=self.team_id
            )
        
        # Return model parameters as numpy arrays
        return [param.detach().cpu().numpy() for param in self.model.parameters()]
    
    def set_parameters(self, parameters):
        if self.model is None:
            return
        
        # Set model parameters from numpy arrays
        import torch
        params_dict = zip(self.model.parameters(), parameters)
        for param, new_param in params_dict:
            param.data = torch.tensor(new_param)
    
    def fit(self, parameters, config):
        self.set_parameters(parameters)
        
        # Train model locally
        print(f"🏀 Training {self.team_id}...")
        
        # Simulate training (replace with real training)
        loss = 2.5  # Placeholder
        
        return self.get_parameters(), 100, {"loss": loss}
    
    def evaluate(self, parameters, config):
        self.set_parameters(parameters)
        
        # Evaluate model locally
        print(f"📊 Evaluating {self.team_id}...")
        
        # Simulate evaluation (replace with real evaluation)
        loss = 2.3  # Placeholder
        accuracy = 0.85  # Placeholder
        
        return loss, 100, {"accuracy": accuracy}

def start_client(team_id: str, server_address: str = "localhost:8080"):
    client = WNBAFlowerClient(team_id)
    fl.client.start_numpy_client(server_address, client)

if __name__ == "__main__":
    import argparse
    parser = argparse.ArgumentParser()
    parser.add_argument("--team", default="ATL", help="Team ID")
    parser.add_argument("--server", default="localhost:8080", help="Server address")
    args = parser.parse_args()
    
    start_client(args.team, args.server)
