#!/usr/bin/env python3
"""
🚨 EMERGENCY RETRAIN - WORKING MODELS
===================================

Emergency retraining to get working models with <2.0 MAE
"""

import pandas as pd
import numpy as np
import torch
import torch.nn as nn
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
from sklearn.ensemble import RandomForestRegressor
from sklearn.linear_model import Ridge
from sklearn.metrics import mean_absolute_error
import json
from datetime import datetime, timedelta
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

class EmergencyRetrainer:
    """Emergency retraining for working models"""
    
    def __init__(self):
        """Initialize emergency retrainer"""
        print("🚨 EMERGENCY RETRAIN - WORKING MODELS")
        print("=" * 45)
        print("🎯 Target: <2.0 MAE professional accuracy")
        
    def load_and_prepare_data(self):
        """Load and prepare data for emergency retraining"""
        try:
            print("\n📊 Loading and preparing training data...")
            
            # Load full dataset
            df = pd.read_csv('data/master/wnba_definitive_master_dataset_FIXED.csv', low_memory=False)
            df['game_date'] = pd.to_datetime(df['game_date'], errors='coerce')
            
            print(f"   Raw data: {len(df)} records")
            
            # Get target variable
            if 'points' in df.columns:
                target_col = 'points'
            elif 'target' in df.columns:
                target_col = 'target'
            else:
                print("❌ No target column found")
                return None
            
            # Clean data
            df = df[
                (df[target_col].notna()) & 
                (df[target_col] >= 0) & 
                (df[target_col] <= 50)
            ].copy()
            
            print(f"   Clean data: {len(df)} records")
            
            # Get robust features
            exclude_cols = {
                'points', 'target', 'player_id', 'game_id', 'game_date', 
                'player_name', 'team', 'opponent', 'season', 'Unnamed: 0'
            }
            
            # Select numeric features with good coverage
            feature_cols = []
            for col in df.columns:
                if col not in exclude_cols:
                    try:
                        df[col] = pd.to_numeric(df[col], errors='coerce')
                        if df[col].dtype in ['float64', 'int64']:
                            # Only keep features with <50% missing and some variance
                            missing_pct = df[col].isnull().sum() / len(df)
                            if missing_pct < 0.5 and df[col].nunique() > 1:
                                feature_cols.append(col)
                    except:
                        continue
            
            print(f"   Selected {len(feature_cols)} robust features")
            
            # Fill missing values and create feature matrix
            for col in feature_cols:
                df[col] = df[col].fillna(df[col].median())
            
            X = df[feature_cols].values.astype(np.float32)
            y = df[target_col].values.astype(np.float32)
            
            # Remove extreme outliers
            outlier_mask = (y >= np.percentile(y, 1)) & (y <= np.percentile(y, 99))
            X = X[outlier_mask]
            y = y[outlier_mask]
            
            print(f"   Final data: {X.shape[0]} samples, {X.shape[1]} features")
            print(f"   Target stats: mean={np.mean(y):.1f}, std={np.std(y):.1f}")
            
            return X, y, feature_cols
            
        except Exception as e:
            print(f"❌ Error preparing data: {e}")
            return None
    
    def create_test_split(self, X, y):
        """Create proper train/test split"""
        # Use last 20% as test (most recent data)
        split_idx = int(0.8 * len(X))
        
        X_train, X_test = X[:split_idx], X[split_idx:]
        y_train, y_test = y[:split_idx], y[split_idx:]
        
        print(f"   Train: {len(X_train)} samples")
        print(f"   Test: {len(X_test)} samples")
        
        return X_train, X_test, y_train, y_test
    
    def train_emergency_models(self, X_train, X_test, y_train, y_test):
        """Train emergency models for immediate use"""
        print("\n🚨 Training emergency models...")
        
        # Standardize features
        scaler = StandardScaler()
        X_train_scaled = scaler.fit_transform(X_train)
        X_test_scaled = scaler.transform(X_test)
        
        models = {}
        
        # 1. Random Forest (robust baseline)
        print("   🌲 Training Random Forest...")
        rf = RandomForestRegressor(
            n_estimators=100,
            max_depth=10,
            min_samples_split=10,
            random_state=42,
            n_jobs=-1
        )
        rf.fit(X_train, y_train)
        rf_pred = rf.predict(X_test)
        rf_mae = mean_absolute_error(y_test, rf_pred)
        
        models['random_forest'] = {
            'model': rf,
            'scaler': None,  # RF doesn't need scaling
            'mae': rf_mae,
            'predictions': rf_pred
        }
        print(f"      ✅ Random Forest MAE: {rf_mae:.3f}")
        
        # 2. Ridge Regression (linear baseline)
        print("   📈 Training Ridge Regression...")
        ridge = Ridge(alpha=1.0, random_state=42)
        ridge.fit(X_train_scaled, y_train)
        ridge_pred = ridge.predict(X_test_scaled)
        ridge_mae = mean_absolute_error(y_test, ridge_pred)
        
        models['ridge'] = {
            'model': ridge,
            'scaler': scaler,
            'mae': ridge_mae,
            'predictions': ridge_pred
        }
        print(f"      ✅ Ridge Regression MAE: {ridge_mae:.3f}")
        
        # 3. Simple Neural Network
        print("   🧠 Training Neural Network...")
        
        class SimpleNN(nn.Module):
            def __init__(self, input_dim):
                super().__init__()
                self.network = nn.Sequential(
                    nn.Linear(input_dim, 128),
                    nn.ReLU(),
                    nn.Dropout(0.2),
                    nn.Linear(128, 64),
                    nn.ReLU(),
                    nn.Dropout(0.2),
                    nn.Linear(64, 32),
                    nn.ReLU(),
                    nn.Linear(32, 1)
                )
            
            def forward(self, x):
                return self.network(x).squeeze()
        
        # Train neural network
        nn_model = SimpleNN(X_train_scaled.shape[1])
        optimizer = torch.optim.Adam(nn_model.parameters(), lr=0.001)
        criterion = nn.MSELoss()
        
        # Convert to tensors
        X_train_tensor = torch.FloatTensor(X_train_scaled)
        y_train_tensor = torch.FloatTensor(y_train)
        X_test_tensor = torch.FloatTensor(X_test_scaled)
        
        # Training loop
        nn_model.train()
        for epoch in range(100):
            optimizer.zero_grad()
            pred = nn_model(X_train_tensor)
            loss = criterion(pred, y_train_tensor)
            loss.backward()
            optimizer.step()
            
            if epoch % 20 == 0:
                print(f"      Epoch {epoch}, Loss: {loss.item():.4f}")
        
        # Test neural network
        nn_model.eval()
        with torch.no_grad():
            nn_pred = nn_model(X_test_tensor).numpy()
        
        nn_mae = mean_absolute_error(y_test, nn_pred)
        
        models['neural_network'] = {
            'model': nn_model,
            'scaler': scaler,
            'mae': nn_mae,
            'predictions': nn_pred
        }
        print(f"      ✅ Neural Network MAE: {nn_mae:.3f}")
        
        return models, y_test
    
    def evaluate_emergency_models(self, models, y_test):
        """Evaluate emergency models"""
        print("\n🏆 EMERGENCY MODEL EVALUATION")
        print("-" * 40)
        
        best_model = None
        best_mae = float('inf')
        
        for name, model_info in models.items():
            mae = model_info['mae']
            predictions = model_info['predictions']
            
            # Calculate additional metrics
            rmse = np.sqrt(np.mean((predictions - y_test) ** 2))
            mape = np.mean(np.abs((y_test - predictions) / np.maximum(y_test, 1))) * 100
            
            print(f"\n🤖 {name.upper()} MODEL:")
            print(f"   🎯 MAE: {mae:.3f} points")
            print(f"   📈 RMSE: {rmse:.3f} points")
            print(f"   📊 MAPE: {mape:.1f}%")
            print(f"   ⚖️ Avg Actual: {np.mean(y_test):.1f} points")
            print(f"   🔮 Avg Predicted: {np.mean(predictions):.1f} points")
            
            # Status assessment
            if mae < 2.0:
                status = "🏆 EXCELLENT"
            elif mae < 3.0:
                status = "✅ GOOD"
            elif mae < 4.0:
                status = "⚠️ ACCEPTABLE"
            else:
                status = "❌ POOR"
            
            print(f"   📊 Status: {status}")
            
            # Show sample predictions
            print(f"   📝 Sample predictions:")
            for i in range(min(5, len(predictions))):
                error = abs(predictions[i] - y_test[i])
                print(f"      Sample {i+1}: Predicted {predictions[i]:.1f}, Actual {y_test[i]:.1f} (Error: {error:.1f})")
            
            if mae < best_mae:
                best_mae = mae
                best_model = name
        
        print(f"\n🥇 BEST EMERGENCY MODEL: {best_model.upper()} (MAE: {best_mae:.3f})")
        
        return best_model, best_mae
    
    def save_emergency_models(self, models, feature_cols, best_model):
        """Save emergency models for immediate use"""
        print(f"\n💾 Saving emergency models...")
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Save model info
        model_info = {
            'timestamp': datetime.now().isoformat(),
            'best_model': best_model,
            'feature_count': len(feature_cols),
            'feature_columns': feature_cols,
            'models': {
                name: {
                    'mae': info['mae'],
                    'type': name
                } for name, info in models.items()
            }
        }
        
        # Save to JSON
        info_path = f"emergency_models_info_{timestamp}.json"
        with open(info_path, 'w') as f:
            json.dump(model_info, f, indent=2)
        
        # Save best model
        best_model_info = models[best_model]
        
        if best_model == 'neural_network':
            model_path = f"emergency_nn_model_{timestamp}.pth"
            torch.save({
                'model_state_dict': best_model_info['model'].state_dict(),
                'input_dim': len(feature_cols),
                'mae': best_model_info['mae']
            }, model_path)
            print(f"   ✅ Neural network saved to: {model_path}")
        
        print(f"   ✅ Model info saved to: {info_path}")
        
        return info_path, model_path if best_model == 'neural_network' else None
    
    def run_emergency_retrain(self):
        """Run complete emergency retraining"""
        print("🚨 Starting emergency retraining...")
        
        # Load and prepare data
        data = self.load_and_prepare_data()
        if data is None:
            return False
        
        X, y, feature_cols = data
        
        # Create train/test split
        X_train, X_test, y_train, y_test = self.create_test_split(X, y)
        
        # Train emergency models
        models, y_test = self.train_emergency_models(X_train, X_test, y_train, y_test)
        
        # Evaluate models
        best_model, best_mae = self.evaluate_emergency_models(models, y_test)
        
        # Save models
        info_path, model_path = self.save_emergency_models(models, feature_cols, best_model)
        
        # Final assessment
        print(f"\n🎯 EMERGENCY RETRAIN ASSESSMENT:")
        if best_mae < 2.0:
            print(f"   🏆 SUCCESS: {best_mae:.3f} MAE is professional-grade!")
            print(f"   ✅ Emergency models ready for production")
        elif best_mae < 3.0:
            print(f"   ✅ GOOD: {best_mae:.3f} MAE is acceptable")
            print(f"   ✅ Emergency models ready with monitoring")
        elif best_mae < 4.0:
            print(f"   ⚠️ ACCEPTABLE: {best_mae:.3f} MAE needs improvement")
            print(f"   🔧 Consider additional feature engineering")
        else:
            print(f"   ❌ POOR: {best_mae:.3f} MAE still unacceptable")
            print(f"   🔧 Requires data quality investigation")
        
        print(f"\n📊 Emergency models tested on {len(y_test)} samples")
        print(f"✅ Best model: {best_model.upper()}")
        
        return best_mae < 4.0


def main():
    """Main function"""
    retrainer = EmergencyRetrainer()
    success = retrainer.run_emergency_retrain()
    
    if success:
        print("\n✅ EMERGENCY RETRAIN COMPLETED SUCCESSFULLY!")
        print("🚨 Working models available for immediate use!")
    else:
        print("\n❌ Emergency retrain failed")


if __name__ == "__main__":
    main()
