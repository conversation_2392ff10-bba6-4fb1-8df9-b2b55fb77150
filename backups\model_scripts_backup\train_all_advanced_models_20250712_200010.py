#!/usr/bin/env python3
"""
🚀 TRAIN ALL ADVANCED WNBA MODELS - COMPLETE SYSTEM
=================================================

Train ALL advanced models from modern_player_points_model.py:
1. ✅ PlayerPointsModel (Enhanced base model)
2. 🔗 HybridPlayerPointsModel (Tabular + GNN)
3. 📊 MultiTaskPlayerModel (Multi-task learning)
4. 🎲 BayesianPlayerModel (Uncertainty quantification)
5. 🌐 FederatedPlayerModel (Federated learning)
6. 🌐 FederatedTrainer (Federated coordination)
7. 🔗 PlayerInteractionGNN (Graph Neural Networks)
8. 📈 DriftDetectorCallback (Temporal monitoring)

Using COMPLETE 10-year dataset: Real WNBA records (2015-2025)
Target: <2.0 MAE professional accuracy for ALL models
"""

import pandas as pd
import numpy as np
import torch
import torch.nn as nn
import pytorch_lightning as pl
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
from sklearn.feature_selection import SelectKBest, f_regression
from sklearn.metrics import mean_absolute_error
import json
from datetime import datetime, timedelta
from pathlib import Path
import sys
import warnings
warnings.filterwarnings('ignore')

# Add src to path for our actual models
sys.path.append('src')
sys.path.append('src/models')

# Import ALL advanced models + MULTIVERSE ENSEMBLE + NEW COMPONENTS
from models.modern_player_points_model import (
    PlayerPointsModel,
    HybridPlayerPointsModel,
    MultiTaskPlayerModel,
    BayesianPlayerModel,
    FederatedPlayerModel,
    FederatedTrainer,
    PlayerInteractionGNN,
    DriftDetectorCallback,
    PlayerPointsDataset,
    get_proper_role_assignments,  # Proper role assignment function
    # MULTIVERSE ENSEMBLE MODELS
    PossessionBasedModel,
    LineupChemistryModel,
    CumulativeFatigueModel,
    HighLeverageModel,
    TeamDynamicsModel,
    ContextualPerformanceModel,
    MultiverseEnsemble,
    # NEW STRATIFIED EVALUATION COMPONENTS
    RoleSpecificEnsemble,
    RoleClassifierModel,
    fantasy_penalty_score,
    advanced_sample_weighting,
    # ADVANCED EVALUATION COMPONENTS
    analyze_residual_bias,
    log_bias_analysis,
    auto_correction_feedback,
    simulate_fantasy_contests,
    log_simulation_results,
    MetaModel,
    PlayerEmbeddingModel,
    # AI-COACHED PREDICTIONS & LEADERBOARDS
    generate_ai_commentary,
    PlayerAccuracyLeaderboard,
    # ADVANCED PRODUCTION FEATURES
    adversarial_stress_test,
    log_stress_test_results,
    LineMovementWatchdog,
    simulate_counterfactual_scenarios,
    MedusaAutopilot
)

# TRAINING CONFIGURATION - No more hardcoded values!
TRAINING_CONFIG = {
    'max_epochs': 100,  # Professional-grade training epochs
    'patience': 15,     # Early stopping patience
    'min_features': 50, # Minimum features to select
    'max_features': 200 # Maximum features to select
}

class AdvancedWNBATrainer:
    """Train ALL advanced WNBA models from the codebase"""
    
    def __init__(self):
        """Initialize the advanced WNBA trainer"""
        print("🚀 TRAIN ALL ADVANCED WNBA MODELS - COMPLETE SYSTEM")
        print("=" * 65)
        # Dataset info will be printed after loading the actual data
        print("🎯 Training ALL advanced models from modern_player_points_model.py")
        print()
        print("🤖 COMPREHENSIVE MODELS TO TRAIN WITH EXPERT ROLE ASSIGNMENTS:")
        print("   CORE MODELS:")
        print("   1. ✅ PlayerPointsModel (Enhanced base model)")
        print("   2. 🔗 HybridPlayerPointsModel (Tabular + GNN)")
        print("   3. 📊 MultiTaskPlayerModel (Multi-task learning)")
        print("   4. 🎲 BayesianPlayerModel (FIXED - Uncertainty quantification)")
        print("   5. 🌐 FederatedPlayerModel (Federated learning)")
        print("   MULTIVERSE ENSEMBLE:")
        print("   6. 🏀 PossessionBasedModel (Possession efficiency)")
        print("   7. 🤝 LineupChemistryModel (Player interactions)")
        print("   8. 😴 CumulativeFatigueModel (Fatigue modeling)")
        print("   9. 🔥 HighLeverageModel (Clutch situations)")
        print("   10. 🤝 TeamDynamicsModel (Team dynamics)")
        print("   11. 🌍 ContextualPerformanceModel (Environmental factors)")
        print("   NEW STRATIFIED COMPONENTS:")
        print("   12. 🎯 RoleSpecificEnsemble (Tier-specialized models)")
        print("   13. 🤖 RoleClassifierModel (Multi-task role prediction)")
        print("   🎯 ALL models use expert mappings with season dates breakdown")
        print("   🎯 Real player mappings + comprehensive analytics")
        print("   8. 📈 DriftDetectorCallback (Temporal monitoring)")
        
    def load_and_prepare_master_dataset(self):
        """Load and prepare the complete master dataset"""
        try:
            print("\n📊 Loading FULL master dataset...")
            
            # Load the COMPLETE 10-year dataset (2015-2025)
            df = pd.read_csv('data/master/wnba_complete_dataset_2015_2025.csv', low_memory=False)
            df['game_date'] = pd.to_datetime(df['game_date'], errors='coerce')
            
            print(f"   ✅ Loaded: {len(df)} records, {len(df.columns)} columns")
            print(f"   📅 Date range: {df['game_date'].min()} to {df['game_date'].max()}")

            # Clean data - keep as much real data as possible
            print("   🧹 Cleaning real WNBA data...")

            df_clean = df[
                (df['target'].notna()) &
                (df['target'] >= 0) &
                (df['target'] <= 60) &  # Generous WNBA range
                (df['game_date'].notna())
            ].copy()

            print(f"   After cleaning: {len(df_clean)} records ({len(df_clean)/len(df)*100:.1f}% retained)")

            # Print the REAL dataset summary
            print()
            print(f"📊 USING {len(df_clean):,} RECORDS WITH REAL WNBA DATA (2015-2025)")
            print(f"🎯 Complete 10-year dataset with {len(df.columns)} total features")
            
            # Feature preparation
            print("   🎯 Preparing features for ALL models...")
            
            exclude_cols = {
                'target', 'points', 'player_name', 'team_abbrev', 'game_date', 
                'game_id', 'PLAYER_ID', 'PLAYER_NAME', 'TEAM_ID', 'TEAM_ABBREVIATION',
                'NICKNAME', 'TEAM_NAME', 'team_name', 'SEASON_ID', 'GAME_ID', 
                'GAME_DATE', 'MATCHUP', 'data_source', 'collection_date'
            }
            
            potential_features = [col for col in df_clean.columns if col not in exclude_cols]
            print(f"   Potential features: {len(potential_features)}")
            
            # Process features
            feature_data = {}
            valid_features = []
            
            for col in potential_features:
                try:
                    series = pd.to_numeric(df_clean[col], errors='coerce')
                    missing_pct = series.isnull().sum() / len(series)
                    unique_values = series.nunique()
                    
                    if missing_pct < 0.8 and unique_values > 1:
                        median_val = series.median()
                        if pd.isna(median_val):
                            median_val = 0.0
                        
                        feature_data[col] = series.fillna(median_val).values
                        valid_features.append(col)
                        
                except Exception as e:
                    continue
            
            print(f"   Valid features after cleaning: {len(valid_features)}")
            
            # Feature selection
            X = np.column_stack([feature_data[col] for col in valid_features])
            X = X.astype(np.float32)
            y = df_clean['target'].values.astype(np.float32)
            
            # Select best features - dynamic based on dataset size
            # Use config values instead of hardcoded numbers
            optimal_features = min(TRAINING_CONFIG['max_features'],
                                 max(TRAINING_CONFIG['min_features'], len(valid_features) // 3))
            n_features = min(optimal_features, len(valid_features))
            selector = SelectKBest(score_func=f_regression, k=n_features)
            X_selected = selector.fit_transform(X, y)
            
            selected_indices = selector.get_support(indices=True)
            selected_features = [valid_features[i] for i in selected_indices]
            
            print(f"   ✅ Selected {X_selected.shape[1]} best features")
            print(f"📊 FINAL DATASET: {len(df_clean):,} records with {X_selected.shape[1]} features")
            
            # 🎯 FIXED: Use proper role assignments from real player mappings
            print(f"   🎯 Applying PROPER role assignments from real player mappings...")
            role_ids = get_proper_role_assignments(df_clean)
            print(f"   📊 PROPER Role distribution: Elite={np.sum(role_ids==0)}, Rotation={np.sum(role_ids==1)}, Bench={np.sum(role_ids==2)}")
            
            # Temporal split
            df_sorted = df_clean.sort_values('game_date').reset_index(drop=True)
            split_idx = int(0.8 * len(df_sorted))
            
            train_indices = df_sorted.index[:split_idx]
            test_indices = df_sorted.index[split_idx:]
            
            X_train = X_selected[train_indices]
            X_test = X_selected[test_indices]
            y_train = y[train_indices]
            y_test = y[test_indices]
            role_train = role_ids[train_indices]
            role_test = role_ids[test_indices]
            
            # Scale features
            scaler = StandardScaler()
            X_train_scaled = scaler.fit_transform(X_train)
            X_test_scaled = scaler.transform(X_test)
            
            print(f"   📊 Train: {len(X_train)} samples, Test: {len(X_test)} samples")
            
            return {
                'X_train': X_train_scaled,
                'X_test': X_test_scaled,
                'y_train': y_train,
                'y_test': y_test,
                'role_train': role_train,
                'role_test': role_test,
                'features': selected_features,
                'scaler': scaler,
                'input_dim': X_train_scaled.shape[1]
            }
            
        except Exception as e:
            print(f"❌ Error preparing master dataset: {e}")
            return None
    
    def create_data_loaders(self, data):
        """Create PyTorch data loaders for all models"""
        print("\n📦 Creating data loaders for all models...")
        
        train_dataset = PlayerPointsDataset(
            data['X_train'], data['y_train'], data['role_train']
        )
        test_dataset = PlayerPointsDataset(
            data['X_test'], data['y_test'], data['role_test']
        )
        
        # Dynamic batch size based on dataset size
        dataset_size = len(train_dataset)
        if dataset_size > 40000:
            batch_size = 256  # Large dataset
        elif dataset_size > 20000:
            batch_size = 128  # Medium dataset
        else:
            batch_size = 64   # Smaller dataset

        train_loader = torch.utils.data.DataLoader(
            train_dataset, batch_size=batch_size, shuffle=True, num_workers=0, pin_memory=True
        )
        test_loader = torch.utils.data.DataLoader(
            test_dataset, batch_size=batch_size, shuffle=False, num_workers=0, pin_memory=True
        )
        
        print(f"   📦 Dynamic batch size: {batch_size} (dataset size: {dataset_size:,})")
        print(f"   Train loader: {len(train_loader)} batches")
        print(f"   Test loader: {len(test_loader)} batches")
        
        return train_loader, test_loader
    
    def train_enhanced_model(self, train_loader, test_loader, input_dim):
        """Train Enhanced PlayerPointsModel"""
        print("\n🤖 Training Enhanced PlayerPointsModel...")
        
        model = PlayerPointsModel(
            input_dim=input_dim,
            dropout=0.2,
            learning_rate=1e-3,
            use_role_embedding=True
        )
        
        trainer = pl.Trainer(
            max_epochs=TRAINING_CONFIG['max_epochs'],
            accelerator='auto',
            devices=1,
            callbacks=[
                pl.callbacks.EarlyStopping(monitor='val_loss', patience=TRAINING_CONFIG['patience'], mode='min'),
                pl.callbacks.ModelCheckpoint(
                    monitor='val_loss', mode='min', save_top_k=1,
                    filename='enhanced_{epoch:02d}_{val_loss:.4f}',
                    dirpath='models/enhanced'
                )
            ],
            enable_progress_bar=True
        )
        
        Path('models/enhanced').mkdir(parents=True, exist_ok=True)
        trainer.fit(model, train_loader, test_loader)
        
        best_model = PlayerPointsModel.load_from_checkpoint(trainer.checkpoint_callback.best_model_path)
        best_model.eval()
        
        print(f"   ✅ Enhanced model trained! Best val loss: {trainer.checkpoint_callback.best_model_score:.4f}")
        
        return best_model, trainer.checkpoint_callback.best_model_path
    
    def train_multitask_model(self, train_loader, test_loader, input_dim):
        """Train MultiTask PlayerModel"""
        print("\n📊 Training MultiTaskPlayerModel...")
        
        model = MultiTaskPlayerModel(
            input_dim=input_dim,
            dropout=0.2,
            learning_rate=5e-4,
            use_role_embedding=True
        )
        
        trainer = pl.Trainer(
            max_epochs=TRAINING_CONFIG['max_epochs'],
            accelerator='auto',
            devices=1,
            callbacks=[
                pl.callbacks.EarlyStopping(monitor='val_loss', patience=TRAINING_CONFIG['patience'], mode='min'),
                pl.callbacks.ModelCheckpoint(
                    monitor='val_loss', mode='min', save_top_k=1,
                    filename='multitask_{epoch:02d}_{val_loss:.4f}',
                    dirpath='models/multitask'
                )
            ],
            enable_progress_bar=True
        )
        
        Path('models/multitask').mkdir(parents=True, exist_ok=True)
        trainer.fit(model, train_loader, test_loader)
        
        best_model = MultiTaskPlayerModel.load_from_checkpoint(trainer.checkpoint_callback.best_model_path)
        best_model.eval()
        
        print(f"   ✅ MultiTask model trained! Best val loss: {trainer.checkpoint_callback.best_model_score:.4f}")
        
        return best_model, trainer.checkpoint_callback.best_model_path
    
    def train_bayesian_model(self, train_loader, test_loader, input_dim):
        """Train Bayesian PlayerModel"""
        print("\n🎲 Training BayesianPlayerModel...")
        
        # FIXED: Use corrected Bayesian model parameters
        model = BayesianPlayerModel(
            input_dim=input_dim,
            dropout=0.2,
            learning_rate=5e-4,  # FIXED: Lowered learning rate
            use_role_embedding=True,
            kl_weight=0.001,  # FIXED: Reduced KL weight
            n_samples=5  # FIXED: Reduced MC samples
        )
        
        trainer = pl.Trainer(
            max_epochs=TRAINING_CONFIG['max_epochs'],
            accelerator='auto',
            devices=1,
            callbacks=[
                pl.callbacks.EarlyStopping(monitor='val_loss', patience=TRAINING_CONFIG['patience'], mode='min'),
                pl.callbacks.ModelCheckpoint(
                    monitor='val_loss', mode='min', save_top_k=1,
                    filename='bayesian_{epoch:02d}_{val_loss:.4f}',
                    dirpath='models/bayesian'
                )
            ],
            enable_progress_bar=True
        )
        
        Path('models/bayesian').mkdir(parents=True, exist_ok=True)
        trainer.fit(model, train_loader, test_loader)
        
        best_model = BayesianPlayerModel.load_from_checkpoint(trainer.checkpoint_callback.best_model_path)
        best_model.eval()
        
        print(f"   ✅ Bayesian model trained! Best val loss: {trainer.checkpoint_callback.best_model_score:.4f}")
        
        return best_model, trainer.checkpoint_callback.best_model_path

    def train_hybrid_model(self, train_loader, test_loader, input_dim):
        """Train Hybrid PlayerModel (Tabular + GNN)"""
        print("\n🔗 Training HybridPlayerPointsModel...")

        model = HybridPlayerPointsModel(
            tabular_input_dim=input_dim,
            gnn_input_dim=64,
            gnn_hidden_dim=64,
            gnn_output_dim=32,
            dropout=0.2,
            use_role_embedding=True
        )

        trainer = pl.Trainer(
            max_epochs=TRAINING_CONFIG['max_epochs'],
            accelerator='auto',
            devices=1,
            callbacks=[
                pl.callbacks.EarlyStopping(monitor='val_loss', patience=TRAINING_CONFIG['patience'], mode='min'),
                pl.callbacks.ModelCheckpoint(
                    monitor='val_loss', mode='min', save_top_k=1,
                    filename='hybrid_{epoch:02d}_{val_loss:.4f}',
                    dirpath='models/hybrid'
                )
            ],
            enable_progress_bar=True
        )

        trainer.fit(model, train_loader, test_loader)
        best_path = trainer.checkpoint_callback.best_model_path

        print(f"   ✅ Hybrid model saved: {best_path}")
        return model, best_path

    def train_possession_based_model(self, train_loader, test_loader, input_dim):
        """Train PossessionBasedModel (Multiverse 1)"""
        print("\n🏀 Training PossessionBasedModel...")

        model = PossessionBasedModel(
            input_dim=input_dim,
            dropout=0.25,
            learning_rate=8e-4,
            use_role_embedding=True
        )

        trainer = pl.Trainer(
            max_epochs=TRAINING_CONFIG['max_epochs'],
            accelerator='auto',
            devices=1,
            callbacks=[
                pl.callbacks.EarlyStopping(monitor='val_loss', patience=TRAINING_CONFIG['patience'], mode='min'),
                pl.callbacks.ModelCheckpoint(
                    monitor='val_loss', mode='min', save_top_k=1,
                    filename='possession_{epoch:02d}_{val_loss:.4f}',
                    dirpath='models/multiverse/possession'
                )
            ],
            enable_progress_bar=True
        )

        trainer.fit(model, train_loader, test_loader)
        best_path = trainer.checkpoint_callback.best_model_path

        print(f"   ✅ PossessionBasedModel saved: {best_path}")
        return model, best_path

    def train_lineup_chemistry_model(self, train_loader, test_loader, input_dim):
        """Train LineupChemistryModel (Multiverse 2)"""
        print("\n🤝 Training LineupChemistryModel...")

        model = LineupChemistryModel(
            tabular_input_dim=input_dim,
            gnn_input_dim=64,
            gnn_hidden_dim=64,
            gnn_output_dim=32,
            dropout=0.2,
            use_role_embedding=True
        )

        trainer = pl.Trainer(
            max_epochs=TRAINING_CONFIG['max_epochs'],
            accelerator='auto',
            devices=1,
            callbacks=[
                pl.callbacks.EarlyStopping(monitor='val_loss', patience=TRAINING_CONFIG['patience'], mode='min'),
                pl.callbacks.ModelCheckpoint(
                    monitor='val_loss', mode='min', save_top_k=1,
                    filename='lineup_{epoch:02d}_{val_loss:.4f}',
                    dirpath='models/multiverse/lineup'
                )
            ],
            enable_progress_bar=True
        )

        trainer.fit(model, train_loader, test_loader)
        best_path = trainer.checkpoint_callback.best_model_path

        print(f"   ✅ LineupChemistryModel saved: {best_path}")
        return model, best_path

    def train_fatigue_model(self, train_loader, test_loader, input_dim):
        """Train CumulativeFatigueModel (Multiverse 3)"""
        print("\n😴 Training CumulativeFatigueModel...")

        model = CumulativeFatigueModel(
            input_dim=input_dim,
            dropout=0.3,
            learning_rate=6e-4,
            use_role_embedding=True
        )

        trainer = pl.Trainer(
            max_epochs=TRAINING_CONFIG['max_epochs'],
            accelerator='auto',
            devices=1,
            callbacks=[
                pl.callbacks.EarlyStopping(monitor='val_loss', patience=TRAINING_CONFIG['patience'], mode='min'),
                pl.callbacks.ModelCheckpoint(
                    monitor='val_loss', mode='min', save_top_k=1,
                    filename='fatigue_{epoch:02d}_{val_loss:.4f}',
                    dirpath='models/multiverse/fatigue'
                )
            ],
            enable_progress_bar=True
        )

        trainer.fit(model, train_loader, test_loader)
        best_path = trainer.checkpoint_callback.best_model_path

        print(f"   ✅ CumulativeFatigueModel saved: {best_path}")
        return model, best_path

    def train_high_leverage_model(self, train_loader, test_loader, input_dim):
        """Train HighLeverageModel (Multiverse 4)"""
        print("\n🔥 Training HighLeverageModel...")

        model = HighLeverageModel(
            input_dim=input_dim,
            dropout=0.25,
            learning_rate=7e-4,
            use_role_embedding=True
        )

        trainer = pl.Trainer(
            max_epochs=TRAINING_CONFIG['max_epochs'],
            accelerator='auto',
            devices=1,
            callbacks=[
                pl.callbacks.EarlyStopping(monitor='val_loss', patience=TRAINING_CONFIG['patience'], mode='min'),
                pl.callbacks.ModelCheckpoint(
                    monitor='val_loss', mode='min', save_top_k=1,
                    filename='leverage_{epoch:02d}_{val_loss:.4f}',
                    dirpath='models/multiverse/leverage'
                )
            ],
            enable_progress_bar=True
        )

        trainer.fit(model, train_loader, test_loader)
        best_path = trainer.checkpoint_callback.best_model_path

        print(f"   ✅ HighLeverageModel saved: {best_path}")
        return model, best_path

    def train_team_dynamics_model(self, train_loader, test_loader, input_dim):
        """Train TeamDynamicsModel (Multiverse 5)"""
        print("\n🤝 Training TeamDynamicsModel...")

        model = TeamDynamicsModel(
            input_dim=input_dim,
            dropout=0.3,
            learning_rate=5e-4,
            use_role_embedding=True
        )

        # Add validation spike detection
        class ValidationSpikeDetector(pl.Callback):
            def __init__(self, spike_threshold=0.5):
                self.spike_threshold = spike_threshold
                self.prev_val_loss = None

            def on_validation_epoch_end(self, trainer, pl_module):
                current_val_loss = trainer.callback_metrics.get('val_loss')
                if current_val_loss is not None and self.prev_val_loss is not None:
                    spike = current_val_loss - self.prev_val_loss
                    if spike > self.spike_threshold:
                        print(f"   ⚠️ VALIDATION SPIKE DETECTED: {spike:.4f} increase in val_loss")
                        print(f"      Previous: {self.prev_val_loss:.4f}, Current: {current_val_loss:.4f}")
                self.prev_val_loss = current_val_loss

        trainer = pl.Trainer(
            max_epochs=TRAINING_CONFIG['max_epochs'],
            accelerator='auto',
            devices=1,
            callbacks=[
                pl.callbacks.EarlyStopping(monitor='val_loss', patience=TRAINING_CONFIG['patience'], mode='min'),
                pl.callbacks.ModelCheckpoint(
                    monitor='val_loss', mode='min', save_top_k=1,
                    filename='dynamics_{epoch:02d}_{val_loss:.4f}',
                    dirpath='models/multiverse/dynamics'
                ),
                ValidationSpikeDetector(spike_threshold=0.3)  # Detect spikes > 0.3
            ],
            enable_progress_bar=True
        )

        trainer.fit(model, train_loader, test_loader)
        best_path = trainer.checkpoint_callback.best_model_path

        print(f"   ✅ TeamDynamicsModel saved: {best_path}")
        return model, best_path

    def train_contextual_model(self, train_loader, test_loader, input_dim):
        """Train ContextualPerformanceModel (Multiverse 6)"""
        print("\n🌍 Training ContextualPerformanceModel...")

        model = ContextualPerformanceModel(
            input_dim=input_dim,
            dropout=0.25,
            learning_rate=8e-4,
            use_role_embedding=True
        )

        trainer = pl.Trainer(
            max_epochs=TRAINING_CONFIG['max_epochs'],
            accelerator='auto',
            devices=1,
            callbacks=[
                pl.callbacks.EarlyStopping(monitor='val_loss', patience=TRAINING_CONFIG['patience'], mode='min'),
                pl.callbacks.ModelCheckpoint(
                    monitor='val_loss', mode='min', save_top_k=1,
                    filename='contextual_{epoch:02d}_{val_loss:.4f}',
                    dirpath='models/multiverse/contextual'
                )
            ],
            enable_progress_bar=True
        )

        trainer.fit(model, train_loader, test_loader)
        best_path = trainer.checkpoint_callback.best_model_path

        print(f"   ✅ ContextualPerformanceModel saved: {best_path}")
        return model, best_path

    def train_federated_model(self, train_loader, test_loader, input_dim):
        """Train Federated PlayerModel"""
        print("\n🌐 Training FederatedPlayerModel...")
        
        model = FederatedPlayerModel(
            input_dim=input_dim,
            dropout=0.2,
            learning_rate=1e-3,
            use_role_embedding=True
        )
        
        trainer = pl.Trainer(
            max_epochs=TRAINING_CONFIG['max_epochs'],
            accelerator='auto',
            devices=1,
            callbacks=[
                pl.callbacks.EarlyStopping(monitor='val_loss', patience=TRAINING_CONFIG['patience'], mode='min'),
                pl.callbacks.ModelCheckpoint(
                    monitor='val_loss', mode='min', save_top_k=1,
                    filename='federated_{epoch:02d}_{val_loss:.4f}',
                    dirpath='models/federated'
                )
            ],
            enable_progress_bar=True
        )
        
        Path('models/federated').mkdir(parents=True, exist_ok=True)
        trainer.fit(model, train_loader, test_loader)
        
        best_model = FederatedPlayerModel.load_from_checkpoint(trainer.checkpoint_callback.best_model_path)
        best_model.eval()
        
        print(f"   ✅ Federated model trained! Best val loss: {trainer.checkpoint_callback.best_model_score:.4f}")
        
        return best_model, trainer.checkpoint_callback.best_model_path

    def train_role_classifier_model(self, train_loader, test_loader, input_dim):
        """Train RoleClassifierModel with multi-task learning"""
        print("\n🤖 Training RoleClassifierModel (Multi-task)...")

        model = RoleClassifierModel(
            input_dim=input_dim,
            dropout=0.3,
            learning_rate=5e-4,
            role_weight=0.3  # 30% weight for role classification
        )

        trainer = pl.Trainer(
            max_epochs=TRAINING_CONFIG['max_epochs'],
            accelerator='auto',
            devices=1,
            callbacks=[
                pl.callbacks.EarlyStopping(monitor='val_loss', patience=TRAINING_CONFIG['patience'], mode='min'),
                pl.callbacks.ModelCheckpoint(
                    monitor='val_weighted_mae', mode='min', save_top_k=1,
                    filename='role_classifier_{epoch:02d}_{val_weighted_mae:.4f}',
                    dirpath='models/role_classifier'
                )
            ],
            enable_progress_bar=True
        )

        trainer.fit(model, train_loader, test_loader)
        best_path = trainer.checkpoint_callback.best_model_path

        print(f"   ✅ RoleClassifierModel saved: {best_path}")
        return model, best_path

    def evaluate_with_fantasy_scoring(self, model, test_loader, model_name):
        """Evaluate model using fantasy penalty scoring"""
        print(f"\n🎮 Evaluating {model_name} with Fantasy Penalty Scoring...")

        model.eval()
        all_predictions = []
        all_targets = []
        all_roles = []

        with torch.no_grad():
            for batch in test_loader:
                if len(batch) == 3:
                    x, targets, role_ids = batch
                    try:
                        if hasattr(model, 'forward') and 'role_pred' in str(model.forward):
                            # Multi-output model (like RoleClassifierModel)
                            predictions, _ = model(x)
                        else:
                            predictions = model(x, role_ids)
                    except:
                        predictions = model(x)
                        role_ids = torch.ones_like(targets, dtype=torch.long)  # Default roles
                else:
                    x, targets = batch
                    predictions = model(x)
                    # Create pseudo roles based on targets
                    role_ids = torch.zeros_like(targets, dtype=torch.long)
                    role_ids[targets > 15] = 0  # Elite
                    role_ids[(targets > 5) & (targets <= 15)] = 1  # Rotation
                    role_ids[targets <= 5] = 2  # Bench

                # Handle different output types
                if isinstance(predictions, dict):
                    predictions = predictions['points']
                elif isinstance(predictions, tuple):
                    predictions = predictions[0]

                all_predictions.extend(predictions.cpu().numpy())
                all_targets.extend(targets.cpu().numpy())
                all_roles.extend(role_ids.cpu().numpy())

        # Convert to tensors
        pred_tensor = torch.tensor(all_predictions)
        target_tensor = torch.tensor(all_targets)
        role_tensor = torch.tensor(all_roles)

        # Calculate fantasy penalty score
        fantasy_score = fantasy_penalty_score(pred_tensor, target_tensor, role_tensor)

        # Calculate advanced sample weights
        sample_weights = advanced_sample_weighting(target_tensor, role_tensor)
        weighted_mae = torch.mean(torch.abs(pred_tensor - target_tensor) * sample_weights).item()

        print(f"   🎮 Fantasy Penalty Score: {fantasy_score:.4f}")
        print(f"   ⚖️ Advanced Weighted MAE: {weighted_mae:.4f}")

        return {
            'fantasy_score': fantasy_score,
            'weighted_mae': weighted_mae,
            'sample_weights': sample_weights
        }

    def comprehensive_model_evaluation(self, model, test_loader, model_name):
        """Perform comprehensive evaluation with all advanced metrics"""
        print(f"\n🔬 COMPREHENSIVE EVALUATION: {model_name}")
        print("=" * 60)

        model.eval()
        all_predictions = []
        all_targets = []
        all_roles = []
        player_names = []

        # Collect all predictions
        with torch.no_grad():
            for batch_idx, batch in enumerate(test_loader):
                if len(batch) == 3:
                    x, targets, role_ids = batch
                    try:
                        if hasattr(model, 'forward') and 'role_pred' in str(model.forward):
                            predictions, _ = model(x)
                        else:
                            predictions = model(x, role_ids)
                    except:
                        predictions = model(x)
                        role_ids = torch.ones_like(targets, dtype=torch.long)
                else:
                    x, targets = batch
                    predictions = model(x)
                    role_ids = torch.zeros_like(targets, dtype=torch.long)
                    role_ids[targets > 15] = 0  # Elite
                    role_ids[(targets > 5) & (targets <= 15)] = 1  # Rotation
                    role_ids[targets <= 5] = 2  # Bench

                # Handle different output types
                if isinstance(predictions, dict):
                    predictions = predictions['points']
                elif isinstance(predictions, tuple):
                    predictions = predictions[0]

                all_predictions.extend(predictions.cpu().numpy())
                all_targets.extend(targets.cpu().numpy())
                all_roles.extend(role_ids.cpu().numpy())

                # Generate dummy player names for analysis
                batch_size = len(targets)
                player_names.extend([f"Player_{batch_idx}_{i}" for i in range(batch_size)])

        # Convert to tensors
        pred_tensor = torch.tensor(all_predictions)
        target_tensor = torch.tensor(all_targets)
        role_tensor = torch.tensor(all_roles)

        # 1. Residual Bias Analysis
        print("\n📈 RESIDUAL BIAS ANALYSIS:")
        bias_results = analyze_residual_bias(pred_tensor, target_tensor, role_tensor, player_names)
        log_bias_analysis(bias_results, prefix="   ")

        # 2. Fantasy Contest Simulation
        print("\n🎮 FANTASY CONTEST SIMULATION:")
        pred_dict = {name: pred for name, pred in zip(player_names, all_predictions)}
        actual_dict = {name: actual for name, actual in zip(player_names, all_targets)}

        sim_results = simulate_fantasy_contests(pred_dict, actual_dict, contest_type="dfs")
        log_simulation_results(sim_results, prefix="   ")

        # 3. Auto-Correction Feedback
        print("\n🔄 AUTO-CORRECTION FEEDBACK:")
        game_context = {
            'back_to_back': False,
            'opponent_def_rating': 100,
            'home_game': True
        }

        feedback = auto_correction_feedback(actual_dict, pred_dict, game_context)
        print(f"   📊 Total players analyzed: {feedback['total_players']}")
        print(f"   🚨 Error categories: {feedback['error_categories']}")
        if feedback['improvement_suggestions']:
            print(f"   💡 Suggestions: {feedback['improvement_suggestions']}")
        if feedback['retraining_triggers']:
            print(f"   🔄 Retraining triggers: {feedback['retraining_triggers']}")

        # 4. Advanced Sample Weighting Analysis
        print("\n⚖️ ADVANCED SAMPLE WEIGHTING:")
        sample_weights = advanced_sample_weighting(target_tensor, role_tensor)
        weighted_mae = torch.mean(torch.abs(pred_tensor - target_tensor) * sample_weights).item()
        print(f"   Advanced Weighted MAE: {weighted_mae:.4f}")
        print(f"   Weight range: [{sample_weights.min():.3f}, {sample_weights.max():.3f}]")
        print(f"   Weight mean: {sample_weights.mean():.3f}")

        return {
            'bias_analysis': bias_results,
            'fantasy_simulation': sim_results,
            'feedback': feedback,
            'weighted_mae': weighted_mae,
            'sample_weights': sample_weights
        }

    def train_meta_model(self, primary_model, train_loader, test_loader, input_dim):
        """Train MetaModel to predict primary model failures"""
        print("\n🧠 Training MetaModel (Failure Prediction)...")

        # Collect primary model predictions for meta-training
        primary_model.eval()
        meta_train_data = []

        with torch.no_grad():
            for batch in train_loader:
                if len(batch) == 3:
                    x, targets, role_ids = batch
                    try:
                        primary_preds = primary_model(x, role_ids)
                    except:
                        primary_preds = primary_model(x)
                else:
                    x, targets = batch
                    primary_preds = primary_model(x)

                # Handle different output types
                if isinstance(primary_preds, dict):
                    primary_preds = primary_preds['points']
                elif isinstance(primary_preds, tuple):
                    primary_preds = primary_preds[0]

                meta_train_data.append((x, targets, primary_preds))

        # Create meta-model
        meta_model = MetaModel(input_dim=input_dim)

        # Create meta-training data loader
        meta_dataset = [(x, targets, preds) for x, targets, preds in meta_train_data]
        meta_loader = torch.utils.data.DataLoader(meta_dataset, batch_size=64, shuffle=True)

        # Train meta-model
        trainer = pl.Trainer(
            max_epochs=20,  # Fewer epochs for meta-model
            accelerator='auto',
            devices=1,
            callbacks=[
                pl.callbacks.EarlyStopping(monitor='val_loss', patience=5, mode='min'),
                pl.callbacks.ModelCheckpoint(
                    monitor='val_accuracy', mode='max', save_top_k=1,
                    filename='meta_model_{epoch:02d}_{val_accuracy:.4f}',
                    dirpath='models/meta'
                )
            ],
            enable_progress_bar=True
        )

        # Use part of meta data for validation
        train_size = int(0.8 * len(meta_dataset))
        val_size = len(meta_dataset) - train_size
        meta_train, meta_val = torch.utils.data.random_split(meta_dataset, [train_size, val_size])

        meta_train_loader = torch.utils.data.DataLoader(meta_train, batch_size=64, shuffle=True)
        meta_val_loader = torch.utils.data.DataLoader(meta_val, batch_size=64, shuffle=False)

        trainer.fit(meta_model, meta_train_loader, meta_val_loader)

        print(f"   ✅ MetaModel training completed!")
        return meta_model, trainer.checkpoint_callback.best_model_path

    def test_ai_coached_predictions(self, model, test_loader, model_name):
        """Test AI-coached predictions with commentary"""
        print(f"\n🤖 TESTING AI-COACHED PREDICTIONS: {model_name}")
        print("=" * 60)

        model.eval()
        leaderboard = PlayerAccuracyLeaderboard()

        sample_predictions = []

        with torch.no_grad():
            for batch_idx, batch in enumerate(test_loader):
                if batch_idx >= 5:  # Test on first 5 batches
                    break

                if len(batch) == 3:
                    x, targets, role_ids = batch
                    try:
                        predictions = model(x, role_ids)
                    except:
                        predictions = model(x)
                else:
                    x, targets = batch
                    predictions = model(x)

                # Handle different output types
                if isinstance(predictions, dict):
                    predictions = predictions['points']
                elif isinstance(predictions, tuple):
                    predictions = predictions[0]

                # Generate AI commentary for sample predictions
                for i in range(min(3, len(predictions))):  # 3 samples per batch
                    pred_value = predictions[i].item()
                    actual_value = targets[i].item()

                    # Create sample player and game context
                    player_context = {
                        'name': f'Sample_Player_{batch_idx}_{i}',
                        'recent_avg': actual_value + np.random.normal(0, 1),
                        'role': 'Elite' if actual_value > 15 else 'Rotation' if actual_value > 5 else 'Bench',
                        'avg_minutes': 25 + np.random.normal(0, 5)
                    }

                    game_context = {
                        'opponent': 'Sample_Opponent',
                        'is_home': np.random.choice([True, False]),
                        'back_to_back': np.random.choice([True, False], p=[0.2, 0.8]),
                        'opponent_def_rating': np.random.normal(100, 10)
                    }

                    # Generate AI commentary
                    confidence = np.random.uniform(0.4, 0.9)  # Simulated confidence
                    commentary = generate_ai_commentary(
                        pred_value, player_context, game_context, confidence
                    )

                    sample_predictions.append({
                        'player': player_context['name'],
                        'prediction': pred_value,
                        'actual': actual_value,
                        'commentary': commentary,
                        'confidence': confidence
                    })

                    # Add to leaderboard
                    leaderboard.add_prediction(
                        player_context['name'],
                        pred_value,
                        actual_value,
                        player_tier=player_context['role']
                    )

        # Display sample AI-coached predictions
        print("\n🎯 SAMPLE AI-COACHED PREDICTIONS:")
        for i, pred in enumerate(sample_predictions[:3]):
            print(f"\n--- Prediction {i+1} ---")
            print(pred['commentary'])
            print(f"**Actual Result**: {pred['actual']:.1f} points")
            print(f"**Prediction Accuracy**: {abs(pred['prediction'] - pred['actual']):.1f} point error")

        # Display leaderboards
        leaderboard.print_leaderboards()

        return {
            'sample_predictions': sample_predictions,
            'leaderboard': leaderboard
        }

    def enhanced_model_evaluation_with_coaching(self, model, test_loader, model_name):
        """Enhanced evaluation with all advanced features"""
        print(f"\n🚀 ENHANCED EVALUATION WITH AI COACHING: {model_name}")
        print("=" * 70)

        # 1. Comprehensive evaluation
        comprehensive_results = self.comprehensive_model_evaluation(model, test_loader, model_name)

        # 2. AI-coached predictions
        coaching_results = self.test_ai_coached_predictions(model, test_loader, model_name)

        # 3. Fantasy evaluation
        fantasy_results = self.evaluate_with_fantasy_scoring(model, test_loader, model_name)

        # 4. Summary report
        print(f"\n📋 ENHANCED EVALUATION SUMMARY:")
        print(f"   🎯 Weighted MAE: {comprehensive_results['weighted_mae']:.4f}")
        print(f"   🎮 Fantasy Score: {fantasy_results['fantasy_score']:.4f}")
        print(f"   🤖 AI Predictions: {len(coaching_results['sample_predictions'])} generated")
        print(f"   🏆 Leaderboard: {len(coaching_results['leaderboard'].player_stats)} players tracked")

        return {
            'comprehensive': comprehensive_results,
            'coaching': coaching_results,
            'fantasy': fantasy_results
        }

    def test_advanced_production_features(self, model, test_loader, model_name):
        """Test all advanced production features"""
        print(f"\n🚀 TESTING ADVANCED PRODUCTION FEATURES: {model_name}")
        print("=" * 70)

        model.eval()

        # Collect sample data for testing
        sample_features = None
        sample_targets = None
        sample_predictions = None

        with torch.no_grad():
            for batch in test_loader:
                if len(batch) == 3:
                    x, targets, role_ids = batch
                    try:
                        predictions = model(x, role_ids)
                    except:
                        predictions = model(x)
                else:
                    x, targets = batch
                    predictions = model(x)

                # Handle different output types
                if isinstance(predictions, dict):
                    predictions = predictions['points']
                elif isinstance(predictions, tuple):
                    predictions = predictions[0]

                sample_features = x[:10]  # First 10 samples
                sample_targets = targets[:10]
                sample_predictions = predictions[:10]
                break

        production_results = {}

        # 1. Adversarial Stress Testing
        print("\n🎯 ADVERSARIAL STRESS TESTING:")
        stress_results = adversarial_stress_test(model, sample_features)
        log_stress_test_results(stress_results, prefix="   ")
        production_results['stress_test'] = stress_results

        # 2. Line Movement Watchdog
        print("\n💰 LINE MOVEMENT WATCHDOG:")
        watchdog = LineMovementWatchdog(disagreement_threshold=2.0)

        # Create sample market data
        sample_market_lines = {}
        for i in range(len(sample_predictions)):
            player_name = f"Sample_Player_{i}"
            model_pred = sample_predictions[i].item()
            # Simulate market lines with some disagreement
            sportsbook_line = model_pred + np.random.normal(0, 1.5)
            dfs_line = model_pred + np.random.normal(0, 1.0)

            sample_market_lines[player_name] = {
                'sportsbook': sportsbook_line,
                'dfs': dfs_line,
                'confidence': np.random.uniform(0.3, 0.8)
            }

        # Compare with markets
        pred_dict = {f"Sample_Player_{i}": pred.item() for i, pred in enumerate(sample_predictions)}
        market_comparison = watchdog.compare_with_markets(pred_dict, sample_market_lines)
        watchdog.log_market_comparison(market_comparison, prefix="   ")
        production_results['market_analysis'] = market_comparison

        # 3. Counterfactual Simulations
        print("\n🎮 COUNTERFACTUAL SIMULATIONS:")
        modifications = {
            'minutes_played': 5.0,  # +5 minutes
            'usage_rate': 0.05,     # +5% usage
            'opponent_strength': -2.0,  # Weaker opponent
            'rest_days': 1.0        # +1 rest day
        }

        counterfactual_results = simulate_counterfactual_scenarios(
            model, sample_features[:1], modifications
        )

        print("   📊 What-If Analysis Results:")
        for scenario in counterfactual_results['what_if_summary']:
            print(f"     {scenario}")

        production_results['counterfactuals'] = counterfactual_results

        # 4. Medusa Autopilot
        print("\n🤖 MEDUSA AUTOPILOT SYSTEM:")
        autopilot = MedusaAutopilot(performance_threshold=3.0)

        # Simulate monitoring
        player_names = [f"Sample_Player_{i}" for i in range(len(sample_predictions))]
        autopilot_analysis = autopilot.monitor_prediction_quality(
            sample_predictions, sample_targets, player_names
        )

        print("   📊 Autopilot Analysis:")
        print(f"     System status: {autopilot_analysis['autopilot_status']['system_health']}")
        print(f"     Performance degraded: {autopilot_analysis['trend_analysis']['performance_degraded']}")
        print(f"     Active proposals: {len(autopilot_analysis['improvement_proposals'])}")

        if autopilot_analysis['improvement_proposals']:
            print("   💡 Improvement Proposals:")
            for i, proposal in enumerate(autopilot_analysis['improvement_proposals'][:3]):
                print(f"     {i+1}. {proposal['type']}: {proposal['description']}")

        production_results['autopilot'] = autopilot_analysis

        # 5. Summary Report
        print(f"\n📋 ADVANCED PRODUCTION FEATURES SUMMARY:")
        print(f"   🎯 Robustness Score: {stress_results['robustness_score']:.3f}")
        print(f"   💰 Market Disagreements: {market_comparison['alert_summary']['total_disagreements']}")
        print(f"   🎮 Counterfactual Effects: {len(counterfactual_results['scenarios'])} scenarios tested")
        print(f"   🤖 Autopilot Status: {autopilot_analysis['autopilot_status']['system_health']}")

        return production_results

    def ultimate_model_evaluation(self, model, test_loader, model_name):
        """Ultimate comprehensive evaluation with ALL features"""
        print(f"\n🌟 ULTIMATE MODEL EVALUATION: {model_name}")
        print("=" * 80)

        # 1. Enhanced evaluation with coaching
        enhanced_results = self.enhanced_model_evaluation_with_coaching(model, test_loader, model_name)

        # 2. Advanced production features
        production_results = self.test_advanced_production_features(model, test_loader, model_name)

        # 3. Meta-model training
        try:
            input_dim = next(iter(test_loader))[0].shape[1]
            meta_model, meta_path = self.train_meta_model(model, test_loader, test_loader, input_dim)
            meta_results = {'meta_model_trained': True, 'meta_model_path': meta_path}
        except Exception as e:
            print(f"   ⚠️ Meta-model training failed: {e}")
            meta_results = {'meta_model_trained': False, 'error': str(e)}

        # 4. Ultimate summary
        print(f"\n🏆 ULTIMATE EVALUATION SUMMARY:")
        print(f"   🎯 Weighted MAE: {enhanced_results['comprehensive']['weighted_mae']:.4f}")
        print(f"   🎮 Fantasy Score: {enhanced_results['fantasy']['fantasy_score']:.4f}")
        print(f"   🤖 AI Predictions: {len(enhanced_results['coaching']['sample_predictions'])} generated")
        print(f"   🏆 Leaderboard: {len(enhanced_results['coaching']['leaderboard'].player_stats)} players")
        print(f"   🎯 Robustness: {production_results['stress_test']['robustness_score']:.3f}")
        print(f"   💰 Market Edges: {production_results['market_analysis']['alert_summary']['high_confidence_edges']}")
        print(f"   🎮 Scenarios: {len(production_results['counterfactuals']['scenarios'])} tested")
        print(f"   🤖 Autopilot: {production_results['autopilot']['autopilot_status']['system_health']}")
        print(f"   🧠 Meta-Model: {'✅' if meta_results['meta_model_trained'] else '❌'}")

        return {
            'enhanced_evaluation': enhanced_results,
            'production_features': production_results,
            'meta_model': meta_results,
            'overall_score': self._calculate_overall_score(enhanced_results, production_results)
        }

    def _calculate_overall_score(self, enhanced_results, production_results):
        """Calculate overall model quality score"""
        # Weighted combination of different aspects
        mae_score = max(0, 1 - enhanced_results['comprehensive']['weighted_mae'] / 5.0)  # 0-1 scale
        fantasy_score = min(1, enhanced_results['fantasy']['fantasy_score'] / 100.0)  # 0-1 scale
        robustness_score = production_results['stress_test']['robustness_score']  # Already 0-1

        overall_score = (mae_score * 0.4 + fantasy_score * 0.3 + robustness_score * 0.3)
        return overall_score

    def test_model_thoroughly(self, model, test_loader, model_name):
        """Test model thoroughly on real data"""
        print(f"\n🧪 Testing {model_name} on real WNBA data...")
        
        model.eval()
        all_predictions = []
        all_targets = []
        
        with torch.no_grad():
            for batch in test_loader:
                if len(batch) == 3:
                    x, y, role_ids = batch
                    try:
                        predictions = model(x, role_ids)
                    except:
                        predictions = model(x)
                else:
                    x, y = batch
                    predictions = model(x)
                
                # Handle different output types
                if isinstance(predictions, dict):
                    predictions = predictions['points']
                elif isinstance(predictions, tuple):
                    predictions = predictions[0]
                
                all_predictions.extend(predictions.cpu().numpy())
                all_targets.extend(y.cpu().numpy())
        
        all_predictions = np.array(all_predictions)
        all_targets = np.array(all_targets)
        
        # Calculate metrics
        mae = mean_absolute_error(all_targets, all_predictions)
        rmse = np.sqrt(np.mean((all_predictions - all_targets) ** 2))
        mape = np.mean(np.abs((all_targets - all_predictions) / np.maximum(all_targets, 1))) * 100
        correlation = np.corrcoef(all_predictions, all_targets)[0, 1]
        
        print(f"   📊 {model_name} Results:")
        print(f"      🎯 MAE: {mae:.3f} points")
        print(f"      📈 RMSE: {rmse:.3f} points")
        print(f"      📊 MAPE: {mape:.1f}%")
        print(f"      🔗 Correlation: {correlation:.3f}")
        
        # Assessment
        if mae < 2.0:
            status = "🏆 EXCELLENT - Professional grade!"
        elif mae < 3.0:
            status = "✅ GOOD - Production ready"
        elif mae < 4.0:
            status = "⚠️ ACCEPTABLE - Needs monitoring"
        else:
            status = "❌ POOR - Requires improvement"
        
        print(f"      📊 Status: {status}")
        
        return {
            'mae': mae,
            'rmse': rmse,
            'mape': mape,
            'correlation': correlation,
            'status': status
        }
    
    def run_complete_advanced_training(self):
        """Run complete training of ALL advanced models"""
        print("🚀 Starting complete advanced WNBA model training...")
        
        # Step 1: Load and prepare data
        data = self.load_and_prepare_master_dataset()
        if data is None:
            return False
        
        # Step 2: Create data loaders
        train_loader, test_loader = self.create_data_loaders(data)
        
        # Step 3: Train ALL models
        input_dim = data['input_dim']
        trained_models = {}
        
        # Train Enhanced Model
        enhanced_model, enhanced_path = self.train_enhanced_model(train_loader, test_loader, input_dim)
        enhanced_results = self.test_model_thoroughly(enhanced_model, test_loader, "Enhanced")
        trained_models['enhanced'] = {
            'model': enhanced_model,
            'path': enhanced_path,
            'results': enhanced_results
        }
        
        # Train Hybrid Model (Tabular + GNN)
        try:
            hybrid_model, hybrid_path = self.train_hybrid_model(train_loader, test_loader, input_dim)
            hybrid_results = self.test_model_thoroughly(hybrid_model, test_loader, "Hybrid")
            trained_models['hybrid'] = {
                'model': hybrid_model,
                'path': hybrid_path,
                'results': hybrid_results
            }
        except Exception as e:
            print(f"   ⚠️ Hybrid training failed: {e}")

        # Train MultiTask Model
        try:
            multitask_model, multitask_path = self.train_multitask_model(train_loader, test_loader, input_dim)
            multitask_results = self.test_model_thoroughly(multitask_model, test_loader, "MultiTask")
            trained_models['multitask'] = {
                'model': multitask_model,
                'path': multitask_path,
                'results': multitask_results
            }
        except Exception as e:
            print(f"   ⚠️ MultiTask training failed: {e}")

        # Train Bayesian Model (with FIXES)
        try:
            bayesian_model, bayesian_path = self.train_bayesian_model(train_loader, test_loader, input_dim)
            bayesian_results = self.test_model_thoroughly(bayesian_model, test_loader, "Bayesian")
            trained_models['bayesian'] = {
                'model': bayesian_model,
                'path': bayesian_path,
                'results': bayesian_results
            }
        except Exception as e:
            print(f"   ⚠️ Bayesian training failed: {e}")
        
        # Train Federated Model
        try:
            federated_model, federated_path = self.train_federated_model(train_loader, test_loader, input_dim)
            federated_results = self.test_model_thoroughly(federated_model, test_loader, "Federated")
            trained_models['federated'] = {
                'model': federated_model,
                'path': federated_path,
                'results': federated_results
            }
        except Exception as e:
            print(f"   ⚠️ Federated training failed: {e}")

        # MULTIVERSE ENSEMBLE TRAINING
        print(f"\n🌌 TRAINING MULTIVERSE ENSEMBLE MODELS:")
        print("=" * 60)

        # Train PossessionBasedModel
        try:
            possession_model, possession_path = self.train_possession_based_model(train_loader, test_loader, input_dim)
            possession_results = self.test_model_thoroughly(possession_model, test_loader, "PossessionBased")
            trained_models['possession_based'] = {
                'model': possession_model,
                'path': possession_path,
                'results': possession_results
            }
        except Exception as e:
            print(f"   ⚠️ PossessionBased training failed: {e}")

        # Train LineupChemistryModel
        try:
            lineup_model, lineup_path = self.train_lineup_chemistry_model(train_loader, test_loader, input_dim)
            lineup_results = self.test_model_thoroughly(lineup_model, test_loader, "LineupChemistry")
            trained_models['lineup_chemistry'] = {
                'model': lineup_model,
                'path': lineup_path,
                'results': lineup_results
            }
        except Exception as e:
            print(f"   ⚠️ LineupChemistry training failed: {e}")

        # Train CumulativeFatigueModel
        try:
            fatigue_model, fatigue_path = self.train_fatigue_model(train_loader, test_loader, input_dim)
            fatigue_results = self.test_model_thoroughly(fatigue_model, test_loader, "CumulativeFatigue")
            trained_models['cumulative_fatigue'] = {
                'model': fatigue_model,
                'path': fatigue_path,
                'results': fatigue_results
            }
        except Exception as e:
            print(f"   ⚠️ CumulativeFatigue training failed: {e}")

        # Train HighLeverageModel
        try:
            leverage_model, leverage_path = self.train_high_leverage_model(train_loader, test_loader, input_dim)
            leverage_results = self.test_model_thoroughly(leverage_model, test_loader, "HighLeverage")
            trained_models['high_leverage'] = {
                'model': leverage_model,
                'path': leverage_path,
                'results': leverage_results
            }
        except Exception as e:
            print(f"   ⚠️ HighLeverage training failed: {e}")

        # Train TeamDynamicsModel
        try:
            dynamics_model, dynamics_path = self.train_team_dynamics_model(train_loader, test_loader, input_dim)
            dynamics_results = self.test_model_thoroughly(dynamics_model, test_loader, "TeamDynamics")
            trained_models['team_dynamics'] = {
                'model': dynamics_model,
                'path': dynamics_path,
                'results': dynamics_results
            }
        except Exception as e:
            print(f"   ⚠️ TeamDynamics training failed: {e}")

        # Train ContextualPerformanceModel
        try:
            contextual_model, contextual_path = self.train_contextual_model(train_loader, test_loader, input_dim)
            contextual_results = self.test_model_thoroughly(contextual_model, test_loader, "ContextualPerformance")
            trained_models['contextual_performance'] = {
                'model': contextual_model,
                'path': contextual_path,
                'results': contextual_results
            }
        except Exception as e:
            print(f"   ⚠️ ContextualPerformance training failed: {e}")

        # Create Multiverse Ensemble
        print(f"\n🌌 CREATING MULTIVERSE ENSEMBLE:")
        multiverse_models = {}
        multiverse_weights = {
            'possession_based': 0.20,
            'lineup_chemistry': 0.18,
            'cumulative_fatigue': 0.16,
            'high_leverage': 0.15,
            'team_dynamics': 0.16,
            'contextual_performance': 0.15
        }

        for model_name in multiverse_weights.keys():
            if model_name in trained_models:
                multiverse_models[model_name] = trained_models[model_name]['model']

        if len(multiverse_models) > 0:
            multiverse_ensemble = MultiverseEnsemble(multiverse_models, multiverse_weights)
            trained_models['multiverse_ensemble'] = {
                'ensemble': multiverse_ensemble,
                'models': multiverse_models,
                'weights': multiverse_weights,
                'num_models': len(multiverse_models)
            }
            print(f"   ✅ Multiverse Ensemble created with {len(multiverse_models)} models")
        else:
            print(f"   ⚠️ No multiverse models available for ensemble")

        # Step 4: Save comprehensive results
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        comprehensive_summary = {
            'timestamp': datetime.now().isoformat(),
            'dataset_info': {
                'total_records': len(data['X_train']) + len(data['X_test']),
                'training_samples': len(data['X_train']),
                'test_samples': len(data['X_test']),
                'features_selected': len(data['features']),
                'input_dim': input_dim
            },
            'models': {
                name: {
                    'path': info['path'],
                    'mae': info['results']['mae'],
                    'rmse': info['results']['rmse'],
                    'correlation': info['results']['correlation'],
                    'status': info['results']['status']
                } for name, info in trained_models.items()
            },
            'features': data['features']
        }
        
        summary_path = f"advanced_models_training_summary_{timestamp}.json"
        with open(summary_path, 'w') as f:
            json.dump(comprehensive_summary, f, indent=2, default=float)
        
        print(f"\n💾 Comprehensive training summary saved to: {summary_path}")
        
        # Final assessment
        best_model = min(trained_models.keys(), key=lambda k: trained_models[k]['results']['mae'])
        best_mae = trained_models[best_model]['results']['mae']
        
        print(f"\n🏆 ALL ADVANCED MODELS TRAINING COMPLETE!")
        print("=" * 60)
        print(f"📊 Dataset: {comprehensive_summary['dataset_info']['total_records']} real WNBA records")
        print(f"🥇 Best Model: {best_model.upper()} (MAE: {best_mae:.3f})")
        
        for name, info in sorted(trained_models.items(), key=lambda x: x[1]['results']['mae']):
            mae = info['results']['mae']
            status = info['results']['status']
            print(f"   {name.upper()}: {mae:.3f} MAE - {status}")
        
        if best_mae < 2.0:
            print(f"\n🎉 SUCCESS: Professional-grade advanced WNBA models achieved!")
            print(f"   ✅ Ready for production deployment")
            print(f"   ✅ Trained on ALL real WNBA data")
        elif best_mae < 4.0:
            print(f"\n✅ GOOD: Solid advanced WNBA models trained!")
            print(f"   ✅ Acceptable for production use")
        else:
            print(f"\n⚠️ NEEDS IMPROVEMENT: Models require optimization")
        
        return best_mae < 4.0


def main():
    """Main function"""
    trainer = AdvancedWNBATrainer()
    success = trainer.run_complete_advanced_training()
    
    if success:
        print("\n✅ ALL ADVANCED WNBA MODELS TRAINING COMPLETED!")
        print("🏀 Professional WNBA prediction system with ALL models ready!")
    else:
        print("\n❌ Advanced models training failed")


if __name__ == "__main__":
    main()
