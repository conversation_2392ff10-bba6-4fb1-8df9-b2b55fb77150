#!/usr/bin/env python3
"""
🌸 MODERN FLOWER SERVER FOR WNBA FEDERATED LEARNING
==================================================

Production-ready Flower server using the new SuperLink architecture.
Aggregates model updates from 13 WNBA teams with advanced privacy and security.
"""

import flwr as fl
from flwr.server.strategy import FedAvg
from flwr.common import Parameters, FitRes, EvaluateRes, Scalar
from flwr.server.client_proxy import ClientProxy
from typing import Dict, List, Tuple, Optional, Any, Union
import numpy as np
import json
import logging
from pathlib import Path
import time

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class WNBAFederatedStrategy(FedAvg):
    """
    Privacy-Preserving WNBA Federated Learning Strategy.

    FEDERATED LEARNING PRINCIPLES:
    - Server NEVER accesses raw team data
    - Only aggregates model weights/gradients from clients
    - Each team's data stays on their premises
    - Privacy-preserving aggregation with outlier detection
    - Support for all 13 WNBA teams including GSV

    Features:
    - Team-specific weighting based on sample counts (not data content)
    - Convergence monitoring via aggregated metrics only
    - Comprehensive federated metrics tracking
    - Robust handling of team dropouts
    """
    
    def __init__(self, min_teams: int = 3, max_rounds: int = 20):
        super().__init__(
            min_fit_clients=min_teams,
            min_evaluate_clients=min_teams,
            min_available_clients=min_teams,
            evaluate_fn=None,  # No server-side evaluation
            on_fit_config_fn=self.get_fit_config,
            on_evaluate_config_fn=self.get_eval_config,
        )
        
        self.min_teams = min_teams
        self.max_rounds = max_rounds
        self.round_metrics = []
        self.team_performance = {}
        self.convergence_threshold = 0.01
        self.best_global_loss = float('inf')
        
        logger.info(f"🏀 WNBA Federated Strategy initialized")
        logger.info(f"   Min teams: {min_teams}")
        logger.info(f"   Max rounds: {max_rounds}")
        logger.info(f"   Supporting all 13 WNBA teams including GSV!")
    
    def get_fit_config(self, server_round: int) -> Dict[str, Scalar]:
        """Configure training parameters for each round"""
        
        # Adaptive learning rate decay
        base_lr = 5e-4
        lr = base_lr * (0.95 ** (server_round - 1))
        
        # Progressive training - more epochs as rounds progress
        local_epochs = min(3 + (server_round // 3), 8)
        
        config = {
            "server_round": server_round,
            "local_epochs": local_epochs,
            "learning_rate": lr,
            "batch_size": 256,
            "dropout": 0.3,
            "weight_decay": 1e-4,
        }
        
        logger.info(f"📋 Round {server_round} config: lr={lr:.6f}, epochs={local_epochs}")
        return config
    
    def get_eval_config(self, server_round: int) -> Dict[str, Scalar]:
        """Configure evaluation parameters"""
        return {"server_round": server_round, "eval_steps": 100}
    
    def aggregate_fit(
        self, 
        server_round: int, 
        results: List[Tuple[ClientProxy, FitRes]], 
        failures: List[Union[Tuple[ClientProxy, FitRes], BaseException]]
    ) -> Tuple[Optional[Parameters], Dict[str, Scalar]]:
        """
        Advanced aggregation with privacy preservation and outlier detection.
        """
        
        if not results:
            logger.warning(f"❌ Round {server_round}: No results to aggregate")
            return None, {}
        
        logger.info(f"🔄 Round {server_round}: Aggregating {len(results)} team updates")
        
        # Extract team information and metrics
        team_metrics = {}
        total_examples = 0
        
        for client_proxy, fit_res in results:
            team_id = client_proxy.cid
            num_examples = fit_res.num_examples
            metrics = fit_res.metrics
            
            team_metrics[team_id] = {
                'num_examples': num_examples,
                'train_loss': metrics.get('train_loss', 0.0),
                'val_loss': metrics.get('val_loss', 0.0),
                'round': server_round
            }
            total_examples += num_examples
            
            logger.info(f"   🏀 {team_id}: {num_examples} examples, "
                       f"train_loss={metrics.get('train_loss', 0.0):.3f}")
        
        # Detect and filter outliers
        filtered_results = self._filter_outlier_teams(results, team_metrics)
        
        if len(filtered_results) < self.min_teams:
            logger.warning(f"⚠️ Too few valid teams after filtering: {len(filtered_results)}")
            filtered_results = results  # Use all results as fallback
        
        # Use parent class aggregation with filtered results
        aggregated_result = super().aggregate_fit(server_round, filtered_results, failures)
        
        if aggregated_result is None or aggregated_result[0] is None:
            logger.error(f"❌ Round {server_round}: Aggregation failed")
            return None, {}
        
        aggregated_parameters, _ = aggregated_result
        
        # Calculate global metrics
        avg_train_loss = np.mean([m['train_loss'] for m in team_metrics.values()])
        avg_val_loss = np.mean([m['val_loss'] for m in team_metrics.values()])
        
        # Check convergence
        convergence_info = self._check_convergence(avg_val_loss, server_round)
        
        # Store round metrics
        round_data = {
            'round': server_round,
            'participating_teams': list(team_metrics.keys()),
            'total_examples': total_examples,
            'avg_train_loss': avg_train_loss,
            'avg_val_loss': avg_val_loss,
            'team_metrics': team_metrics,
            'failures': len(failures),
            'convergence': convergence_info,
            'timestamp': time.time()
        }
        self.round_metrics.append(round_data)
        
        # Global metrics to return
        global_metrics = {
            'avg_train_loss': avg_train_loss,
            'avg_val_loss': avg_val_loss,
            'participating_teams': len(team_metrics),
            'total_examples': total_examples,
            'converged': convergence_info['converged']
        }
        
        logger.info(f"   ✅ Round {server_round} complete: avg_val_loss={avg_val_loss:.3f}, "
                   f"teams={len(team_metrics)}")
        
        # Save progress
        self._save_progress()
        
        return aggregated_parameters, global_metrics
    
    def _filter_outlier_teams(self, results: List[Tuple[ClientProxy, FitRes]], 
                             team_metrics: Dict[str, Dict]) -> List[Tuple[ClientProxy, FitRes]]:
        """Filter out teams with suspicious metrics (potential attacks or errors)"""
        
        if len(results) <= 2:
            return results  # Don't filter if too few teams
        
        # Extract validation losses for outlier detection
        val_losses = [metrics['val_loss'] for metrics in team_metrics.values()]
        
        # Use IQR method for outlier detection
        q1 = np.percentile(val_losses, 25)
        q3 = np.percentile(val_losses, 75)
        iqr = q3 - q1
        lower_bound = q1 - 1.5 * iqr
        upper_bound = q3 + 1.5 * iqr
        
        # Filter results
        filtered_results = []
        for (client_proxy, fit_res), (team_id, metrics) in zip(results, team_metrics.items()):
            val_loss = metrics['val_loss']
            
            if lower_bound <= val_loss <= upper_bound:
                filtered_results.append((client_proxy, fit_res))
            else:
                logger.warning(f"   ⚠️ Filtering outlier {team_id}: val_loss={val_loss:.3f}")
        
        return filtered_results
    
    def _check_convergence(self, current_loss: float, round_num: int) -> Dict[str, Any]:
        """Check if federated training has converged"""
        
        converged = False
        reason = "continuing"
        
        # Update best loss
        if current_loss < self.best_global_loss:
            improvement = self.best_global_loss - current_loss
            self.best_global_loss = current_loss
            
            # Check if improvement is below threshold
            if improvement < self.convergence_threshold:
                converged = True
                reason = "loss_plateau"
        
        # Check maximum rounds
        if round_num >= self.max_rounds:
            converged = True
            reason = "max_rounds"
        
        # Check for overfitting (loss increasing)
        if len(self.round_metrics) >= 3:
            recent_losses = [r['avg_val_loss'] for r in self.round_metrics[-3:]]
            if all(recent_losses[i] <= recent_losses[i+1] for i in range(len(recent_losses)-1)):
                converged = True
                reason = "overfitting"
        
        convergence_info = {
            'converged': converged,
            'reason': reason,
            'best_loss': self.best_global_loss,
            'current_loss': current_loss,
            'round': round_num
        }
        
        if converged:
            logger.info(f"🏁 Convergence detected: {reason}")
        
        return convergence_info
    
    def _save_progress(self):
        """Save federated learning progress"""
        progress_file = Path("federated_progress.json")
        
        progress_data = {
            'strategy': 'WNBAFederatedStrategy',
            'total_rounds': len(self.round_metrics),
            'best_global_loss': self.best_global_loss,
            'round_metrics': self.round_metrics,
            'team_performance': self.team_performance,
            'timestamp': time.time()
        }
        
        with open(progress_file, 'w') as f:
            json.dump(progress_data, f, indent=2)

def create_strategy() -> WNBAFederatedStrategy:
    """Create the federated learning strategy"""
    return WNBAFederatedStrategy(min_teams=2, max_rounds=15)

# For new Flower SuperLink compatibility
def get_strategy():
    """Get strategy for Flower SuperLink"""
    return create_strategy()

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="WNBA Flower Server")
    parser.add_argument("--min-teams", type=int, default=2, help="Minimum teams")
    parser.add_argument("--max-rounds", type=int, default=15, help="Maximum rounds")
    parser.add_argument("--port", type=int, default=8080, help="Server port")
    
    args = parser.parse_args()
    
    print("🌸 MODERN WNBA FLOWER SERVER")
    print("=" * 35)
    print(f"   🏀 Min teams: {args.min_teams}")
    print(f"   🔄 Max rounds: {args.max_rounds}")
    print(f"   🌐 Port: {args.port}")
    print(f"   🆕 Including Golden State Valkyries (GSV)")
    print()
    print("💡 Use new Flower CLI:")
    print(f"   flower-superlink --insecure --port {args.port}")
    print()
    print("🏀 Waiting for WNBA teams to connect...")
    
    # Create strategy
    strategy = WNBAFederatedStrategy(min_teams=args.min_teams, max_rounds=args.max_rounds)
    
    # For backward compatibility, still support old API
    try:
        fl.server.start_server(
            server_address=f"0.0.0.0:{args.port}",
            config=fl.server.ServerConfig(num_rounds=args.max_rounds),
            strategy=strategy
        )
    except Exception as e:
        print(f"❌ Server failed: {e}")
        print("💡 Try using: flower-superlink --insecure")
