#!/usr/bin/env python3
"""
Unified WNBA Automated Data Collection System

This consolidated system combines all three collectors into one unified system:
- automated_nba_api_collector.py
- enhanced_automated_collector.py  
- updated_2025_season_collector.py

Features:
- Runs at 3:00 AM when all games are finished
- Proper NBA API endpoint usage with 2025 season support
- Smart duplicate prevention (game-level)
- Golden State Valkyries integration
- Seamless integration with definitive master dataset
- Model retraining integration
- Comprehensive logging and monitoring

Author: WNBA Analytics Team
Date: 2025-07-11
"""

import pandas as pd
import numpy as np
import requests
import json
import sqlite3
import schedule
import time
import logging
from datetime import datetime, timedelta, date
from pathlib import Path
import hashlib
import warnings
warnings.filterwarnings('ignore')

class UnifiedWNBACollector:
    """Unified WNBA data collector - consolidates all collection systems"""
    
    def __init__(self):
        self.setup_logging()
        self.setup_database()
        self.setup_configuration()

        # Master dataset path (definitive source of truth)
        self.master_dataset_path = "wnba_definitive_master_dataset_FIXED.csv"

        # Collection tracking
        self.collection_history = []
        self.last_successful_collection = None

        # API session management (consolidated from nba_api_integration.py)
        self.session = None
        self.setup_api_session()

        # API credit tracking (consolidated from nba_api_integration.py)
        self.api_tracking_file = "api_credit_tracking.json"
        self.load_api_tracking()

        # Gap analysis tracking (consolidated from nba_api_data_update.py)
        self.coverage_gaps = []
        self.last_gap_analysis = None
        
    def setup_logging(self):
        """Setup comprehensive logging"""
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('unified_wnba_collector.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        
    def setup_database(self):
        """Setup SQLite tracking database"""
        
        self.db_path = "unified_collection_tracking.db"
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # Collection tracking table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS collections (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    collection_date DATE,
                    collection_time TIMESTAMP,
                    endpoint TEXT,
                    records_collected INTEGER,
                    games_processed INTEGER,
                    new_games INTEGER,
                    integration_successful BOOLEAN,
                    notes TEXT
                )
            ''')
            
            # Game tracking table (prevent duplicates)
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS processed_games (
                    game_id TEXT PRIMARY KEY,
                    game_date DATE,
                    home_team TEXT,
                    away_team TEXT,
                    processed_date TIMESTAMP,
                    data_source TEXT
                )
            ''')
            
            conn.commit()
            
    def setup_configuration(self):
        """Setup unified configuration"""
        
        # 2025 WNBA teams including Golden State Valkyries
        self.wnba_teams = {
            'ATL': {'name': 'Atlanta Dream', 'id': 1611661313},
            'CHI': {'name': 'Chicago Sky', 'id': 1611661314}, 
            'CON': {'name': 'Connecticut Sun', 'id': 1611661315},
            'DAL': {'name': 'Dallas Wings', 'id': 1611661316},
            'GSV': {'name': 'Golden State Valkyries', 'id': 1611661324},  # New 2025 team
            'IND': {'name': 'Indiana Fever', 'id': 1611661317},
            'LAS': {'name': 'Los Angeles Sparks', 'id': 1611661318},
            'LV': {'name': 'Las Vegas Aces', 'id': 1611661319},
            'MIN': {'name': 'Minnesota Lynx', 'id': 1611661320},
            'NYL': {'name': 'New York Liberty', 'id': 1611661321},
            'PHO': {'name': 'Phoenix Mercury', 'id': 1611661322},
            'SEA': {'name': 'Seattle Storm', 'id': 1611661323},
            'WAS': {'name': 'Washington Mystics', 'id': 1611661325}
        }
        
        # NBA API endpoints for WNBA data
        self.endpoints = {
            'player_stats': {
                'url': 'https://stats.nba.com/stats/leaguedashplayerstats',
                'params': {
                    'College': '',
                    'Conference': '',
                    'Country': '',
                    'DateFrom': '',
                    'DateTo': '',
                    'Division': '',
                    'DraftPick': '',
                    'DraftYear': '',
                    'GameScope': '',
                    'GameSegment': '',
                    'Height': '',
                    'LastNGames': 0,
                    'LeagueID': '10',  # WNBA League ID
                    'Location': '',
                    'MeasureType': 'Base',
                    'Month': 0,
                    'OpponentTeamID': 0,
                    'Outcome': '',
                    'PORound': 0,
                    'PaceAdjust': 'N',
                    'PerMode': 'PerGame',
                    'Period': 0,
                    'PlayerExperience': '',
                    'PlayerPosition': '',
                    'PlusMinus': 'N',
                    'Rank': 'N',
                    'Season': '2025',
                    'SeasonSegment': '',
                    'SeasonType': 'Regular Season',
                    'ShotClockRange': '',
                    'StarterBench': '',
                    'TeamID': 0,
                    'TwoWay': 0,
                    'VsConference': '',
                    'VsDivision': '',
                    'Weight': ''
                }
            },
            'team_stats': {
                'url': 'https://stats.nba.com/stats/leaguedashteamstats',
                'params': {
                    'Conference': '',
                    'DateFrom': '',
                    'DateTo': '',
                    'Division': '',
                    'GameScope': '',
                    'GameSegment': '',
                    'LastNGames': 0,
                    'LeagueID': '10',  # WNBA
                    'Location': '',
                    'MeasureType': 'Base',
                    'Month': 0,
                    'OpponentTeamID': 0,
                    'Outcome': '',
                    'PORound': 0,
                    'PaceAdjust': 'N',
                    'PerMode': 'PerGame',
                    'Period': 0,
                    'PlusMinus': 'N',
                    'Rank': 'N',
                    'Season': '2025',
                    'SeasonSegment': '',
                    'SeasonType': 'Regular Season',
                    'ShotClockRange': '',
                    'TeamID': 0,
                    'VsConference': '',
                    'VsDivision': ''
                }
            },
            'game_logs': {
                'url': 'https://stats.nba.com/stats/leaguegamelog',
                'params': {
                    'Counter': 1000,
                    'DateFrom': '',
                    'DateTo': '',
                    'Direction': 'DESC',
                    'LeagueID': '10',  # WNBA
                    'PlayerOrTeam': 'P',
                    'Season': '2025',
                    'SeasonType': 'Regular Season',
                    'Sorter': 'DATE'
                }
            },
            'box_scores': {
                'url': 'https://stats.nba.com/stats/boxscoretraditionalv2',
                'params': {
                    'EndPeriod': 10,
                    'EndRange': 55800,
                    'GameID': '',  # Will be filled per game
                    'RangeType': 2,
                    'Season': '2025',
                    'SeasonType': 'Regular Season',
                    'StartPeriod': 1,
                    'StartRange': 0
                }
            }
        }
        
        # Enhanced API headers (consolidated from nba_api_wnba_integration.py)
        self.headers = {
            'Host': 'stats.nba.com',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:72.0) Gecko/20100101 Firefox/72.0',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate, br',
            'x-nba-stats-origin': 'stats',
            'x-nba-stats-token': 'true',
            'Connection': 'keep-alive',
            'Referer': 'https://stats.nba.com/',
            'Pragma': 'no-cache',
            'Cache-Control': 'no-cache',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin'
        }
        
    def get_current_season(self):
        """Get current WNBA season in correct format"""
        
        current_year = datetime.now().year
        current_month = datetime.now().month
        
        # WNBA season runs May-October
        if current_month >= 5:
            return str(current_year)
        else:
            return str(current_year - 1)

    def setup_api_session(self):
        """Setup API session with proper configuration (consolidated from nba_api_integration.py)"""

        import requests
        self.session = requests.Session()
        self.session.headers.update(self.headers)

        # Rate limiting configuration
        self.last_request_time = 0
        self.min_request_interval = 1.5  # 1.5 seconds between requests (from nba_api_wnba_integration.py)

        self.logger.info("🔗 API session configured with enhanced headers and rate limiting")

    def load_api_tracking(self):
        """Load API usage tracking (consolidated from nba_api_integration.py)"""

        if Path(self.api_tracking_file).exists():
            try:
                with open(self.api_tracking_file, 'r') as f:
                    self.api_tracking = json.load(f)
            except:
                self.api_tracking = {'requests_today': 0, 'last_reset': date.today().isoformat()}
        else:
            self.api_tracking = {'requests_today': 0, 'last_reset': date.today().isoformat()}

    def update_api_tracking(self):
        """Update API usage tracking"""

        # Reset daily counter if new day
        if self.api_tracking['last_reset'] != date.today().isoformat():
            self.api_tracking['requests_today'] = 0
            self.api_tracking['last_reset'] = date.today().isoformat()

        self.api_tracking['requests_today'] += 1

        # Save tracking
        with open(self.api_tracking_file, 'w') as f:
            json.dump(self.api_tracking, f, indent=2)

    def analyze_data_coverage_gaps(self):
        """Analyze current data coverage to identify gaps (consolidated from nba_api_data_update.py)"""

        self.logger.info("📊 Analyzing data coverage gaps")

        if not Path(self.master_dataset_path).exists():
            self.logger.warning("⚠️ Master dataset not found for gap analysis")
            return []

        try:
            df = pd.read_csv(self.master_dataset_path)

            # Analyze date coverage
            if 'game_date' in df.columns:
                df['game_date'] = pd.to_datetime(df['game_date'], errors='coerce')
                date_range = pd.date_range(df['game_date'].min(), df['game_date'].max(), freq='D')
                missing_dates = []

                for date in date_range:
                    if date not in df['game_date'].values:
                        missing_dates.append(date.strftime('%Y-%m-%d'))

                if missing_dates:
                    self.logger.info(f"   📅 Found {len(missing_dates)} missing dates")
                    self.coverage_gaps = missing_dates[:10]  # Store first 10 gaps
                else:
                    self.logger.info("   ✅ No date gaps found")
                    self.coverage_gaps = []

            # Analyze team coverage
            if 'team_abbrev' in df.columns:
                teams_in_data = set(df['team_abbrev'].unique())
                expected_teams = set(self.wnba_teams.keys())
                missing_teams = expected_teams - teams_in_data

                if missing_teams:
                    self.logger.info(f"   🏀 Missing teams: {missing_teams}")
                else:
                    self.logger.info("   ✅ All teams present")

            self.last_gap_analysis = datetime.now()
            return self.coverage_gaps

        except Exception as e:
            self.logger.error(f"❌ Gap analysis error: {e}")
            return []
    
    def fetch_from_endpoint(self, endpoint_name, season=None, retry_count=3):
        """Enhanced fetch with session management and retry logic (consolidated functionality)"""

        if endpoint_name not in self.endpoints:
            self.logger.error(f"Unknown endpoint: {endpoint_name}")
            return None

        endpoint = self.endpoints[endpoint_name]
        params = endpoint['params'].copy()

        # Update season if provided
        if season:
            params['Season'] = season

        # Rate limiting (consolidated from nba_api_wnba_integration.py)
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        if time_since_last < self.min_request_interval:
            sleep_time = self.min_request_interval - time_since_last
            time.sleep(sleep_time)

        for attempt in range(retry_count):
            try:
                self.logger.info(f"🔄 Fetching from {endpoint_name} (attempt {attempt + 1})")

                # Use session for better connection management
                response = self.session.get(
                    endpoint['url'],
                    params=params,
                    timeout=30
                )

                # Update API tracking
                self.update_api_tracking()
                self.last_request_time = time.time()

                if response.status_code == 200:
                    data = response.json()

                    if 'resultSets' in data and len(data['resultSets']) > 0:
                        result_set = data['resultSets'][0]

                        if 'rowSet' in result_set and 'headers' in result_set:
                            df = pd.DataFrame(result_set['rowSet'], columns=result_set['headers'])

                            if len(df) > 0:
                                self.logger.info(f"   ✅ {len(df)} records fetched from {endpoint_name}")
                                return df
                            else:
                                self.logger.info(f"   ℹ️ No data in response from {endpoint_name}")
                                return None
                        else:
                            self.logger.warning(f"   ⚠️ Unexpected response format from {endpoint_name}")
                            return None
                    else:
                        self.logger.info(f"   ℹ️ No result sets in response from {endpoint_name}")
                        return None

                elif response.status_code == 429:  # Rate limited
                    self.logger.warning(f"   ⚠️ Rate limited, waiting before retry...")
                    time.sleep(5 * (attempt + 1))  # Exponential backoff
                    continue

                else:
                    self.logger.error(f"   ❌ HTTP {response.status_code} from {endpoint_name}")
                    if attempt < retry_count - 1:
                        time.sleep(2 * (attempt + 1))  # Wait before retry
                        continue
                    return None

            except requests.exceptions.RequestException as e:
                self.logger.error(f"   ❌ Request error for {endpoint_name}: {e}")
                if attempt < retry_count - 1:
                    time.sleep(2 * (attempt + 1))
                    continue
                return None
            except KeyboardInterrupt:
                self.logger.info(f"   ⏹️ Collection interrupted by user")
                raise
            except Exception as e:
                self.logger.error(f"   ❌ Unexpected error for {endpoint_name}: {e}")
                if attempt < retry_count - 1:
                    time.sleep(2 * (attempt + 1))
                    continue
                return None

        return None
    
    def is_game_already_processed(self, game_id):
        """Check if game has already been processed"""
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM processed_games WHERE game_id = ?", (game_id,))
            count = cursor.fetchone()[0]
            return count > 0
    
    def mark_game_as_processed(self, game_id, game_date, home_team, away_team, data_source):
        """Mark game as processed to prevent duplicates"""
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT OR REPLACE INTO processed_games 
                (game_id, game_date, home_team, away_team, processed_date, data_source)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (game_id, game_date, home_team, away_team, datetime.now(), data_source))
            conn.commit()
    
    def collect_daily_data(self):
        """Enhanced daily data collection with gap analysis (consolidated functionality)"""

        self.logger.info("🌙 Starting 3:00 AM daily data collection")

        # Perform gap analysis first (consolidated from nba_api_data_update.py)
        coverage_gaps = self.analyze_data_coverage_gaps()

        current_season = self.get_current_season()
        collected_data = {}
        collection_summary = {
            'collection_date': date.today().isoformat(),
            'collection_time': datetime.now().isoformat(),
            'season': current_season,
            'endpoints_processed': 0,
            'total_records': 0,
            'new_games': 0,
            'coverage_gaps_found': len(coverage_gaps),
            'api_requests_today': self.api_tracking.get('requests_today', 0),
            'integration_successful': False
        }
        
        # Collect from all endpoints
        for endpoint_name in self.endpoints.keys():
            try:
                self.logger.info(f"📊 Processing {endpoint_name}")

                data = self.fetch_from_endpoint(endpoint_name, current_season)

                if data is not None:
                    # Filter for new games only
                    if endpoint_name == 'game_logs':
                        new_data = self.filter_new_games(data)
                        if len(new_data) > 0:
                            collected_data[endpoint_name] = new_data
                            collection_summary['new_games'] += len(new_data)
                            self.logger.info(f"   ✅ {len(new_data)} new games from {endpoint_name}")
                        else:
                            self.logger.info(f"   ℹ️ No new games from {endpoint_name}")
                    else:
                        collected_data[endpoint_name] = data
                        collection_summary['total_records'] += len(data)
                        self.logger.info(f"   ✅ {len(data)} records from {endpoint_name}")

                    collection_summary['endpoints_processed'] += 1
                else:
                    self.logger.info(f"   ⚠️ No data from {endpoint_name}")

                # Rate limiting - be respectful to NBA API
                time.sleep(3)

            except KeyboardInterrupt:
                self.logger.info(f"⏹️ Collection interrupted by user during {endpoint_name}")
                break
            except Exception as e:
                self.logger.error(f"❌ Error processing {endpoint_name}: {e}")
                # Continue with next endpoint
                continue
        
        # Integrate with master dataset
        if collected_data:
            integration_success = self.integrate_with_master_dataset(collected_data)
            collection_summary['integration_successful'] = integration_success

            if integration_success:
                self.logger.info("✅ Daily collection and integration successful")
                self.last_successful_collection = datetime.now()
            else:
                self.logger.error("❌ Integration failed")
        else:
            self.logger.info("ℹ️ No new data collected (normal during off-season or API issues)")
            # Still mark as successful if no data but no errors
            collection_summary['integration_successful'] = True
            collection_summary['notes'] = 'No new data available - normal during off-season'

        # Save collection summary
        try:
            with open('daily_collection_summary.json', 'w') as f:
                json.dump(collection_summary, f, indent=2)
        except Exception as e:
            self.logger.error(f"❌ Error saving collection summary: {e}")

        # Record in database
        try:
            self.record_collection_in_db(collection_summary)
        except Exception as e:
            self.logger.error(f"❌ Error recording in database: {e}")

        return collection_summary

    def filter_new_games(self, game_logs_df):
        """Filter for games not already processed"""

        if 'GAME_ID' not in game_logs_df.columns:
            return game_logs_df

        new_games = []
        for _, row in game_logs_df.iterrows():
            game_id = str(row['GAME_ID'])

            if not self.is_game_already_processed(game_id):
                new_games.append(row)

                # Mark as processed
                game_date = row.get('GAME_DATE', date.today().isoformat())
                home_team = row.get('MATCHUP', '').split(' vs ')[-1] if ' vs ' in str(row.get('MATCHUP', '')) else 'Unknown'
                away_team = row.get('TEAM_ABBREVIATION', 'Unknown')

                self.mark_game_as_processed(game_id, game_date, home_team, away_team, 'unified_collector')

        return pd.DataFrame(new_games) if new_games else pd.DataFrame()

    def integrate_with_master_dataset(self, collected_data):
        """Integrate new data with master dataset"""

        if not collected_data:
            return False

        self.logger.info("🔗 Integrating with master dataset")

        try:
            # Load master dataset
            if not Path(self.master_dataset_path).exists():
                self.logger.error(f"Master dataset not found: {self.master_dataset_path}")
                return False

            master_df = pd.read_csv(self.master_dataset_path)
            original_shape = master_df.shape

            # Process each collected dataset
            for endpoint_name, new_df in collected_data.items():
                self.logger.info(f"🔄 Integrating {endpoint_name}: {len(new_df)} records")

                # Standardize column names for integration
                standardized_df = self.standardize_columns(new_df, endpoint_name)

                if len(standardized_df) > 0:
                    # Append new data (avoiding duplicates handled at game level)
                    master_df = pd.concat([master_df, standardized_df], ignore_index=True)
                    self.logger.info(f"   ✅ Integrated {len(standardized_df)} records from {endpoint_name}")

            # Remove any duplicate rows that might have been created
            master_df = master_df.drop_duplicates()

            # Save updated master dataset
            master_df.to_csv(self.master_dataset_path, index=False)

            final_shape = master_df.shape
            new_records = final_shape[0] - original_shape[0]

            self.logger.info(f"✅ Master dataset updated:")
            self.logger.info(f"   Original: {original_shape[0]:,} rows")
            self.logger.info(f"   Final: {final_shape[0]:,} rows")
            self.logger.info(f"   Added: {new_records:,} new records")

            # Update federated data
            self.update_federated_data(master_df)

            return True

        except Exception as e:
            self.logger.error(f"❌ Integration error: {e}")
            return False

    def standardize_columns(self, df, endpoint_name):
        """Standardize column names for integration"""

        standardized_df = df.copy()

        # Common column mappings
        column_mappings = {
            'PLAYER_NAME': 'player_name',
            'TEAM_ABBREVIATION': 'team_abbrev',
            'TEAM_NAME': 'team_name',
            'GAME_ID': 'game_id',
            'GAME_DATE': 'game_date',
            'PTS': 'points',
            'REB': 'rebounds',
            'AST': 'assists',
            'MIN': 'minutes',
            'FGM': 'field_goals_made',
            'FGA': 'field_goals_attempted',
            'FG_PCT': 'field_goal_percentage'
        }

        # Apply mappings
        for old_col, new_col in column_mappings.items():
            if old_col in standardized_df.columns:
                standardized_df[new_col] = standardized_df[old_col]

        # Add metadata
        standardized_df['data_source'] = f'unified_collector_{endpoint_name}'
        standardized_df['collection_date'] = date.today().isoformat()
        standardized_df['year'] = int(self.get_current_season())

        # Ensure target column exists (for model training)
        if 'points' in standardized_df.columns and 'target' not in standardized_df.columns:
            standardized_df['target'] = standardized_df['points']

        return standardized_df

    def update_federated_data(self, master_df):
        """Update federated learning data with new master dataset"""

        self.logger.info("🤖 Updating federated learning data")

        federated_dir = Path('federated_data')
        federated_dir.mkdir(exist_ok=True)

        if 'team_abbrev' not in master_df.columns:
            self.logger.warning("⚠️ No team_abbrev column for federated update")
            return

        # Update each team's federated data
        for team_abbrev in self.wnba_teams.keys():
            team_data = master_df[master_df['team_abbrev'] == team_abbrev].copy()

            if len(team_data) > 0:
                team_file = federated_dir / f"{team_abbrev}_data.csv"
                team_data.to_csv(team_file, index=False)
                self.logger.info(f"   ✅ {team_abbrev}: {len(team_data):,} records")
            else:
                self.logger.info(f"   ⚠️ {team_abbrev}: No data found")

    def record_collection_in_db(self, summary):
        """Record collection summary in database"""

        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO collections
                (collection_date, collection_time, endpoint, records_collected,
                 games_processed, new_games, integration_successful, notes)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                summary['collection_date'],
                summary['collection_time'],
                'unified_collection',
                summary['total_records'],
                summary['endpoints_processed'],
                summary['new_games'],
                summary['integration_successful'],
                f"Season: {summary['season']}"
            ))
            conn.commit()

    def run_scheduler(self):
        """Run the automated scheduler with 3:00 AM collection"""

        self.logger.info("⏰ Starting unified automated scheduler")
        self.logger.info("🌙 Daily collection scheduled for 3:00 AM")

        # Schedule daily collection at 3:00 AM (when all games are finished)
        schedule.every().day.at("03:00").do(self.collect_daily_data)

        # Schedule status check every 6 hours
        schedule.every(6).hours.do(self.log_status)

        # Schedule weekly cleanup
        schedule.every().sunday.at("04:00").do(self.weekly_maintenance)

        self.logger.info("✅ Scheduler configured:")
        self.logger.info("   📅 Daily collection: 3:00 AM")
        self.logger.info("   📊 Status checks: Every 6 hours")
        self.logger.info("   🧹 Weekly maintenance: Sunday 4:00 AM")

        while True:
            schedule.run_pending()
            time.sleep(60)  # Check every minute

    def log_status(self):
        """Log current system status"""

        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            # Get recent collections
            cursor.execute('''
                SELECT COUNT(*) FROM collections
                WHERE collection_date >= date('now', '-7 days')
            ''')
            recent_collections = cursor.fetchone()[0]

            # Get last successful collection
            cursor.execute('''
                SELECT collection_date, collection_time FROM collections
                WHERE integration_successful = 1
                ORDER BY collection_time DESC LIMIT 1
            ''')
            last_success = cursor.fetchone()

        status_msg = f"📊 System Status: {recent_collections} collections in last 7 days"
        if last_success:
            status_msg += f", Last success: {last_success[0]} {last_success[1]}"

        self.logger.info(status_msg)

    def weekly_maintenance(self):
        """Weekly maintenance tasks"""

        self.logger.info("🧹 Running weekly maintenance")

        # Clean old log entries (keep 30 days)
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                DELETE FROM collections
                WHERE collection_date < date('now', '-30 days')
            ''')
            deleted = cursor.rowcount
            conn.commit()

        self.logger.info(f"   🗑️ Cleaned {deleted} old collection records")

        # Backup master dataset
        backup_path = f"backups/master_dataset_backup_{date.today().isoformat()}.csv"
        Path("backups").mkdir(exist_ok=True)

        if Path(self.master_dataset_path).exists():
            import shutil
            shutil.copy2(self.master_dataset_path, backup_path)
            self.logger.info(f"   💾 Created backup: {backup_path}")

def main():
    """Main function to setup and run unified collector"""

    print("🔄 UNIFIED WNBA AUTOMATED COLLECTION SYSTEM")
    print("=" * 70)
    print("🎯 Consolidated system combining all three collectors:")
    print("   • automated_nba_api_collector.py")
    print("   • enhanced_automated_collector.py")
    print("   • updated_2025_season_collector.py")
    print()
    print("✅ Features:")
    print("   🌙 Daily collection at 3:00 AM (when all games finished)")
    print("   🏀 2025 season support with Golden State Valkyries")
    print("   🛡️ Smart duplicate prevention (game-level)")
    print("   🔗 Seamless master dataset integration")
    print("   🤖 Automatic federated learning updates")
    print("   📊 Comprehensive logging and monitoring")
    print("   🗄️ SQLite tracking database")
    print("   📋 Weekly maintenance and backups")
    print()

    # Initialize unified collector
    collector = UnifiedWNBACollector()

    # Test collection
    print("🧪 Testing unified collection system...")
    test_summary = collector.collect_daily_data()

    if test_summary['integration_successful']:
        print(f"✅ Test successful!")
        print(f"   Endpoints processed: {test_summary['endpoints_processed']}")
        print(f"   Total records: {test_summary['total_records']}")
        print(f"   New games: {test_summary['new_games']}")
        print(f"   Integration: {'✅ Success' if test_summary['integration_successful'] else '❌ Failed'}")
    else:
        print(f"⚠️ Test completed with issues - check logs")

    # Save configuration
    config = {
        'system_name': 'Unified WNBA Automated Collector',
        'version': '1.0.0',
        'setup_date': datetime.now().isoformat(),
        'consolidates': [
            'automated_nba_api_collector.py',
            'enhanced_automated_collector.py',
            'updated_2025_season_collector.py'
        ],
        'schedule': {
            'daily_collection': '03:00 AM',
            'status_checks': 'Every 6 hours',
            'weekly_maintenance': 'Sunday 04:00 AM'
        },
        'features': [
            '3:00 AM collection when all games finished',
            '2025 season support with Golden State Valkyries',
            'Smart game-level duplicate prevention',
            'Master dataset integration',
            'Federated learning updates',
            'Comprehensive logging',
            'SQLite tracking database',
            'Weekly maintenance and backups'
        ],
        'database': 'unified_collection_tracking.db',
        'log_file': 'unified_wnba_collector.log',
        'master_dataset': 'wnba_definitive_master_dataset_FIXED.csv'
    }

    with open('unified_collector_config.json', 'w') as f:
        json.dump(config, f, indent=2)

    print(f"\n🎯 UNIFIED SYSTEM READY!")
    print(f"   📋 Configuration: unified_collector_config.json")
    print(f"   📊 Test summary: daily_collection_summary.json")
    print(f"   📝 Logs: unified_wnba_collector.log")
    print(f"   🗄️ Database: unified_collection_tracking.db")

    return collector

if __name__ == "__main__":
    collector = main()

    # Ask if user wants to start scheduler
    response = input(f"\n🔄 Start the 3:00 AM scheduler now? (y/n): ").lower().strip()

    if response == 'y':
        print(f"\n⏰ Starting unified scheduler...")
        print(f"🌙 System will collect data daily at 3:00 AM")
        print(f"💡 Press Ctrl+C to stop")

        try:
            collector.run_scheduler()
        except KeyboardInterrupt:
            print(f"\n⏹️ Scheduler stopped by user")
            print(f"💡 System configuration remains active")
    else:
        print(f"\n💡 Scheduler not started")
        print(f"🔄 Run this script again to start the scheduler")
