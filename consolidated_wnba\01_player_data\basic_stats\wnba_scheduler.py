import os
import sys
import json
import logging
import asyncio
from datetime import datetime, timed<PERSON>ta
from typing import Optional
            from scripts.wnba_data_pipeline_manager import WNBADataPipelineManager
    import argparse


"""
WNBA Data Collection Scheduler

This script provides scheduling capabilities for automated WNBA data collection.
Can be integrated with Windows Task Scheduler, cron, or run as a service.
"""


# Configure logging for scheduler
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/wnba_scheduler.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class WNBAScheduler:
    """
    Scheduler for automated WNBA data collection.
    """
    
    def __init__(self):
        self.script_dir = os.path.dirname(os.path.abspath(__file__))
        self.project_root = os.path.dirname(self.script_dir)
        self.logs_dir = os.path.join(self.project_root, 'logs')
        
        # Ensure logs directory exists
        os.makedirs(self.logs_dir, exist_ok=True)
    
    async def run_scheduled_collection(self):
        """Run scheduled WNBA data collection."""
        logger.info("Starting scheduled WNBA data collection...")
        
        try:
            # Import and run the pipeline manager
            sys.path.append(self.project_root)
            
            # Initialize manager with config
            config_path = os.path.join(self.project_root, 'config', 'wnba_config.json')
            manager = WNBADataPipelineManager(config_path)
            
            # Check if update is needed
            if not manager.should_update_data():
                logger.info("Data is current, no update needed")
                return True
            
            # Run data collection
            success = await manager.run_data_collection()
            
            if success:
                logger.info("✅ Scheduled WNBA data collection completed successfully")
                self._log_collection_success()
            else:
                logger.error("❌ Scheduled WNBA data collection failed")
                self._log_collection_failure()
            
            return success
            
        except Exception as e:
            logger.error(f"Scheduler error: {e}")
            self._log_collection_failure(str(e))
            return False
    
    def _log_collection_success(self):
        """Log successful collection for monitoring."""
        log_entry = {
            "timestamp": datetime.now().isoformat(),
            "status": "success",
            "type": "scheduled_collection"
        }
        self._write_status_log(log_entry)
    
    def _log_collection_failure(self, error_message: Optional[str] = None):
        """Log failed collection for monitoring."""
        log_entry = {
            "timestamp": datetime.now().isoformat(),
            "status": "failure",
            "type": "scheduled_collection",
            "error": error_message
        }
        self._write_status_log(log_entry)
    
    def _write_status_log(self, log_entry: dict):
        """Write status log entry."""
        try:
            status_log_path = os.path.join(self.logs_dir, 'wnba_status.jsonl')
            with open(status_log_path, 'a') as f:
                f.write(json.dumps(log_entry) + '\n')
        except Exception as e:
            logger.warning(f"Could not write status log: {e}")
    
    def get_recent_runs(self, days: int = 7) -> list:
        """Get recent run status from logs."""
        try:
            status_log_path = os.path.join(self.logs_dir, 'wnba_status.jsonl')
            if not os.path.exists(status_log_path):
                return []
            
            recent_runs = []
            cutoff_date = datetime.now() - timedelta(days=days)
            
            with open(status_log_path, 'r') as f:
                for line in f:
                    try:
                        entry = json.loads(line.strip())
                        entry_date = datetime.fromisoformat(entry['timestamp'])
                        if entry_date >= cutoff_date:
                            recent_runs.append(entry)
                    except (json.JSONDecodeError, KeyError, ValueError):
                        continue
            
            return sorted(recent_runs, key=lambda x: x['timestamp'], reverse=True)
            
        except Exception as e:
            logger.warning(f"Could not read status log: {e}")
            return []


async def main():
    """Main entry point for scheduler."""
    scheduler = WNBAScheduler()
    
    # Check command line arguments
    parser = argparse.ArgumentParser(description="WNBA Data Collection Scheduler")
    parser.add_argument("--run", action="store_true", help="Run scheduled collection")
    parser.add_argument("--status", action="store_true", help="Show recent run status")
    parser.add_argument("--days", type=int, default=7, help="Days of history to show")
    
    args = parser.parse_args()
    
    if args.status:
        # Show recent runs
        recent_runs = scheduler.get_recent_runs(args.days)
        
        if not recent_runs:
        else:
            for run in recent_runs:
                timestamp = datetime.fromisoformat(run['timestamp']).strftime('%Y-%m-%d %H:%M:%S')
                status = "✅ SUCCESS" if run['status'] == 'success' else "❌ FAILURE"
                if run.get('error'):
        
        return
    
    if args.run:
        # Run scheduled collection
        success = await scheduler.run_scheduled_collection()
        sys.exit(0 if success else 1)
    else:
        # Show help
        parser.print_help()


if __name__ == "__main__":
    asyncio.run(main())
