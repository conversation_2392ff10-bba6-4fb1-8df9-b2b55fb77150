
# 🎯 UPDATED TO USE EXPERT DATASET
# This script now uses the consolidated expert dataset: data/master/wnba_expert_dataset.csv
# Updated on: 2025-07-12 20:00:10
# Expert dataset contains: 49,512 high-quality records with 840 features
# All duplicates removed, data quality validated

#!/usr/bin/env python3
"""
🏀 WNBA NOISE-REDUCED PRODUCTION MODEL
====================================

EXPERT NOISE REDUCTION + TRAINING:
✅ Remove extreme values and outliers
✅ Handle 16% missing data properly
✅ Reduce 636 features to ~150 best features
✅ Remove multicollinear features
✅ Lower learning rate for stability
✅ Stronger regularization
✅ Better early stopping

Addresses root cause of overfitting: noise-to-signal ratio
"""

import pandas as pd
import numpy as np
import json
import logging
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Tuple, Any
from sklearn.feature_selection import SelectKBest, f_regression
from sklearn.ensemble import RandomForestRegressor
from sklearn.preprocessing import StandardScaler
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

# PyTorch and Lightning
import torch
import pytorch_lightning as pl
from pytorch_lightning.callbacks import EarlyStopping, ModelCheckpoint
from pytorch_lightning.loggers import TensorBoardLogger
from pytorch_lightning import seed_everything

# Our model
from src.models.modern_player_points_model import PlayerPointsModel, WNBADataModule

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# NOISE-REDUCED CONFIGURATION
RANDOM_SEED = 42
TARGET_FEATURES = 150  # Reduce from 636 to 150 (4x reduction)
EXPERT_EPOCHS = 80     # Reduced epochs for cleaner data
EXPERT_PATIENCE = 15   # Aggressive early stopping
MIN_DELTA = 0.001      # Require meaningful improvement

class NoiseReducedTrainer:
    """
    Noise-reduced trainer addressing overfitting root causes
    """
    
    def __init__(self):
        self.random_seed = RANDOM_SEED
        seed_everything(self.random_seed, workers=True)
        
        logger.info("🔍 NOISE-REDUCED TRAINER INITIALIZED")
        logger.info(f"🎯 Target features: {TARGET_FEATURES} (from 636)")
        logger.info(f"📊 Addressing: 16% missing data, extreme values, multicollinearity")
    
    def clean_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Comprehensive data cleaning
        """
        logger.info("🧹 CLEANING DATA...")
        
        initial_count = len(df)
        
        # Remove extreme target values
        if 'target' in df.columns:
            # Remove extreme outliers (>40 points or <0)
            clean_mask = (df['target'] >= 0) & (df['target'] <= 40)
            df = df[clean_mask].copy()
            removed = initial_count - len(df)
            logger.info(f"   📊 Removed {removed} extreme target values")
        
        # Remove rows with too much missing data
        missing_threshold = 0.5  # Remove rows with >50% missing
        row_missing_pct = df.isna().sum(axis=1) / len(df.columns)
        clean_rows = row_missing_pct <= missing_threshold
        df = df[clean_rows].copy()
        removed_rows = initial_count - len(df)
        logger.info(f"   ❌ Removed {removed_rows} rows with excessive missing data")
        
        # Remove duplicate rows
        duplicates = df.duplicated().sum()
        if duplicates > 0:
            df = df.drop_duplicates()
            logger.info(f"   🔄 Removed {duplicates} duplicate rows")
        
        logger.info(f"✅ Data cleaning: {initial_count:,} → {len(df):,} samples")
        return df
    
    def select_best_features(self, df: pd.DataFrame, feature_cols: List[str]) -> List[str]:
        """
        Select best features to reduce noise
        """
        logger.info(f"🎯 SELECTING BEST {TARGET_FEATURES} FEATURES...")
        
        # Prepare data
        X = df[feature_cols].fillna(0)  # Simple imputation for feature selection
        y = df['target']
        
        # Method 1: Random Forest importance (robust to noise)
        logger.info("   🌲 Computing Random Forest importance...")
        rf = RandomForestRegressor(n_estimators=50, random_state=42, n_jobs=-1)
        rf.fit(X, y)
        rf_scores = pd.Series(rf.feature_importances_, index=feature_cols)
        
        # Method 2: F-statistic (statistical significance)
        logger.info("   📊 Computing F-statistics...")
        f_scores, _ = f_regression(X, y)
        f_scores = pd.Series(f_scores, index=feature_cols)
        
        # Combine scores (weighted toward RF for noise robustness)
        combined_scores = (
            0.7 * rf_scores / rf_scores.max() +
            0.3 * f_scores / f_scores.max()
        )
        
        # Select top features
        best_features = combined_scores.nlargest(TARGET_FEATURES).index.tolist()
        
        logger.info(f"✅ Selected {len(best_features)} best features")
        logger.info(f"   🌟 Top 5: {best_features[:5]}")
        
        return best_features
    
    def remove_correlated_features(self, df: pd.DataFrame, feature_cols: List[str]) -> List[str]:
        """
        Remove highly correlated features
        """
        logger.info("🔗 REMOVING CORRELATED FEATURES...")
        
        # Calculate correlations
        corr_matrix = df[feature_cols].fillna(0).corr().abs()
        
        # Find highly correlated pairs
        upper_triangle = corr_matrix.where(
            np.triu(np.ones(corr_matrix.shape), k=1).astype(bool)
        )
        
        # Remove features with correlation > 0.9
        to_remove = []
        for column in upper_triangle.columns:
            correlated = upper_triangle.index[upper_triangle[column] > 0.9].tolist()
            if correlated:
                to_remove.extend(correlated)
        
        # Keep unique features
        to_remove = list(set(to_remove))
        clean_features = [col for col in feature_cols if col not in to_remove]
        
        logger.info(f"✅ Correlation removal: {len(to_remove)} features removed")
        logger.info(f"📊 Remaining: {len(clean_features)} features")
        
        return clean_features
    
    def prepare_noise_reduced_data(self) -> Tuple[pd.DataFrame, pd.DataFrame, pd.DataFrame, List[str]]:
        """
        Complete noise reduction pipeline
        """
        logger.info("🚀 NOISE REDUCTION PIPELINE")
        logger.info("=" * 50)
        
        # Load data
        data_path = Path("data/master/wnba_expert_dataset.csv")
        df = pd.read_csv(data_path)
        logger.info(f"📊 Loaded: {df.shape}")
        
        # Clean data
        df = self.clean_data(df)
        
        # Define initial features
        exclude_cols = [
            'target', 'player_name', 'team_abbrev', 'game_id', 'game_date', 
            'year', 'player_id', 'team_id', 'season', 'SEASON_ID', 'GAME_DATE',
            'collection_date'
        ]
        feature_cols = [col for col in df.columns if col not in exclude_cols]
        logger.info(f"🔧 Initial features: {len(feature_cols)}")
        
        # Remove correlated features
        feature_cols = self.remove_correlated_features(df, feature_cols)
        
        # Select best features
        best_features = self.select_best_features(df, feature_cols)
        
        # Handle missing data in selected features
        logger.info("❌ HANDLING MISSING DATA...")
        for col in best_features:
            if col in df.columns:
                # Use median imputation for numerical features
                median_val = df[col].median()
                df[col] = df[col].fillna(median_val)
        
        missing_after = df[best_features].isna().sum().sum()
        logger.info(f"✅ Missing data handled: {missing_after} remaining")
        
        # Temporal splits
        if 'year' in df.columns:
            train_mask = df['year'] <= 2022
            val_mask = df['year'] == 2023
            test_mask = df['year'] >= 2024
            
            train_df = df[train_mask].copy()
            val_df = df[val_mask].copy()
            test_df = df[test_mask].copy()
            
            logger.info("✅ NOISE-REDUCED DATA READY:")
            logger.info(f"   📈 Train: {len(train_df):,} samples")
            logger.info(f"   📊 Val: {len(val_df):,} samples")
            logger.info(f"   🧪 Test: {len(test_df):,} samples")
            logger.info(f"   🔧 Features: {len(best_features)} (reduced from 636)")
            logger.info(f"   📊 Samples per feature: {len(train_df) // len(best_features)} (much better!)")
        
        return train_df, val_df, test_df, best_features
    
    def train_noise_reduced_model(self) -> Dict[str, Any]:
        """
        Train model on noise-reduced data
        """
        logger.info("🚀 TRAINING NOISE-REDUCED MODEL")
        logger.info("=" * 50)
        
        training_start = datetime.now()
        
        # Prepare clean data
        train_df, val_df, test_df, features = self.prepare_noise_reduced_data()
        
        # Create data module
        data_module = WNBADataModule(
            train_df=train_df,
            val_df=val_df,
            test_df=test_df,
            feature_cols=features,
            target_col='target',
            batch_size=256,  # Larger batch for cleaner data
            num_workers=4
        )
        
        # Model with noise-optimized config
        model = PlayerPointsModel(
            input_dim=len(features),
            dropout=0.3,  # Moderate dropout for clean data
            learning_rate=0.0002,  # Conservative learning rate
            use_role_embedding=True
        )
        
        # Callbacks
        callbacks = [
            EarlyStopping(
                monitor='val_loss',
                patience=EXPERT_PATIENCE,
                mode='min',
                verbose=True,
                min_delta=MIN_DELTA
            ),
            ModelCheckpoint(
                dirpath='models/noise_reduced_production',
                filename='clean_model_{epoch:02d}_{val_loss:.4f}',
                monitor='val_loss',
                mode='min',
                save_top_k=3,
                verbose=True
            )
        ]
        
        # Trainer
        trainer = pl.Trainer(
            max_epochs=EXPERT_EPOCHS,
            callbacks=callbacks,
            logger=TensorBoardLogger('models/noise_reduced_production/logs', name='clean_training'),
            accelerator='auto',
            devices='auto',
            precision='16-mixed',
            gradient_clip_val=0.5,
            deterministic=True
        )
        
        logger.info("🏋️ Starting clean training...")
        trainer.fit(model, data_module)
        
        training_end = datetime.now()
        duration = training_end - training_start
        
        # Results
        best_path = trainer.checkpoint_callback.best_model_path
        best_score = trainer.checkpoint_callback.best_model_score
        
        logger.info("✅ NOISE-REDUCED TRAINING COMPLETE!")
        logger.info(f"🏆 Best model: {best_path}")
        logger.info(f"📊 Best val_loss: {best_score:.4f}")
        logger.info(f"⏱️ Duration: {duration}")
        
        # Save metadata
        metadata = {
            'timestamp': training_start.isoformat(),
            'duration_seconds': duration.total_seconds(),
            'noise_reduction': {
                'original_features': 636,
                'selected_features': len(features),
                'feature_reduction': f"{(636-len(features))/636*100:.1f}%",
                'missing_data_handled': True,
                'extreme_values_removed': True,
                'correlations_removed': True
            },
            'model_config': {
                'features': len(features),
                'dropout': 0.3,
                'learning_rate': 0.0002,
                'batch_size': 256
            },
            'training_config': {
                'epochs': EXPERT_EPOCHS,
                'patience': EXPERT_PATIENCE,
                'min_delta': MIN_DELTA
            },
            'results': {
                'best_model_path': best_path,
                'best_val_loss': float(best_score)
            }
        }
        
        metadata_path = Path('models/noise_reduced_production/metadata.json')
        metadata_path.parent.mkdir(parents=True, exist_ok=True)
        with open(metadata_path, 'w') as f:
            json.dump(metadata, f, indent=2)
        
        logger.info(f"📋 Metadata saved: {metadata_path}")
        return metadata

def main():
    """Main function"""
    
    logger.info("🔍 STARTING NOISE-REDUCED WNBA MODEL TRAINING")
    logger.info("Addressing overfitting root cause: noise-to-signal ratio")
    
    trainer = NoiseReducedTrainer()
    results = trainer.train_noise_reduced_model()
    
    logger.info("🎯 NOISE-REDUCED MODEL COMPLETE!")
    logger.info("Expected: Much better generalization, less overfitting")
    
    return results

if __name__ == "__main__":
    main()
