#!/usr/bin/env python3
"""
Extract Additional NBA API Data

This script systematically tests and extracts data from the most relevant
NBA API endpoints for WNBA data enhancement.

Author: WNBA Analytics Team
Date: 2025-07-11
"""

import pandas as pd
import numpy as np
import requests
import json
from datetime import datetime, timedelta
import time
import warnings
warnings.filterwarnings('ignore')

def setup_nba_api_headers():
    """Setup proper headers for NBA API requests"""
    
    headers = {
        'Host': 'stats.nba.com',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/119.0',
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate, br',
        'x-nba-stats-origin': 'stats',
        'x-nba-stats-token': 'true',
        'Connection': 'keep-alive',
        'Referer': 'https://stats.nba.com/',
        '<PERSON>ragma': 'no-cache',
        'Cache-Control': 'no-cache',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-origin'
    }
    
    return headers

def test_priority_endpoints():
    """Test the highest priority endpoints for WNBA data"""
    
    print("🎯 TESTING PRIORITY NBA API ENDPOINTS")
    print("=" * 60)
    
    headers = setup_nba_api_headers()
    successful_extractions = {}
    
    # Priority endpoints based on the list provided
    priority_endpoints = [
        {
            'name': 'League Player Stats',
            'url': 'https://stats.nba.com/stats/leaguedashplayerstats',
            'params': {'LeagueID': '10', 'Season': '2024', 'SeasonType': 'Regular Season'},
            'description': 'Basic player statistics'
        },
        {
            'name': 'League Team Stats',
            'url': 'https://stats.nba.com/stats/leaguedashteamstats',
            'params': {'LeagueID': '10', 'Season': '2024', 'SeasonType': 'Regular Season'},
            'description': 'Basic team statistics'
        },
        {
            'name': 'Player Bio Stats',
            'url': 'https://stats.nba.com/stats/leaguedashplayerbiostats',
            'params': {'LeagueID': '10', 'Season': '2024', 'SeasonType': 'Regular Season'},
            'description': 'Player biographical information'
        },
        {
            'name': 'Player Clutch Stats',
            'url': 'https://stats.nba.com/stats/leaguedashplayerclutch',
            'params': {'LeagueID': '10', 'Season': '2024', 'SeasonType': 'Regular Season'},
            'description': 'Clutch performance statistics'
        },
        {
            'name': 'Player Shot Locations',
            'url': 'https://stats.nba.com/stats/leaguedashplayershotlocations',
            'params': {'LeagueID': '10', 'Season': '2024', 'SeasonType': 'Regular Season'},
            'description': 'Shot location analytics'
        },
        {
            'name': 'Player Tracking Stats',
            'url': 'https://stats.nba.com/stats/leaguedashptstats',
            'params': {'LeagueID': '10', 'Season': '2024', 'SeasonType': 'Regular Season', 'PlayerOrTeam': 'Player'},
            'description': 'Player tracking data'
        },
        {
            'name': 'Defensive Tracking',
            'url': 'https://stats.nba.com/stats/leaguedashptdefend',
            'params': {'LeagueID': '10', 'Season': '2024', 'SeasonType': 'Regular Season'},
            'description': 'Defensive tracking metrics'
        },
        {
            'name': 'Hustle Stats Player',
            'url': 'https://stats.nba.com/stats/leaguehustlestatsplayer',
            'params': {'LeagueID': '10', 'Season': '2024', 'SeasonType': 'Regular Season'},
            'description': 'Player hustle statistics'
        },
        {
            'name': 'Shot Chart Detail',
            'url': 'https://stats.nba.com/stats/shotchartdetail',
            'params': {'LeagueID': '10', 'Season': '2024', 'SeasonType': 'Regular Season', 'PlayerID': '0'},
            'description': 'Detailed shot chart data'
        },
        {
            'name': 'Common Team Roster',
            'url': 'https://stats.nba.com/stats/commonteamroster',
            'params': {'LeagueID': '10', 'Season': '2024', 'TeamID': '1611661313'},  # Sample team ID
            'description': 'Team roster information'
        }
    ]
    
    for endpoint in priority_endpoints:
        print(f"\n🔍 Testing: {endpoint['name']}")
        print(f"   📝 {endpoint['description']}")
        
        try:
            response = requests.get(endpoint['url'], headers=headers, params=endpoint['params'], timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                
                if 'resultSets' in data and len(data['resultSets']) > 0:
                    result_set = data['resultSets'][0]
                    
                    if 'rowSet' in result_set and len(result_set['rowSet']) > 0:
                        headers_list = result_set.get('headers', [])
                        row_count = len(result_set['rowSet'])
                        
                        print(f"   ✅ SUCCESS: {row_count} records, {len(headers_list)} columns")
                        print(f"   📋 Columns: {headers_list[:8]}...")
                        
                        # Store successful extraction
                        successful_extractions[endpoint['name']] = {
                            'url': endpoint['url'],
                            'params': endpoint['params'],
                            'data': pd.DataFrame(result_set['rowSet'], columns=headers_list),
                            'record_count': row_count,
                            'columns': headers_list,
                            'description': endpoint['description']
                        }
                        
                        # Show sample data
                        if row_count > 0:
                            sample_df = pd.DataFrame(result_set['rowSet'], columns=headers_list)
                            if 'PLAYER_NAME' in sample_df.columns:
                                sample_players = sample_df['PLAYER_NAME'].head(3).tolist()
                                print(f"   👥 Sample players: {sample_players}")
                            elif 'TEAM_NAME' in sample_df.columns:
                                sample_teams = sample_df['TEAM_NAME'].head(3).tolist()
                                print(f"   🏀 Sample teams: {sample_teams}")
                    else:
                        print(f"   ⚠️ No data rows returned")
                else:
                    print(f"   ⚠️ No result sets in response")
            else:
                print(f"   ❌ Failed: HTTP {response.status_code}")
        
        except Exception as e:
            print(f"   ❌ Error: {e}")
        
        # Rate limiting
        time.sleep(2)
    
    return successful_extractions

def test_advanced_endpoints():
    """Test advanced/specialized endpoints"""
    
    print(f"\n🚀 TESTING ADVANCED ENDPOINTS")
    print("=" * 60)
    
    headers = setup_nba_api_headers()
    advanced_extractions = {}
    
    # Advanced endpoints
    advanced_endpoints = [
        {
            'name': 'Player Estimated Metrics',
            'url': 'https://stats.nba.com/stats/playerestimatedmetrics',
            'params': {'LeagueID': '10', 'Season': '2024', 'SeasonType': 'Regular Season'},
            'description': 'Advanced estimated metrics'
        },
        {
            'name': 'Team Estimated Metrics',
            'url': 'https://stats.nba.com/stats/teamestimatedmetrics',
            'params': {'LeagueID': '10', 'Season': '2024', 'SeasonType': 'Regular Season'},
            'description': 'Team advanced metrics'
        },
        {
            'name': 'League Game Finder',
            'url': 'https://stats.nba.com/stats/leaguegamefinder',
            'params': {'LeagueID': '10', 'Season': '2024', 'SeasonType': 'Regular Season'},
            'description': 'Game-level data finder'
        },
        {
            'name': 'Synergy Play Types',
            'url': 'https://stats.nba.com/stats/synergyplaytypes',
            'params': {'LeagueID': '10', 'Season': '2024', 'SeasonType': 'Regular Season'},
            'description': 'Play type analytics'
        },
        {
            'name': 'League Lineups',
            'url': 'https://stats.nba.com/stats/leaguedashlineups',
            'params': {'LeagueID': '10', 'Season': '2024', 'SeasonType': 'Regular Season'},
            'description': 'Lineup performance data'
        }
    ]
    
    for endpoint in advanced_endpoints:
        print(f"\n🔍 Testing: {endpoint['name']}")
        
        try:
            response = requests.get(endpoint['url'], headers=headers, params=endpoint['params'], timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                
                if 'resultSets' in data and len(data['resultSets']) > 0:
                    result_set = data['resultSets'][0]
                    
                    if 'rowSet' in result_set and len(result_set['rowSet']) > 0:
                        headers_list = result_set.get('headers', [])
                        row_count = len(result_set['rowSet'])
                        
                        print(f"   ✅ SUCCESS: {row_count} records, {len(headers_list)} columns")
                        
                        advanced_extractions[endpoint['name']] = {
                            'url': endpoint['url'],
                            'params': endpoint['params'],
                            'data': pd.DataFrame(result_set['rowSet'], columns=headers_list),
                            'record_count': row_count,
                            'columns': headers_list,
                            'description': endpoint['description']
                        }
                    else:
                        print(f"   ⚠️ No data returned")
                else:
                    print(f"   ⚠️ No result sets")
            else:
                print(f"   ❌ Failed: HTTP {response.status_code}")
        
        except Exception as e:
            print(f"   ❌ Error: {e}")
        
        time.sleep(2)
    
    return advanced_extractions

def test_historical_seasons():
    """Test multiple historical seasons to find data coverage"""
    
    print(f"\n📅 TESTING HISTORICAL SEASONS")
    print("=" * 60)
    
    headers = setup_nba_api_headers()
    historical_data = {}
    
    # Test seasons from 2020-2024
    seasons_to_test = ['2020', '2021', '2022', '2023', '2024']
    
    for season in seasons_to_test:
        print(f"\n📊 Testing season: {season}")
        
        try:
            url = 'https://stats.nba.com/stats/leaguedashplayerstats'
            params = {
                'LeagueID': '10',
                'Season': season,
                'SeasonType': 'Regular Season'
            }
            
            response = requests.get(url, headers=headers, params=params, timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                
                if 'resultSets' in data and len(data['resultSets']) > 0:
                    result_set = data['resultSets'][0]
                    
                    if 'rowSet' in result_set and len(result_set['rowSet']) > 0:
                        row_count = len(result_set['rowSet'])
                        headers_list = result_set.get('headers', [])
                        
                        print(f"   ✅ {season}: {row_count} player records")
                        
                        # Store data
                        df = pd.DataFrame(result_set['rowSet'], columns=headers_list)
                        historical_data[season] = {
                            'data': df,
                            'record_count': row_count,
                            'columns': headers_list
                        }
                        
                        # Show sample teams
                        if 'TEAM_ABBREVIATION' in df.columns:
                            teams = df['TEAM_ABBREVIATION'].unique()
                            print(f"   🏀 Teams: {sorted(teams)}")
                    else:
                        print(f"   ⚠️ {season}: No data")
                else:
                    print(f"   ⚠️ {season}: No result sets")
            else:
                print(f"   ❌ {season}: HTTP {response.status_code}")
        
        except Exception as e:
            print(f"   ❌ {season}: {e}")
        
        time.sleep(2)
    
    return historical_data

def integrate_new_api_data(successful_extractions, advanced_extractions, historical_data):
    """Integrate newly extracted API data with existing dataset"""
    
    print(f"\n🔗 INTEGRATING NEW API DATA")
    print("=" * 60)
    
    # Load current dataset
    current_df = pd.read_csv("wnba_ultimate_expert_dataset_complete.csv")
    print(f"📊 Current dataset: {current_df.shape}")
    
    enhanced_df = current_df.copy()
    new_features_added = 0
    
    # Process successful extractions
    for endpoint_name, extraction_data in successful_extractions.items():
        print(f"\n🔄 Processing: {endpoint_name}")
        
        api_df = extraction_data['data']
        
        if len(api_df) > 0:
            # Standardize column names
            if 'PLAYER_NAME' in api_df.columns:
                api_df = api_df.rename(columns={'PLAYER_NAME': 'player_name'})
            if 'TEAM_ABBREVIATION' in api_df.columns:
                api_df = api_df.rename(columns={'TEAM_ABBREVIATION': 'team_abbrev'})
            
            # Add year and source
            api_df['year'] = 2024  # From our test parameters
            api_df['api_source'] = endpoint_name.lower().replace(' ', '_')
            
            # Get new columns (exclude merge keys and metadata)
            exclude_cols = ['player_name', 'team_abbrev', 'year', 'api_source', 'PLAYER_ID', 'TEAM_ID']
            new_cols = [col for col in api_df.columns if col not in exclude_cols]
            
            if new_cols and 'player_name' in api_df.columns:
                # Add prefix to avoid conflicts
                prefix = f"api_{endpoint_name.lower().replace(' ', '_')}_"
                api_df_renamed = api_df.copy()
                
                for col in new_cols:
                    new_col_name = f"{prefix}{col}"
                    if new_col_name not in enhanced_df.columns:
                        api_df_renamed[new_col_name] = api_df_renamed[col]
                
                # Merge with main dataset
                merge_cols = ['player_name', 'year']
                new_feature_cols = [f"{prefix}{col}" for col in new_cols if f"{prefix}{col}" not in enhanced_df.columns]
                
                if new_feature_cols:
                    enhanced_df = enhanced_df.merge(
                        api_df_renamed[merge_cols + new_feature_cols],
                        on=merge_cols,
                        how='left'
                    )
                    
                    new_features_added += len(new_feature_cols)
                    print(f"   ✅ Added {len(new_feature_cols)} features")
                else:
                    print(f"   ⚠️ No new features to add")
            else:
                print(f"   ⚠️ No suitable columns for merging")
    
    print(f"\n✅ API Integration Complete:")
    print(f"   Original features: {len(current_df.columns)}")
    print(f"   New API features: {new_features_added}")
    print(f"   Total features: {len(enhanced_df.columns)}")
    
    return enhanced_df

def main():
    """Main function to extract additional NBA API data"""
    
    print("🔗 EXTRACTING ADDITIONAL NBA API DATA")
    print("=" * 80)
    print("📅 Date: July 11, 2025")
    print("🎯 Goal: Extract maximum additional WNBA data from NBA API")
    print()
    
    # Test priority endpoints
    successful_extractions = test_priority_endpoints()
    
    # Test advanced endpoints
    advanced_extractions = test_advanced_endpoints()
    
    # Test historical seasons
    historical_data = test_historical_seasons()
    
    # Integrate new data
    if successful_extractions or advanced_extractions:
        enhanced_df = integrate_new_api_data(successful_extractions, advanced_extractions, historical_data)
        
        # Save enhanced dataset
        output_path = "wnba_ultimate_expert_dataset_with_api_enhancements.csv"
        enhanced_df.to_csv(output_path, index=False)
        
        print(f"\n🎉 ENHANCED DATASET SAVED!")
        print(f"📁 File: {output_path}")
        print(f"📊 Shape: {enhanced_df.shape}")
        print(f"💾 Size: {Path(output_path).stat().st_size / 1024 / 1024:.1f} MB")
    else:
        print(f"\n⚠️ No additional data extracted from API")
        print(f"💡 Current dataset remains the most comprehensive available")
    
    # Summary report
    print(f"\n📊 EXTRACTION SUMMARY")
    print("=" * 60)
    print(f"✅ Priority endpoints tested: {len(successful_extractions)} successful")
    print(f"✅ Advanced endpoints tested: {len(advanced_extractions)} successful")
    print(f"✅ Historical seasons tested: {len(historical_data)} successful")
    
    # Save extraction results
    extraction_results = {
        'extraction_date': '2025-07-11',
        'successful_extractions': {name: {
            'record_count': data['record_count'],
            'columns': data['columns'],
            'description': data['description']
        } for name, data in successful_extractions.items()},
        'advanced_extractions': {name: {
            'record_count': data['record_count'],
            'columns': data['columns'],
            'description': data['description']
        } for name, data in advanced_extractions.items()},
        'historical_coverage': {season: data['record_count'] for season, data in historical_data.items()},
        'total_new_features': sum(len(data['columns']) for data in successful_extractions.values()),
        'api_status': 'Limited access due to server restrictions'
    }
    
    with open('nba_api_extraction_results.json', 'w') as f:
        json.dump(extraction_results, f, indent=2, default=str)
    
    print(f"\n📁 Results saved: nba_api_extraction_results.json")
    print(f"🎯 EXTRACTION COMPLETE!")
    
    return extraction_results

if __name__ == "__main__":
    from pathlib import Path
    extraction_results = main()
