import os
import sys
import json
import logging
from datetime import datetime, timedelta
import asyncio
from typing import Optional, Dict, Any
            from scripts.build_moneyline_training_data_wnba import fetch_games_and_features, validate_training_data
                import shutil
            import glob
    import argparse


"""
Enhanced WNBA Training Data Integration and Scheduling Script

This script provides additional functionality for the WNBA moneyline training data:
1. Integration with ML training pipeline
2. Automated scheduling capabilities
3. Data quality monitoring
4. Performance optimization features
"""


# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class WNBADataPipelineManager:
    """
    Manager class for WNBA data pipeline operations.
    Handles scheduling, monitoring, and integration with ML systems.
    """
    
    def __init__(self, config_path: Optional[str] = None):
        self.config = self._load_config(config_path)
        self.last_run_file = "data/wnba_last_run.json"
        
    def _load_config(self, config_path: Optional[str]) -> Dict[str, Any]:
        """Load configuration from file or use defaults."""
        default_config = {
            "season_start": 2005,
            "season_end": 2024,
            "update_frequency_days": 7,
            "output_path": "data/moneyline_training_data_wnba.csv",
            "backup_enabled": True,
            "max_retries": 3,
            "timeout_minutes": 30
        }
        
        if config_path and os.path.exists(config_path):
            try:
                with open(config_path, 'r') as f:
                    file_config = json.load(f)
                default_config.update(file_config)
                logger.info(f"Loaded configuration from {config_path}")
            except Exception as e:
                logger.warning(f"Could not load config file {config_path}: {e}")
                
        return default_config
    
    def should_update_data(self) -> bool:
        """Check if data should be updated based on last run time."""
        if not os.path.exists(self.last_run_file):
            logger.info("No previous run found, data update needed")
            return True
            
        try:
            with open(self.last_run_file, 'r') as f:
                last_run_data = json.load(f)
            
            last_run = datetime.fromisoformat(last_run_data['timestamp'])
            days_since_update = (datetime.now() - last_run).days
            frequency = self.config['update_frequency_days']
            
            if days_since_update >= frequency:
                logger.info(f"Data is {days_since_update} days old, update needed")
                return True
            else:
                logger.info(f"Data is current ({days_since_update} days old)")
                return False
                
        except Exception as e:
            logger.warning(f"Could not check last run time: {e}")
            return True
    
    def record_successful_run(self, stats: Dict[str, Any]):
        """Record successful data collection run."""
        try:
            run_data = {
                "timestamp": datetime.now().isoformat(),
                "stats": stats,
                "config": self.config
            }
            
            os.makedirs(os.path.dirname(self.last_run_file), exist_ok=True)
            with open(self.last_run_file, 'w') as f:
                json.dump(run_data, f, indent=2)
                
            logger.info("Recorded successful run")
            
        except Exception as e:
            logger.error(f"Could not record run: {e}")
    
    async def run_data_collection(self, force_update: bool = False) -> bool:
        """Run the WNBA data collection with enhanced monitoring."""
        if not force_update and not self.should_update_data():
            logger.info("Data is current, skipping collection")
            return True
            
        logger.info("Starting WNBA data collection...")
        
        try:
            # Import the main collection function
            sys.path.append(os.path.dirname(os.path.abspath(__file__)))
            
            # Run the collection
            start_time = datetime.now()
            result_df = await fetch_games_and_features()
            end_time = datetime.now()
            
            if result_df is not None and validate_training_data(result_df):
                # Calculate statistics
                stats = {
                    "total_games": len(result_df),
                    "home_wins": int(result_df['home_win'].sum()),
                    "away_wins": int(len(result_df) - result_df['home_win'].sum()),
                    "home_win_pct": float(result_df['home_win'].mean()),
                    "date_range": {
                        "start": str(result_df['game_date'].min()),
                        "end": str(result_df['game_date'].max())
                    },
                    "execution_time_seconds": (end_time - start_time).total_seconds(),
                    "file_size_mb": os.path.getsize(self.config['output_path']) / (1024 * 1024)
                }
                
                # Record successful run
                self.record_successful_run(stats)
                
                logger.info("WNBA data collection completed successfully")
                logger.info(f"Statistics: {json.dumps(stats, indent=2)}")
                
                return True
            else:
                logger.error("Data collection failed validation")
                return False
                
        except Exception as e:
            logger.error(f"Data collection failed: {e}")
            return False
    
    def backup_existing_data(self):
        """Create backup of existing data file."""
        if not self.config.get('backup_enabled', True):
            return
            
        output_path = self.config['output_path']
        if os.path.exists(output_path):
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_path = f"{output_path}.backup_{timestamp}"
            
            try:
                shutil.copy2(output_path, backup_path)
                logger.info(f"Created backup: {backup_path}")
                
                # Clean up old backups (keep last 5)
                self._cleanup_old_backups(output_path)
                
            except Exception as e:
                logger.warning(f"Could not create backup: {e}")
    
    def _cleanup_old_backups(self, base_path: str, keep_count: int = 5):
        """Clean up old backup files."""
        try:
            backup_pattern = f"{base_path}.backup_*"
            backup_files = glob.glob(backup_pattern)
            
            if len(backup_files) > keep_count:
                # Sort by modification time and remove oldest
                backup_files.sort(key=os.path.getmtime)
                files_to_remove = backup_files[:-keep_count]
                
                for file_path in files_to_remove:
                    os.remove(file_path)
                    logger.info(f"Removed old backup: {file_path}")
                    
        except Exception as e:
            logger.warning(f"Could not cleanup old backups: {e}")
    
    def get_data_status(self) -> Dict[str, Any]:
        """Get current status of WNBA training data."""
        status = {
            "data_file_exists": os.path.exists(self.config['output_path']),
            "last_run": None,
            "file_size_mb": 0,
            "needs_update": self.should_update_data()
        }
        
        if status["data_file_exists"]:
            try:
                status["file_size_mb"] = os.path.getsize(self.config['output_path']) / (1024 * 1024)
                status["last_modified"] = datetime.fromtimestamp(
                    os.path.getmtime(self.config['output_path'])
                ).isoformat()
            except Exception as e:
                logger.warning(f"Could not get file stats: {e}")
        
        if os.path.exists(self.last_run_file):
            try:
                with open(self.last_run_file, 'r') as f:
                    status["last_run"] = json.load(f)
            except Exception as e:
                logger.warning(f"Could not read last run file: {e}")
        
        return status


async def main():
    """Main entry point for enhanced WNBA data pipeline."""
    
    parser = argparse.ArgumentParser(description="Enhanced WNBA Training Data Pipeline")
    parser.add_argument("--force", action="store_true", help="Force data update")
    parser.add_argument("--status", action="store_true", help="Show data status")
    parser.add_argument("--config", help="Configuration file path")
    
    args = parser.parse_args()
    
    # Initialize pipeline manager
    manager = WNBADataPipelineManager(args.config)
    
    if args.status:
        # Show status
        status = manager.get_data_status()
        return
    
    # Create backup before updating
    manager.backup_existing_data()
    
    # Run data collection
    success = await manager.run_data_collection(force_update=args.force)
    
    if success:
        logger.info("✅ WNBA data pipeline completed successfully")
        sys.exit(0)
    else:
        logger.error("❌ WNBA data pipeline failed")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
