#!/usr/bin/env python3
"""
🎯 DIRECT CHECKPOINT TEST
========================

Load models directly from checkpoints and test on real data.
"""

import pandas as pd
import numpy as np
import torch
import json
from datetime import datetime, timedelta
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

class DirectCheckpointTester:
    """Test models by loading checkpoints directly"""
    
    def __init__(self):
        """Initialize the tester"""
        print("🎯 DIRECT CHECKPOINT TESTER")
        print("=" * 35)
        print("🔧 Loading models directly from checkpoints")
        
    def load_test_data(self):
        """Load and prepare test data"""
        try:
            print("📊 Loading test data...")
            
            # Load the data
            df = pd.read_csv('data/master/wnba_definitive_master_dataset_FIXED.csv', low_memory=False)
            df['game_date'] = pd.to_datetime(df['game_date'], errors='coerce')
            
            # Filter last 7 days
            end_date = datetime(2025, 7, 12)
            start_date = end_date - timedelta(days=7)
            
            recent_games = df[
                (df['game_date'] >= start_date) & 
                (df['game_date'] <= end_date)
            ].copy()
            
            if len(recent_games) == 0:
                print("⚠️ No recent games, using latest games")
                recent_games = df.nlargest(50, 'game_date').copy()
            
            # Get target variable
            if 'points' in recent_games.columns:
                target_col = 'points'
            elif 'target' in recent_games.columns:
                target_col = 'target'
            else:
                print("❌ No target column found")
                return None
            
            # Remove rows with missing targets
            recent_games = recent_games.dropna(subset=[target_col])
            
            if len(recent_games) == 0:
                print("❌ No valid games after cleaning")
                return None
            
            print(f"✅ Found {len(recent_games)} valid games")
            
            # Get targets
            y = recent_games[target_col].values
            
            print(f"🎯 Samples: {len(y)}")
            print(f"⚖️ Avg points: {np.mean(y):.1f}")
            print(f"📊 Point range: {np.min(y):.1f} - {np.max(y):.1f}")
            
            return recent_games, y
            
        except Exception as e:
            print(f"❌ Error loading test data: {e}")
            return None
    
    def inspect_checkpoint(self, checkpoint_path):
        """Inspect a checkpoint to understand its structure"""
        try:
            checkpoint = torch.load(checkpoint_path, map_location='cpu')
            
            print(f"\n🔍 Inspecting checkpoint: {Path(checkpoint_path).name}")
            
            if 'state_dict' in checkpoint:
                state_dict = checkpoint['state_dict']
                
                # Find the first layer to get input dimension
                for key, tensor in state_dict.items():
                    if 'weight' in key and len(tensor.shape) == 2:
                        print(f"   Layer: {key}")
                        print(f"   Shape: {tensor.shape}")
                        print(f"   Input dim: {tensor.shape[1]}")
                        return tensor.shape[1]
            
            return None
            
        except Exception as e:
            print(f"❌ Error inspecting checkpoint: {e}")
            return None
    
    def create_minimal_model(self, input_dim, model_type='enhanced'):
        """Create a minimal model that matches the checkpoint"""
        
        class MinimalModel(torch.nn.Module):
            def __init__(self, input_dim):
                super().__init__()
                if model_type == 'enhanced':
                    # Match the enhanced model architecture
                    self.feature_net = torch.nn.Sequential(
                        torch.nn.Linear(input_dim, 512),
                        torch.nn.ReLU(),
                        torch.nn.Dropout(0.25),
                        torch.nn.Linear(512, 256),
                        torch.nn.ReLU(),
                        torch.nn.Dropout(0.25),
                        torch.nn.Linear(256, 128),
                        torch.nn.ReLU(),
                        torch.nn.Dropout(0.25),
                        torch.nn.Linear(128, 64),
                        torch.nn.ReLU(),
                        torch.nn.Dropout(0.25),
                        torch.nn.Linear(64, 1)
                    )
                elif model_type == 'multitask':
                    # Match the multitask model architecture
                    self.net = torch.nn.Sequential(
                        torch.nn.Linear(input_dim, 256),
                        torch.nn.ReLU(),
                        torch.nn.Dropout(0.25),
                        torch.nn.Linear(256, 128),
                        torch.nn.ReLU(),
                        torch.nn.Dropout(0.25),
                        torch.nn.Linear(128, 64),
                        torch.nn.ReLU(),
                        torch.nn.Dropout(0.25)
                    )
                    self.points_head = torch.nn.Linear(64, 1)
                    self.rebounds_head = torch.nn.Linear(64, 1)
                    self.assists_head = torch.nn.Linear(64, 1)
                    self.steals_head = torch.nn.Linear(64, 1)
                    self.blocks_head = torch.nn.Linear(64, 1)
                elif model_type == 'bayesian':
                    # Match the bayesian model architecture
                    self.net = torch.nn.Sequential(
                        torch.nn.Linear(input_dim, 256),
                        torch.nn.ReLU(),
                        torch.nn.Dropout(0.25),
                        torch.nn.Linear(256, 128),
                        torch.nn.ReLU(),
                        torch.nn.Dropout(0.25),
                        torch.nn.Linear(128, 1)
                    )
                    self.bayesian_net = torch.nn.Sequential(
                        torch.nn.Linear(input_dim, 512),
                        torch.nn.ReLU(),
                        torch.nn.Dropout(0.25),
                        torch.nn.Linear(512, 256),
                        torch.nn.ReLU(),
                        torch.nn.Dropout(0.25),
                        torch.nn.Linear(256, 1)
                    )
            
            def forward(self, x):
                if model_type == 'enhanced':
                    return self.feature_net(x)
                elif model_type == 'multitask':
                    features = self.net(x)
                    points = self.points_head(features)
                    return {'points': points}
                elif model_type == 'bayesian':
                    return self.net(x)
        
        return MinimalModel(input_dim)
    
    def test_model_with_checkpoint(self, checkpoint_path, model_type, X, y):
        """Test a model by loading its checkpoint"""
        try:
            print(f"\n🤖 Testing {model_type.upper()} model...")
            
            # Inspect checkpoint to get input dimension
            input_dim = self.inspect_checkpoint(checkpoint_path)
            if input_dim is None:
                print(f"   ❌ Could not determine input dimension")
                return None
            
            # Create minimal model
            model = self.create_minimal_model(input_dim, model_type)
            
            # Load checkpoint
            checkpoint = torch.load(checkpoint_path, map_location='cpu')
            model.load_state_dict(checkpoint['state_dict'])
            model.eval()
            
            print(f"   ✅ Model loaded with {input_dim} input features")
            
            # Prepare features to match input dimension
            if X.shape[1] != input_dim:
                if X.shape[1] > input_dim:
                    X_model = X[:, :input_dim]
                else:
                    padding = np.zeros((X.shape[0], input_dim - X.shape[1]))
                    X_model = np.concatenate([X, padding], axis=1)
            else:
                X_model = X
            
            # Get predictions
            predictions = []
            X_tensor = torch.FloatTensor(X_model)
            
            with torch.no_grad():
                for i in range(len(X_tensor)):
                    try:
                        output = model(X_tensor[i:i+1])
                        if isinstance(output, dict):
                            pred = float(output['points'].item())
                        else:
                            pred = float(output.item())
                        
                        # Ensure reasonable range
                        pred = max(0, min(50, pred))
                        predictions.append(pred)
                        
                    except Exception as e:
                        print(f"   ⚠️ Prediction error: {e}")
                        predictions.append(np.mean(y))
            
            # Calculate metrics
            predictions = np.array(predictions)
            errors = np.abs(predictions - y)
            
            mae = np.mean(errors)
            rmse = np.sqrt(np.mean((predictions - y) ** 2))
            
            print(f"   📊 Predictions: {len(predictions)}")
            print(f"   🎯 MAE: {mae:.3f} points")
            print(f"   📈 RMSE: {rmse:.3f} points")
            print(f"   ⚖️ Avg Actual: {np.mean(y):.1f} points")
            print(f"   🔮 Avg Predicted: {np.mean(predictions):.1f} points")
            
            # Show sample predictions
            print(f"   📝 Sample predictions:")
            for j in range(min(3, len(predictions))):
                print(f"      Game {j+1}: Predicted {predictions[j]:.1f}, Actual {y[j]:.1f} (Error: {errors[j]:.1f})")
            
            return {
                'mae': mae,
                'rmse': rmse,
                'predictions': predictions.tolist(),
                'actual': y.tolist()
            }
            
        except Exception as e:
            print(f"   ❌ Error testing {model_type}: {e}")
            return None
    
    def run_test(self):
        """Run the complete test"""
        print("🚀 Starting direct checkpoint test...")
        
        # Load test data
        test_data = self.load_test_data()
        if test_data is None:
            return False
        
        games_df, y = test_data
        
        # Create simple features (first 200 numeric columns)
        numeric_cols = []
        for col in games_df.columns:
            if col not in ['points', 'target', 'player_id', 'game_id', 'game_date', 'player_name', 'team', 'opponent', 'season']:
                try:
                    games_df[col] = pd.to_numeric(games_df[col], errors='coerce')
                    if games_df[col].dtype in ['float64', 'int64']:
                        numeric_cols.append(col)
                except:
                    continue
        
        # Use first 200 features and fill missing values
        feature_cols = numeric_cols[:200]
        X = games_df[feature_cols].fillna(0).values
        
        print(f"📊 Prepared {X.shape[1]} features for testing")
        
        # Load training results to get model paths
        try:
            results_path = Path("models/comprehensive_system/comprehensive_training_results.json")
            with open(results_path, 'r') as f:
                training_results = json.load(f)
        except Exception as e:
            print(f"❌ Error loading training results: {e}")
            return False
        
        # Test each model
        results = {}
        
        model_configs = [
            ('enhanced', 'enhanced_model'),
            ('multitask', 'multitask_model'),
            ('bayesian', 'bayesian_model')
        ]
        
        for model_type, result_key in model_configs:
            model_info = training_results.get('all_models', {}).get(result_key, {})
            model_path = model_info.get('best_model_path')
            
            if model_path and Path(model_path).exists():
                result = self.test_model_with_checkpoint(model_path, model_type, X, y)
                if result:
                    results[model_type] = result
        
        # Save results
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_data = {
            'timestamp': datetime.now().isoformat(),
            'test_period': 'July 5-12, 2025 (Direct Checkpoint)',
            'test_samples': len(y),
            'results': results
        }
        
        output_path = f"direct_checkpoint_test_results_{timestamp}.json"
        with open(output_path, 'w') as f:
            json.dump(output_data, f, indent=2)
        
        print(f"\n💾 Results saved to: {output_path}")
        
        # Summary
        print("\n🏆 FINAL RESULTS")
        print("=" * 25)
        
        if results:
            best_model = min(results.keys(), key=lambda k: results[k]['mae'])
            print(f"🥇 Best Model: {best_model.upper()} (MAE: {results[best_model]['mae']:.3f})")
            
            for name, result in sorted(results.items(), key=lambda x: x[1]['mae']):
                print(f"   {name.upper()}: {result['mae']:.3f} MAE")
            
            print(f"\n🎯 SUCCESS: Models tested on {len(y)} real WNBA games!")
        else:
            print("❌ No successful model tests")
        
        return len(results) > 0


def main():
    """Main function"""
    tester = DirectCheckpointTester()
    success = tester.run_test()
    
    if success:
        print("\n✅ Direct checkpoint testing completed successfully!")
    else:
        print("\n❌ Testing failed.")


if __name__ == "__main__":
    main()
