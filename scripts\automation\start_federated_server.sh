#!/bin/bash
# start_federated_server.sh
# Expert Federated Learning Server for WNBA Multi-Team Training
# 
# This script starts a robust federated learning server with:
# - Comprehensive error handling and logging
# - Resource monitoring and management
# - Production-ready configuration
# - Multi-team coordination

set -euo pipefail  # Exit on error, undefined vars, pipe failures

# =============================================================================
# CONFIGURATION
# =============================================================================

SERVER_SCRIPT="${FEDERATED_SERVER_SCRIPT:-federated_wnba_server.py}"
SERVER_ADDR="${FEDERATED_SERVER_ADDR:-localhost:8080}"
CONFIG_FILE="federated_config.json"
MIN_TEAMS="${MIN_TEAMS:-3}"
MAX_ROUNDS="${MAX_ROUNDS:-15}"
TIMEOUT_MINUTES="${TIMEOUT_MINUTES:-120}"

# Logging configuration
LOG_DIR="logs/federated"
SERVER_LOG="$LOG_DIR/server.log"
STDOUT_LOG="$LOG_DIR/server_stdout.log"
ERROR_LOG="$LOG_DIR/server_error.log"
PERFORMANCE_LOG="$LOG_DIR/server_performance.log"

# =============================================================================
# UTILITY FUNCTIONS
# =============================================================================

log_info() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] [INFO] [SERVER] $1" | tee -a "$SERVER_LOG"
}

log_error() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] [ERROR] [SERVER] $1" | tee -a "$SERVER_LOG" "$ERROR_LOG" >&2
}

cleanup() {
    log_info "Shutting down federated server..."
    pkill -f "federated.*server" 2>/dev/null || true
    log_info "Server shutdown complete"
}

check_prerequisites() {
    log_info "Checking server prerequisites..."
    
    if [[ ! -f "$SERVER_SCRIPT" ]]; then
        log_error "Server script not found: $SERVER_SCRIPT"
        return 1
    fi
    
    if [[ ! -f "$CONFIG_FILE" ]]; then
        log_error "Configuration file not found: $CONFIG_FILE"
        return 1
    fi
    
    if ! command -v python &> /dev/null; then
        log_error "Python not found in PATH"
        return 1
    fi
    
    log_info "Server prerequisites check passed"
    return 0
}

# =============================================================================
# MAIN EXECUTION
# =============================================================================

main() {
    trap cleanup EXIT INT TERM
    
    mkdir -p "$LOG_DIR"
    
    log_info "=========================================="
    log_info "Starting WNBA Federated Learning Server"
    log_info "Address: $SERVER_ADDR"
    log_info "Min Teams: $MIN_TEAMS"
    log_info "Max Rounds: $MAX_ROUNDS"
    log_info "=========================================="
    
    if ! check_prerequisites; then
        log_error "Server prerequisites check failed"
        exit 1
    fi
    
    log_info "Launching federated server..."
    
    timeout "${TIMEOUT_MINUTES}m" python "$SERVER_SCRIPT" \
        --server-addr "$SERVER_ADDR" \
        --min-teams "$MIN_TEAMS" \
        --max-rounds "$MAX_ROUNDS" \
        --config "$CONFIG_FILE" \
        --log-file "$SERVER_LOG" \
        --performance-log "$PERFORMANCE_LOG" \
        "$@" \
        2> >(tee -a "$ERROR_LOG" >&2) \
        1> >(tee -a "$STDOUT_LOG")
    
    log_info "Federated server completed"
}

if [[ "${1:-}" == "--help" ]] || [[ "${1:-}" == "-h" ]]; then
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Expert Federated Learning Server for WNBA"
    echo ""
    echo "Environment Variables:"
    echo "  FEDERATED_SERVER_SCRIPT  Server script path"
    echo "  FEDERATED_SERVER_ADDR    Server address"
    echo "  MIN_TEAMS               Minimum teams required"
    echo "  MAX_ROUNDS              Maximum training rounds"
    echo "  TIMEOUT_MINUTES         Server timeout"
    echo ""
    exit 0
fi

main "$@"
