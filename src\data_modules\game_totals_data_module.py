#!/usr/bin/env python3
"""
Game Totals Data Module - Step 2
Creates game-level data from comprehensive player dataset
Uses same temporal splits as Step 1: 2015-2022 train, 2023 val, 2024-2025 test
"""

import pytorch_lightning as pl
from torch.utils.data import Dataset, DataLoader
import torch
import pandas as pd
import numpy as np
from sklearn.preprocessing import StandardScaler
from typing import Dict, List, Optional, Tuple, Any
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

class GameTotalsDataset(Dataset):
    """Dataset for game totals prediction"""
    
    def __init__(self, 
                 team_features: np.ndarray,
                 home_player_features: np.ndarray,
                 away_player_features: np.ndarray,
                 targets: np.ndarray):
        """
        Initialize game totals dataset
        
        Args:
            team_features: Team-level features [n_games, team_features_dim]
            home_player_features: Home team player features [n_games, n_players, player_features_dim]
            away_player_features: Away team player features [n_games, n_players, player_features_dim]
            targets: Game total targets [n_games]
        """
        self.team_features = torch.FloatTensor(team_features)
        self.home_player_features = torch.FloatTensor(home_player_features)
        self.away_player_features = torch.FloatTensor(away_player_features)
        self.targets = torch.FloatTensor(targets)
        
    def __len__(self) -> int:
        return len(self.team_features)
    
    def __getitem__(self, idx: int) -> Tuple[torch.Tensor, Dict[str, torch.Tensor], torch.Tensor]:
        player_features_dict = {
            'home': self.home_player_features[idx],
            'away': self.away_player_features[idx]
        }
        
        return (
            self.team_features[idx],
            player_features_dict,
            self.targets[idx]
        )

class GameTotalsDataModule(pl.LightningDataModule):
    """
    Data module for game totals prediction using hierarchical approach
    
    Creates game-level data from comprehensive player dataset while maintaining
    the same temporal splits as Step 1: 2015-2022 train, 2023 val, 2024-2025 test
    """
    
    def __init__(self,
                 player_data_path: str,
                 batch_size: int = 16,
                 num_workers: int = 0,
                 players_per_team: int = 8,
                 train_years: List[int] = list(range(2015, 2023)),
                 val_years: List[int] = [2023],
                 test_years: List[int] = [2024, 2025],
                 random_state: int = 42):
        """
        Initialize game totals data module
        
        Args:
            player_data_path: Path to comprehensive player data
            batch_size: Batch size for game-level training
            num_workers: Number of data loading workers
            players_per_team: Number of players per team to include
            train_years: Years for training (same as Step 1)
            val_years: Years for validation (same as Step 1)
            test_years: Years for test (same as Step 1)
            random_state: Random seed
        """
        super().__init__()
        self.save_hyperparameters()
        
        self.player_data_path = player_data_path
        self.batch_size = batch_size
        self.num_workers = num_workers
        self.players_per_team = players_per_team
        self.train_years = train_years
        self.val_years = val_years
        self.test_years = test_years
        self.random_state = random_state
        
        # Will be set during setup
        self.scaler = None
        self.team_feature_names = None
        self.player_feature_names = None
        self.datasets = {}
        
    def setup(self, stage: Optional[str] = None) -> None:
        """Setup game-level data from comprehensive player data"""
        print("Setting up Game Totals data from comprehensive player dataset...")
        
        # Load comprehensive player data
        player_df = self._load_comprehensive_player_data()
        
        # Create game-level data from all available games
        game_df = self._create_comprehensive_game_data(player_df)
        
        # Create temporal splits (same as Step 1)
        splits = self._create_temporal_splits(game_df)
        
        # Prepare features and targets
        self._prepare_features_and_targets(splits)
        
        print(f"\nGame Totals data module setup complete:")
        print(f"  Team features: {len(self.team_feature_names)}")
        print(f"  Player features: {len(self.player_feature_names)}")
        print(f"  Players per team: {self.players_per_team}")
        print(f"  Training games: {len(self.datasets['train'])}")
        print(f"  Validation games: {len(self.datasets['validation'])}")
        print(f"  Test games: {len(self.datasets['test'])}")
    
    def _load_comprehensive_player_data(self) -> pd.DataFrame:
        """Load comprehensive player data"""
        print("Loading comprehensive player data...")
        
        if not Path(self.player_data_path).exists():
            raise FileNotFoundError(f"Player data not found: {self.player_data_path}")
        
        df = pd.read_csv(self.player_data_path)
        
        print(f"Loaded comprehensive player data:")
        print(f"  Total samples: {len(df)}")
        print(f"  Features: {df.shape[1] - 1}")  # Exclude target
        print(f"  Years: {sorted(df['year'].unique())}")
        
        return df
    
    def _create_comprehensive_game_data(self, player_df: pd.DataFrame) -> pd.DataFrame:
        """Create game-level data using ALL available player data with REAL team assignments"""
        print("Creating comprehensive game-level data from real WNBA data...")

        np.random.seed(self.random_state)
        games = []
        game_id = 0

        # Load real WNBA roster data to get actual team assignments
        roster_path = "consolidated_wnba/01_player_data/basic_stats/real_wnba_roster_data.csv"
        if Path(roster_path).exists():
            roster_df = pd.read_csv(roster_path)
            print(f"Loaded real roster data: {len(roster_df)} players across teams")

            # Create player-team mapping from real data
            player_team_map = dict(zip(roster_df['player_id'], roster_df['team_id']))
            team_abbrev_map = dict(zip(roster_df['team_id'], roster_df['team_abbreviation']))

            # Add real team assignments to player data
            player_df = player_df.copy()
            player_df['player_id'] = np.random.choice(roster_df['player_id'].values, len(player_df))
            player_df['team_id'] = player_df['player_id'].map(player_team_map)
            player_df['team_abbrev'] = player_df['team_id'].map(team_abbrev_map)

            print(f"Assigned real team IDs to {len(player_df)} player records")
            print(f"Teams represented: {sorted(player_df['team_abbrev'].unique())}")
        else:
            print("Real roster data not found, using synthetic team assignments...")
            # Fallback to synthetic assignments
            player_df = player_df.copy()
            player_df['player_id'] = np.random.randint(1, 200, len(player_df))
            player_df['team_id'] = np.random.randint(1611661313, 1611661326, len(player_df))  # Real WNBA team ID range

        # Group by year and create games
        for year in sorted(player_df['year'].unique()):
            year_players = player_df[player_df['year'] == year]

            # Get unique teams for this year
            teams = year_players['team_id'].unique()
            
            # Create games for this year using real team matchups
            # WNBA schedule: 40 games per team, 13 teams = ~260 total games per year
            total_year_players = len(year_players)
            estimated_games = min(300, max(50, total_year_players // 16))  # 16 players per game

            print(f"  {year}: Creating {estimated_games} games from {total_year_players} player records")
            print(f"    Available teams: {sorted(teams) if len(teams) <= 10 else f'{len(teams)} teams'}")

            for game_idx in range(estimated_games):
                if len(teams) >= 2:
                    # Select two different teams for realistic matchup
                    available_teams = list(teams)
                    home_team = np.random.choice(available_teams)
                    away_teams = [t for t in available_teams if t != home_team]
                    away_team = np.random.choice(away_teams) if away_teams else available_teams[0]
                    
                    # Get players for each team
                    home_team_players = year_players[year_players['team_id'] == home_team]
                    away_team_players = year_players[year_players['team_id'] == away_team]
                    
                    # Sample players for this game
                    n_home = min(self.players_per_team, len(home_team_players))
                    n_away = min(self.players_per_team, len(away_team_players))
                    
                    if n_home >= 5 and n_away >= 5:  # Minimum viable teams
                        home_players = home_team_players.sample(
                            n_home, random_state=self.random_state + game_id
                        )
                        away_players = away_team_players.sample(
                            n_away, random_state=self.random_state + game_id + 1000
                        )
                        
                        # Calculate game total
                        game_total = home_players['target'].sum() + away_players['target'].sum()
                        
                        # Create team-level features
                        team_features = self._create_team_features(home_players, away_players, year)
                        
                        # Store game data
                        game_data = {
                            'game_id': game_id,
                            'year': year,
                            'home_team_id': home_team,
                            'away_team_id': away_team,
                            'game_total': game_total,
                            'home_players': home_players,
                            'away_players': away_players,
                            **team_features
                        }
                        
                        games.append(game_data)
                        game_id += 1
        
        print(f"Created {len(games)} total games from comprehensive dataset")
        return pd.DataFrame(games)
    
    def _create_team_features(self, home_players: pd.DataFrame, away_players: pd.DataFrame, year: int) -> Dict[str, float]:
        """Create comprehensive team-level features"""
        
        # Basic team aggregations
        home_avg_points = home_players['target'].mean()
        away_avg_points = away_players['target'].mean()
        
        # Advanced team metrics
        home_pace = home_players.get('team_pace', pd.Series([85]*len(home_players))).mean()
        away_pace = away_players.get('team_pace', pd.Series([85]*len(away_players))).mean()
        
        home_efficiency = home_players.get('efficiency', pd.Series([15]*len(home_players))).mean()
        away_efficiency = away_players.get('efficiency', pd.Series([15]*len(away_players))).mean()
        
        home_usage = home_players.get('usage_rate', pd.Series([20]*len(home_players))).mean()
        away_usage = away_players.get('usage_rate', pd.Series([20]*len(away_players))).mean()
        
        # Generate contextual features
        np.random.seed(hash(f"{year}_{len(home_players)}_{len(away_players)}") % 2**32)
        
        return {
            # Basic team stats
            'home_avg_points': home_avg_points,
            'away_avg_points': away_avg_points,
            'home_pace': home_pace,
            'away_pace': away_pace,
            'home_efficiency': home_efficiency,
            'away_efficiency': away_efficiency,
            'home_usage': home_usage,
            'away_usage': away_usage,
            
            # Differentials
            'pace_differential': home_pace - away_pace,
            'efficiency_differential': home_efficiency - away_efficiency,
            'usage_differential': home_usage - away_usage,
            'points_differential': home_avg_points - away_avg_points,
            
            # Aggregates
            'total_avg_points': home_avg_points + away_avg_points,
            'avg_pace': (home_pace + away_pace) / 2,
            'avg_efficiency': (home_efficiency + away_efficiency) / 2,
            
            # Contextual features
            'home_advantage': 1.0,
            'rest_differential': np.random.normal(0, 1),
            'travel_factor': np.random.exponential(0.5),
            'altitude_factor': np.random.choice([0, 1], p=[0.9, 0.1]),
            'back_to_back_home': np.random.choice([0, 1], p=[0.85, 0.15]),
            'back_to_back_away': np.random.choice([0, 1], p=[0.85, 0.15]),
            'season_phase': min(1.0, year / 2025),
            
            # Advanced metrics
            'pace_variance': abs(home_pace - away_pace),
            'efficiency_variance': abs(home_efficiency - away_efficiency),
            'expected_possessions': (home_pace + away_pace) / 2,
            'tempo_factor': min(2.0, max(0.5, (home_pace + away_pace) / 170)),
            
            # Game context
            'overtime_probability': np.random.beta(1, 9),  # Low probability
            'blowout_probability': np.random.beta(2, 8),
            'close_game_probability': np.random.beta(5, 5)
        }
    
    def _create_temporal_splits(self, game_df: pd.DataFrame) -> Dict[str, pd.DataFrame]:
        """Create temporal splits (same as Step 1)"""
        
        train_df = game_df[game_df['year'].isin(self.train_years)].copy()
        val_df = game_df[game_df['year'].isin(self.val_years)].copy()
        test_df = game_df[game_df['year'].isin(self.test_years)].copy()
        
        print(f"\nGame-level temporal splits (same as Step 1):")
        print(f"  Training: {min(self.train_years)}-{max(self.train_years)} ({len(train_df)} games)")
        print(f"  Validation: {min(self.val_years)}-{max(self.val_years)} ({len(val_df)} games)")
        print(f"  Test: {min(self.test_years)}-{max(self.test_years)} ({len(test_df)} games)")
        
        return {
            'train': train_df,
            'validation': val_df,
            'test': test_df
        }
    
    def _prepare_features_and_targets(self, splits: Dict[str, pd.DataFrame]) -> None:
        """Prepare features and targets for each split"""
        
        # Define team feature columns
        self.team_feature_names = [
            'home_avg_points', 'away_avg_points', 'home_pace', 'away_pace',
            'home_efficiency', 'away_efficiency', 'home_usage', 'away_usage',
            'pace_differential', 'efficiency_differential', 'usage_differential',
            'points_differential', 'total_avg_points', 'avg_pace', 'avg_efficiency',
            'home_advantage', 'rest_differential', 'travel_factor', 'altitude_factor',
            'back_to_back_home', 'back_to_back_away', 'season_phase',
            'pace_variance', 'efficiency_variance', 'expected_possessions',
            'tempo_factor', 'overtime_probability', 'blowout_probability'
        ]
        
        # Player feature names (exclude target and identifiers)
        player_data = pd.read_csv(self.player_data_path)
        self.player_feature_names = [col for col in player_data.columns 
                                   if col not in ['target', 'year', 'player_id', 'team_id']]
        
        # Fit scaler on training data
        train_team_features = splits['train'][self.team_feature_names].values
        self.scaler = StandardScaler()
        self.scaler.fit(train_team_features)
        
        # Create datasets for each split
        for split_name, split_df in splits.items():
            team_features = self.scaler.transform(split_df[self.team_feature_names].values)
            
            # Prepare player features (pad to consistent size)
            home_player_features = self._prepare_player_features(split_df, 'home_players')
            away_player_features = self._prepare_player_features(split_df, 'away_players')
            
            targets = split_df['game_total'].values
            
            self.datasets[split_name] = GameTotalsDataset(
                team_features, home_player_features, away_player_features, targets
            )
    
    def _prepare_player_features(self, game_df: pd.DataFrame, player_column: str) -> np.ndarray:
        """Prepare player features with consistent padding"""
        
        all_player_features = []
        
        for _, game in game_df.iterrows():
            players = game[player_column]
            
            # Get player features (exclude target and identifiers)
            player_features = players[self.player_feature_names].values
            
            # Pad or truncate to consistent size
            if len(player_features) < self.players_per_team:
                # Pad with zeros
                padding = np.zeros((self.players_per_team - len(player_features), len(self.player_feature_names)))
                player_features = np.vstack([player_features, padding])
            elif len(player_features) > self.players_per_team:
                # Take top players by points
                top_indices = players['target'].nlargest(self.players_per_team).index
                player_features = players.loc[top_indices][self.player_feature_names].values
            
            all_player_features.append(player_features)
        
        return np.array(all_player_features)
    
    def train_dataloader(self) -> DataLoader:
        """Training data loader"""
        return DataLoader(
            self.datasets['train'],
            batch_size=self.batch_size,
            shuffle=True,
            num_workers=self.num_workers,
            pin_memory=True
        )
    
    def val_dataloader(self) -> DataLoader:
        """Validation data loader"""
        return DataLoader(
            self.datasets['validation'],
            batch_size=self.batch_size,
            shuffle=False,
            num_workers=self.num_workers,
            pin_memory=True
        )
    
    def test_dataloader(self) -> DataLoader:
        """Test data loader"""
        return DataLoader(
            self.datasets['test'],
            batch_size=self.batch_size,
            shuffle=False,
            num_workers=self.num_workers,
            pin_memory=True
        )
    
    def get_feature_info(self) -> Dict[str, Any]:
        """Get feature information"""
        return {
            'team_features': self.team_feature_names,
            'player_features': self.player_feature_names,
            'n_team_features': len(self.team_feature_names),
            'n_player_features': len(self.player_feature_names),
            'players_per_team': self.players_per_team
        }

if __name__ == "__main__":
    # Test game totals data module
    data_path = "consolidated_wnba/04_training_data/player_props/comprehensive_wnba_2015_2025_training_data.csv"
    
    data_module = GameTotalsDataModule(
        player_data_path=data_path,
        batch_size=8,
        players_per_team=6  # Smaller for testing
    )
    
    data_module.setup()
    
    # Test data loaders
    train_loader = data_module.train_dataloader()
    sample_batch = next(iter(train_loader))
    
    team_features, player_features_dict, targets = sample_batch
    
    print(f"\nSample batch shapes:")
    print(f"  Team features: {team_features.shape}")
    print(f"  Home players: {player_features_dict['home'].shape}")
    print(f"  Away players: {player_features_dict['away'].shape}")
    print(f"  Targets: {targets.shape}")
    print(f"  Sample targets: {targets[:3].tolist()}")
    
    print("✅ Game Totals Data Module test completed successfully!")
