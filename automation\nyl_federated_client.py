#!/usr/bin/env python3
"""
🏀 NYL FEDERATED MULTIVERSE CLIENT
===================================

Federated learning client for NYL team.
Trains multiverse ensemble models on private team data.

Usage: python nyl_federated_client.py
"""

import sys
from pathlib import Path

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))

from federated_multiverse_integration import FederatedMultiverseClient
import flwr as fl
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def main():
    """Launch NYL federated multiverse client"""

    logger.info(f"🏀 Starting NYL Federated Multiverse Client")

    # Initialize client
    client = FederatedMultiverseClient(
        team_id="NYL",
        data_path="data/teams/nyl_data.csv"  # Team-specific data path
    )

    # Connect to federated server
    try:
        fl.client.start_numpy_client(
            server_address="localhost:8080",  # Adjust server address as needed
            client=client
        )
    except Exception as e:
        logger.error(f"❌ Failed to connect to federated server: {e}")
        return False

    logger.info(f"✅ NYL client session completed")
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        exit(1)
