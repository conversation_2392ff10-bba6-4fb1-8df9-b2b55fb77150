#!/usr/bin/env python3
"""
Quick validation script for production model setup
"""

import pandas as pd
from pathlib import Path
import json

def validate_production_setup():
    """Validate all components are ready for production training"""
    
    print("🏀 EXPERT PRODUCTION MODEL 1 VALIDATION")
    print("=" * 60)
    
    issues = []
    
    # 1. Check master dataset
    master_path = Path("data/master/wnba_definitive_master_dataset_FIXED.csv")
    if master_path.exists():
        file_size = master_path.stat().st_size / (1024 * 1024)
        print(f"✅ Master dataset found: {file_size:.1f} MB")
        
        # Check structure with small sample
        df_sample = pd.read_csv(master_path, nrows=10)
        print(f"📊 Dataset shape (sample): {df_sample.shape}")
        print(f"🔧 Total features: {len(df_sample.columns)}")
        
        # Check required columns
        required_cols = ['target', 'year', 'player_name', 'team_abbrev']
        missing_cols = [col for col in required_cols if col not in df_sample.columns]
        if missing_cols:
            issues.append(f"Missing required columns: {missing_cols}")
        else:
            print("✅ All required columns present")
            
        # Check year range
        if 'year' in df_sample.columns:
            print(f"📅 Year range (sample): {df_sample['year'].min()}-{df_sample['year'].max()}")
    else:
        issues.append("Master dataset not found")
    
    # 2. Check player mappings
    player_mapping_path = Path("consolidated_wnba/mappings/real_player_mappings.json")
    if player_mapping_path.exists():
        with open(player_mapping_path, 'r') as f:
            player_mappings = json.load(f)
        print(f"✅ Player mappings: {len(player_mappings):,} players")
    else:
        issues.append("Player mappings not found")
    
    # 3. Check team mappings
    team_mapping_path = Path("consolidated_wnba/mappings/real_team_mappings.json")
    if team_mapping_path.exists():
        with open(team_mapping_path, 'r') as f:
            team_mappings = json.load(f)
        print(f"✅ Team mappings: {len(team_mappings)} teams")
    else:
        issues.append("Team mappings not found")
    
    # 4. Check arena data
    arena_path = Path("data/master/wnba_stadium_locations.csv")
    if arena_path.exists():
        arena_df = pd.read_csv(arena_path)
        print(f"✅ Arena data: {len(arena_df)} venues")
        print(f"🏟️ Teams with arenas: {list(arena_df['team_abbrev'].values)}")
    else:
        issues.append("Arena data not found")
    
    # 5. Check model file
    model_path = Path("src/models/modern_player_points_model.py")
    if model_path.exists():
        print("✅ Model file found")
    else:
        issues.append("Model file not found")
    
    # 6. Check training script
    training_path = Path("train_production_model_1.py")
    if training_path.exists():
        print("✅ Training script found")
    else:
        issues.append("Training script not found")
    
    print("\n" + "=" * 60)
    
    if issues:
        print("❌ ISSUES FOUND:")
        for issue in issues:
            print(f"   - {issue}")
        return False
    else:
        print("✅ ALL COMPONENTS VALIDATED!")
        print("🚀 READY FOR EXPERT PRODUCTION TRAINING")
        print("\nNext steps:")
        print("1. Run: python train_production_model_1.py")
        print("2. Monitor training progress")
        print("3. Validate model performance")
        return True

if __name__ == "__main__":
    validate_production_setup()
