<!DOCTYPE html>
<html>
<head>
    <title>Dashboard Test</title>
</head>
<body>
    <h1>Dashboard API Test</h1>
    <div id="test-output"></div>
    <button onclick="testAPI()">Test API</button>
    
    <script>
        console.log('Test page loaded');
        
        function testAPI() {
            console.log('Testing API...');
            const output = document.getElementById('test-output');
            output.innerHTML = 'Testing API...';
            
            fetch('/api/dashboard')
                .then(response => {
                    console.log('Response status:', response.status);
                    output.innerHTML += '<br>Response status: ' + response.status;
                    return response.json();
                })
                .then(data => {
                    console.log('Data received:', data);
                    output.innerHTML += '<br><strong>Data Keys:</strong> ' + Object.keys(data).join(', ');
                    output.innerHTML += '<br><strong>Has live_games:</strong> ' + (data.live_games ? 'YES (' + data.live_games.length + ' games)' : 'NO');
                    output.innerHTML += '<br><strong>Has top_performers:</strong> ' + (data.top_performers ? 'YES (' + data.top_performers.length + ' players)' : 'NO');
                    output.innerHTML += '<br><strong>Has summary:</strong> ' + (data.summary ? 'YES' : 'NO');
                    output.innerHTML += '<br><strong>Timestamp:</strong> ' + data.timestamp;
                })
                .catch(error => {
                    console.error('Error:', error);
                    output.innerHTML += '<br>Error: ' + error.message;
                });
        }
        
        // Auto-test on load
        window.onload = function() {
            console.log('Auto-testing API...');
            testAPI();
        };
    </script>
</body>
</html>
