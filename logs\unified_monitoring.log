2025-07-11 21:04:00,490 - src.monitoring.unified_monitoring_dashboard - ERROR - Error getting fan_engagement status: Sample larger than population or is negative
2025-07-12 21:44:04,650 - __main__ - ERROR - Error checking federated multiverse status: SystemStatus.__init__() got an unexpected keyword argument 'last_updated'. Did you mean 'last_update'?
2025-07-12 21:54:15,467 - __main__ - ERROR - Error getting betting_optimization status: Sample larger than population or is negative
2025-07-12 21:54:15,477 - __main__ - ERROR - Error checking line movement watchdog status: 'charmap' codec can't decode byte 0x90 in position 3479: character maps to <undefined>
2025-07-12 21:54:15,481 - __main__ - ERROR - Error checking medusa autopilot status: 'charmap' codec can't decode byte 0x90 in position 3479: character maps to <undefined>
2025-07-12 21:54:15,483 - __main__ - ERROR - Error checking drift detection status: 'charmap' codec can't decode byte 0x81 in position 1868: character maps to <undefined>
2025-07-12 21:54:15,486 - __main__ - ERROR - Error checking hybrid prediction status: 'charmap' codec can't decode byte 0x9d in position 3588: character maps to <undefined>
2025-07-12 21:58:26,167 - __main__ - ERROR - Error checking line movement watchdog status: 'charmap' codec can't decode byte 0x90 in position 3479: character maps to <undefined>
2025-07-12 21:58:26,170 - __main__ - ERROR - Error checking medusa autopilot status: 'charmap' codec can't decode byte 0x90 in position 3479: character maps to <undefined>
2025-07-12 21:58:26,173 - __main__ - ERROR - Error checking drift detection status: 'charmap' codec can't decode byte 0x81 in position 1868: character maps to <undefined>
2025-07-12 21:58:26,176 - __main__ - ERROR - Error checking hybrid prediction status: 'charmap' codec can't decode byte 0x9d in position 3588: character maps to <undefined>
2025-07-12 21:59:12,567 - __main__ - ERROR - Error checking line movement watchdog status: 'charmap' codec can't decode byte 0x90 in position 3479: character maps to <undefined>
2025-07-12 21:59:12,574 - __main__ - ERROR - Error checking medusa autopilot status: 'charmap' codec can't decode byte 0x90 in position 3479: character maps to <undefined>
2025-07-12 21:59:12,581 - __main__ - ERROR - Error checking drift detection status: 'charmap' codec can't decode byte 0x81 in position 1868: character maps to <undefined>
2025-07-12 21:59:12,586 - __main__ - ERROR - Error checking hybrid prediction status: 'charmap' codec can't decode byte 0x9d in position 3588: character maps to <undefined>
