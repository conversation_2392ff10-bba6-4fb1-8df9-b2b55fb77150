#!/usr/bin/env python3
"""
WNBA Priority Training - Train WNBA models first for active season
"""

import sys
from pathlib import Path

# Add src to path
sys.path.append(str(Path(__file__).parent / "src"))

from src.neural_cortex.neural_training_pipeline import (
    NeuralTrainingPipeline, 
    create_training_config,
    logger
)

def main():
    """Train WNBA models first - active season priority"""
    logger.info("🏀 MEDUSA VAULT: WNBA PRIORITY TRAINING - ACTIVE SEASON")
    logger.info("🔥 WNBA is in active season - training first for live predictions!")
    logger.info("=" * 70)
    
    # WNBA Training Configuration - Enhanced for 89k+ records
    wnba_config = create_training_config(
        league="WNBA",
        batch_size=64,
        learning_rate=0.001,
        num_epochs=50,
        hidden_dim=512,
        use_data_augmentation=True,
        mixed_precision=True,
        model_save_path="./models/neural_core/wnba"
    )
    
    logger.info("🏀 Starting WNBA Neural Training Pipeline...")
    logger.info("📊 Expected data: 89,046 clean WNBA records")
    logger.info("🎯 Target: Real-time predictions for active WNBA season")
    logger.info("-" * 50)
    
    # Initialize WNBA training pipeline
    wnba_pipeline = NeuralTrainingPipeline(wnba_config)
    
    # Start WNBA training
    logger.info("🚀 LAUNCHING WNBA TRAINING...")
    wnba_results = wnba_pipeline.train()
    
    # Report WNBA results
    logger.info("✅ WNBA TRAINING COMPLETED!")
    logger.info(f"🎯 Best validation loss: {wnba_results['best_val_loss']:.4f}")
    logger.info(f"🎯 Test accuracy: {wnba_results['test_metrics']['accuracy']:.3f}")
    logger.info("🏀 WNBA MODEL READY FOR ACTIVE SEASON PREDICTIONS!")
    
    print("\n" + "="*60)
    print("🎉 WNBA NEURAL TRAINING COMPLETE!")
    print("👑 THE QUEEN IS READY FOR WNBA PREDICTIONS!")
    print("🔥 Active season model trained and operational!")
    print("="*60)

if __name__ == "__main__":
    main()
