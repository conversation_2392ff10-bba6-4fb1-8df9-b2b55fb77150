#!/bin/bash
# start_multiple_clients.sh
# Expert Multi-Client Federated Learning Launcher
# 
# Starts all 13 WNBA team clients simultaneously with:
# - Robust error handling and monitoring
# - Resource management and limits
# - Comprehensive logging and status tracking

set -euo pipefail

# =============================================================================
# CONFIGURATION
# =============================================================================

TEAMS=("ATL" "CHI" "CON" "DAL" "GSV" "IND" "LAS" "LV" "MIN" "NYL" "PHO" "SEA" "WAS")
LOG_DIR="logs/federated"
MASTER_LOG="$LOG_DIR/multi_client_master.log"
MAX_PARALLEL="${MAX_PARALLEL_CLIENTS:-13}"
STARTUP_DELAY="${CLIENT_STARTUP_DELAY:-2}"

# =============================================================================
# UTILITY FUNCTIONS
# =============================================================================

log_info() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] [MASTER] $1" | tee -a "$MASTER_LOG"
}

log_error() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] [ERROR] [MASTER] $1" | tee -a "$MASTER_LOG" >&2
}

cleanup() {
    log_info "Stopping all federated clients..."
    for team in "${TEAMS[@]}"; do
        pkill -f "start_client_${team,,}" 2>/dev/null || true
    done
    log_info "All clients stopped"
}

# =============================================================================
# MAIN EXECUTION
# =============================================================================

main() {
    trap cleanup EXIT INT TERM
    
    mkdir -p "$LOG_DIR"
    
    log_info "=========================================="
    log_info "Starting All WNBA Federated Clients"
    log_info "Teams: ${#TEAMS[@]}"
    log_info "Max Parallel: $MAX_PARALLEL"
    log_info "=========================================="
    
    local pids=()
    local started=0
    
    for team in "${TEAMS[@]}"; do
        local script="start_client_${team,,}.sh"
        
        if [[ ! -f "$script" ]]; then
            log_error "Client script not found: $script"
            continue
        fi
        
        log_info "Starting client for team: $team"
        
        bash "$script" &
        local pid=$!
        pids+=($pid)
        
        log_info "Started $team client with PID: $pid"
        
        ((started++))
        
        # Stagger client startups to avoid overwhelming the server
        if (( started % MAX_PARALLEL == 0 )); then
            log_info "Waiting for batch to stabilize..."
            sleep $STARTUP_DELAY
        fi
    done
    
    log_info "All $started clients started, waiting for completion..."
    
    # Wait for all clients and track results
    local success_count=0
    local failure_count=0
    
    for i in "${!pids[@]}"; do
        local pid=${pids[$i]}
        local team=${TEAMS[$i]}
        
        if wait "$pid"; then
            log_info "Client $team completed successfully"
            ((success_count++))
        else
            log_error "Client $team failed"
            ((failure_count++))
        fi
    done
    
    log_info "=========================================="
    log_info "Multi-client execution complete"
    log_info "Successful: $success_count"
    log_info "Failed: $failure_count"
    log_info "Total: $((success_count + failure_count))"
    log_info "=========================================="
    
    if (( failure_count > 0 )); then
        exit 1
    fi
}

if [[ "${1:-}" == "--help" ]] || [[ "${1:-}" == "-h" ]]; then
    echo "Usage: $0"
    echo ""
    echo "Expert Multi-Client Federated Learning Launcher"
    echo ""
    echo "Environment Variables:"
    echo "  MAX_PARALLEL_CLIENTS     Maximum parallel clients"
    echo "  CLIENT_STARTUP_DELAY     Delay between client batches"
    echo ""
    exit 0
fi

main "$@"
