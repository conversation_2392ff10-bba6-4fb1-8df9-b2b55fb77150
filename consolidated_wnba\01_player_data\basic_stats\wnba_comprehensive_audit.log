2025-07-05 07:41:36,199 - INFO - ================================================================================
2025-07-05 07:41:37,530 - INFO -  NBA API Endpoints loaded successfully (class import fix).
2025-07-05 07:41:37,608 - INFO - --- Initializing HYPER MEDUSA NEURAL VAULT Data Connector ---
2025-07-05 07:41:37,608 - INFO - BasketballDataLoader initialized with NBA API connector.
2025-07-05 07:45:24,683 - INFO - STARTING COMPREHENSIVE FEATURE AUDIT TRAINING
2025-07-05 07:45:24,684 - INFO - Implementing ALL user recommendations
2025-07-05 07:45:24,684 - INFO - ================================================================================
2025-07-05 07:45:25,464 - INFO -  NBA API Endpoints loaded successfully (class import fix).
2025-07-05 07:45:25,501 - INFO - RECOMMENDATION 2: Using database source for data consistency
2025-07-05 07:45:25,503 - INFO - --- Initializing HYPER MEDUSA NEURAL VAULT Data Connector ---
2025-07-05 07:45:25,503 - INFO - BasketballDataLoader initialized with NBA API connector.
2025-07-05 07:45:25,888 - INFO -    games (schedule): 135,816 records
2025-07-05 07:45:25,888 - INFO -    player_game_stats (performance): 26,213 records
2025-07-05 07:45:26,125 - INFO - Raw database data: (15417, 23)
2025-07-05 07:45:26,126 - INFO - RECOMMENDATION 1: Comprehensive Feature Audit
2025-07-05 07:45:26,127 - INFO - COMPREHENSIVE FEATURE AUDIT STARTING
2025-07-05 07:45:26,128 - INFO - ============================================================
2025-07-05 07:45:26,129 - INFO - Original data: (15417, 23)
2025-07-05 07:45:26,142 - INFO - STEP 2: Removing 6 categorical features: ['rank_position', 'engineered_feature_15', 'engineered_feature_16', 'engineered_feature_17', 'engineered_feature_18']...
2025-07-05 07:45:26,153 - INFO - After categorical removal: (15417, 17)
2025-07-05 07:45:26,183 - INFO - STEP 4: Removing 7 low-variance features
2025-07-05 07:45:26,186 - INFO - ============================================================
2025-07-05 07:45:26,187 - INFO - FEATURE AUDIT COMPLETE: (15417, 23) -> (15417, 10)
2025-07-05 07:45:26,188 - INFO - Final features: ['stat_value', 'season_encoded', 'high_performer', 'top_10_rank', 'above_average_performer', 'stat_value_normalized', 'stat_value_log', 'stat_value_squared', 'stat_value_percentile', 'win_prediction']
2025-07-05 07:45:26,189 - INFO - ============================================================
2025-07-05 07:45:26,189 - INFO - RECOMMENDATION 4: Ensuring target quality
2025-07-05 07:45:26,201 - INFO - Target quality check: {np.int64(0): np.int64(7710), np.int64(1): np.int64(7707)}
2025-07-05 07:45:26,203 - INFO - Good class balance: 0.500
2025-07-05 07:45:26,219 - INFO - Train: 10791 samples, {np.int64(0): np.int64(5397), np.int64(1): np.int64(5394)}
2025-07-05 07:45:26,220 - INFO - Val: 2313 samples, {np.int64(0): np.int64(1156), np.int64(1): np.int64(1157)}
2025-07-05 07:45:26,222 - INFO - Test: 2313 samples, {np.int64(0): np.int64(1157), np.int64(1): np.int64(1156)}
2025-07-05 07:45:26,249 - INFO - RECOMMENDATION 3: Optimizing model complexity for feature count
2025-07-05 07:45:26,277 - INFO - Optimized model: 9 features -> 425 parameters
2025-07-05 07:45:26,278 - INFO - Feature-to-parameter ratio: 1:47
2025-07-05 07:45:29,536 - INFO - Starting optimized training...
2025-07-05 07:45:29,662 - INFO - Epoch  1: Train Loss: 1.1396, Val Loss: 1.3025, Train Acc: 0.465, Val Acc: 0.500
2025-07-05 07:45:29,722 - INFO - Epoch  2: Train Loss: 1.0837, Val Loss: 1.1588, Train Acc: 0.504, Val Acc: 0.500
2025-07-05 07:45:29,764 - INFO - Epoch  3: Train Loss: 1.0889, Val Loss: 1.0552, Train Acc: 0.513, Val Acc: 0.500
2025-07-05 07:45:29,802 - INFO - Epoch  4: Train Loss: 1.0749, Val Loss: 0.9783, Train Acc: 0.515, Val Acc: 0.500
2025-07-05 07:45:29,837 - INFO - Epoch  5: Train Loss: 1.0504, Val Loss: 0.9200, Train Acc: 0.525, Val Acc: 0.500
2025-07-05 07:45:29,865 - INFO - Epoch  6: Train Loss: 1.0434, Val Loss: 0.8727, Train Acc: 0.524, Val Acc: 0.500
2025-07-05 07:45:29,901 - INFO - Epoch  7: Train Loss: 1.0357, Val Loss: 0.8343, Train Acc: 0.531, Val Acc: 0.500
2025-07-05 07:45:29,942 - INFO - Epoch  8: Train Loss: 1.0057, Val Loss: 0.8016, Train Acc: 0.529, Val Acc: 0.500
2025-07-05 07:45:29,969 - INFO - Epoch  9: Train Loss: 0.9944, Val Loss: 0.7748, Train Acc: 0.540, Val Acc: 0.500
2025-07-05 07:45:30,004 - INFO - Epoch 10: Train Loss: 0.9815, Val Loss: 0.7523, Train Acc: 0.541, Val Acc: 0.500
2025-07-05 07:45:30,035 - INFO - Epoch 11: Train Loss: 0.9639, Val Loss: 0.7323, Train Acc: 0.543, Val Acc: 0.500
2025-07-05 07:45:30,066 - INFO - Epoch 12: Train Loss: 0.9424, Val Loss: 0.7145, Train Acc: 0.559, Val Acc: 0.500
2025-07-05 07:45:30,100 - INFO - Epoch 13: Train Loss: 0.9264, Val Loss: 0.6978, Train Acc: 0.563, Val Acc: 0.500
2025-07-05 07:45:30,135 - INFO - Epoch 14: Train Loss: 0.9126, Val Loss: 0.6819, Train Acc: 0.562, Val Acc: 0.500
2025-07-05 07:45:30,176 - INFO - Epoch 15: Train Loss: 0.8998, Val Loss: 0.6679, Train Acc: 0.565, Val Acc: 0.501
2025-07-05 07:45:30,205 - INFO - Epoch 16: Train Loss: 0.9021, Val Loss: 0.6566, Train Acc: 0.564, Val Acc: 0.505
2025-07-05 07:45:30,232 - INFO - Epoch 17: Train Loss: 0.8667, Val Loss: 0.6453, Train Acc: 0.567, Val Acc: 0.508
2025-07-05 07:45:30,255 - INFO - Epoch 18: Train Loss: 0.8706, Val Loss: 0.6353, Train Acc: 0.577, Val Acc: 0.510
2025-07-05 07:45:30,278 - INFO - Epoch 19: Train Loss: 0.8601, Val Loss: 0.6250, Train Acc: 0.576, Val Acc: 0.511
2025-07-05 07:45:30,340 - INFO - Epoch 20: Train Loss: 0.8509, Val Loss: 0.6151, Train Acc: 0.584, Val Acc: 0.511
2025-07-05 07:45:30,366 - INFO - Epoch 21: Train Loss: 0.8451, Val Loss: 0.6060, Train Acc: 0.585, Val Acc: 0.514
2025-07-05 07:45:30,396 - INFO - Epoch 22: Train Loss: 0.8392, Val Loss: 0.5968, Train Acc: 0.590, Val Acc: 0.514
2025-07-05 07:45:30,428 - INFO - Epoch 23: Train Loss: 0.8410, Val Loss: 0.5880, Train Acc: 0.595, Val Acc: 0.514
2025-07-05 07:45:30,463 - INFO - Epoch 24: Train Loss: 0.8157, Val Loss: 0.5797, Train Acc: 0.600, Val Acc: 0.514
2025-07-05 07:45:30,495 - INFO - Epoch 25: Train Loss: 0.8236, Val Loss: 0.5718, Train Acc: 0.602, Val Acc: 0.895
2025-07-05 07:45:30,532 - INFO - Epoch 26: Train Loss: 0.8044, Val Loss: 0.5659, Train Acc: 0.605, Val Acc: 0.896
2025-07-05 07:45:30,569 - INFO - Epoch 27: Train Loss: 0.7967, Val Loss: 0.5601, Train Acc: 0.612, Val Acc: 0.896
2025-07-05 07:45:30,602 - INFO - Epoch 28: Train Loss: 0.7924, Val Loss: 0.5543, Train Acc: 0.614, Val Acc: 0.896
2025-07-05 07:45:30,625 - INFO - Epoch 29: Train Loss: 0.7751, Val Loss: 0.5489, Train Acc: 0.607, Val Acc: 0.896
2025-07-05 07:45:30,659 - INFO - Epoch 30: Train Loss: 0.7805, Val Loss: 0.5430, Train Acc: 0.622, Val Acc: 0.896
2025-07-05 07:45:30,662 - INFO - RECOMMENDATION 5: Comprehensive validation with confusion matrix
2025-07-05 07:45:30,702 - INFO - ================================================================================
2025-07-05 07:45:30,702 - INFO - COMPREHENSIVE AUDIT RESULTS
2025-07-05 07:45:30,702 - INFO - ================================================================================
2025-07-05 07:45:30,703 - INFO - Test Accuracy: 0.9014
2025-07-05 07:45:30,703 - INFO - 
Classification Report:
2025-07-05 07:45:30,715 - INFO -               precision    recall  f1-score   support

           0       0.84      1.00      0.91      1157
           1       1.00      0.80      0.89      1156

    accuracy                           0.90      2313
   macro avg       0.92      0.90      0.90      2313
weighted avg       0.92      0.90      0.90      2313

2025-07-05 07:45:30,721 - INFO - 
Confusion Matrix:
2025-07-05 07:45:30,721 - INFO - True\Pred    0    1
2025-07-05 07:45:30,722 - INFO - 0         1157    0
2025-07-05 07:45:30,723 - INFO - 1          228  928
2025-07-05 07:45:30,729 - INFO - 
Per-Class Metrics:
2025-07-05 07:45:30,730 - INFO - Class 0: Precision=0.835, Recall=1.000, F1=0.910
2025-07-05 07:45:30,731 - INFO - Class 1: Precision=1.000, Recall=0.803, F1=0.891
2025-07-05 07:45:30,731 - INFO - ================================================================================
2025-07-05 07:45:30,732 - INFO - ALL USER RECOMMENDATIONS IMPLEMENTED SUCCESSFULLY!
2025-07-05 07:45:30,733 - INFO - ================================================================================
