#!/usr/bin/env python3
"""
WNBA Game Totals Model - Step 2
Hierarchical model using frozen Player Points model to predict game totals
Uses same temporal splits as Step 1: 2015-2022 train, 2023 val, 2024-2025 test
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import pytorch_lightning as pl
from torch.optim.lr_scheduler import ReduceLROnPlateau
from typing import Dict, List, Optional, Tuple, Any
import numpy as np
import warnings
warnings.filterwarnings('ignore')

class GameTotalsModel(pl.LightningModule):
    """
    WNBA Game Totals Prediction Model
    
    Hierarchical architecture:
    1. Uses frozen Player Points model for individual predictions
    2. Aggregates player predictions to team totals
    3. Combines with team-level features for final game total prediction
    """
    
    def __init__(self,
                 player_points_model: pl.LightningModule,
                 team_features_dim: int = 28,
                 dropout_rate: float = 0.2,
                 learning_rate: float = 1e-3,
                 huber_delta: float = 2.0):
        """
        Initialize Game Totals Model
        
        Args:
            player_points_model: Trained Player Points model from Step 1
            team_features_dim: Number of team-level features
            dropout_rate: Dropout probability
            learning_rate: Learning rate
            huber_delta: Huber loss delta (larger for game-level variance)
        """
        super().__init__()
        self.save_hyperparameters(ignore=['player_points_model'])
        
        # Store and freeze player model
        self.player_model = player_points_model
        for param in self.player_model.parameters():
            param.requires_grad = False
        self.player_model.eval()
        
        # Team network input: team_features + home_agg + away_agg + differential + average
        team_net_input_dim = team_features_dim + 4
        
        # Team-level network for combining aggregated predictions with team features
        self.team_net = nn.Sequential(
            nn.Linear(team_net_input_dim, 256),
            nn.BatchNorm1d(256),
            nn.ELU(),
            nn.Dropout(dropout_rate),
            
            nn.Linear(256, 128),
            nn.BatchNorm1d(128),
            nn.ELU(),
            nn.Dropout(dropout_rate),
            
            nn.Linear(128, 64),
            nn.ELU(),
            
            nn.Linear(64, 1)
        )
        
        # Loss function (larger delta for game-level variance)
        self.loss_fn = nn.HuberLoss(delta=huber_delta)
        
        # Metrics storage
        self.training_step_outputs = []
        self.validation_step_outputs = []
    
    def forward(self, 
                team_features: torch.Tensor,
                player_features_dict: Dict[str, torch.Tensor]) -> torch.Tensor:
        """
        Forward pass for game totals prediction
        
        Args:
            team_features: Team-level features [batch_size, team_features_dim]
            player_features_dict: Dict with 'home' and 'away' player features
                                 Each: [batch_size, n_players, player_features_dim]
            
        Returns:
            Game total predictions [batch_size]
        """
        # Get player predictions using frozen player model
        with torch.no_grad():
            player_preds = {}
            for team_id, features in player_features_dict.items():
                # Reshape: [batch_size * n_players, player_features_dim]
                batch_size, n_players, feature_dim = features.shape
                features_flat = features.view(-1, feature_dim)
                
                # Get predictions and reshape back
                preds_flat = self.player_model(features_flat)
                player_preds[team_id] = preds_flat.view(batch_size, n_players)
        
        # Team aggregation with outlier clipping
        team_aggs = {}
        for team_id, preds in player_preds.items():
            # Clip extreme individual predictions
            clipped = torch.clamp(preds, min=0, max=30)
            # Sum to get team total
            team_aggs[team_id] = clipped.sum(dim=1, keepdim=True)  # [batch_size, 1]
        
        # Combine with team-level features
        home_agg = team_aggs['home']
        away_agg = team_aggs['away']
        
        # Create hierarchical features
        enhanced_features = torch.cat([
            team_features,
            home_agg,
            away_agg,
            home_agg - away_agg,  # Differential feature
            (home_agg + away_agg) / 2  # Average feature
        ], dim=1)
        
        # Final prediction through team network
        game_total_pred = self.team_net(enhanced_features).squeeze(-1)
        
        return game_total_pred
    
    def training_step(self, batch: Tuple[torch.Tensor, ...], batch_idx: int) -> torch.Tensor:
        """Training step"""
        team_features, player_features_dict, targets = batch
        
        predictions = self.forward(team_features, player_features_dict)
        loss = self.loss_fn(predictions, targets)
        
        # Calculate metrics
        mae = F.l1_loss(predictions, targets)
        
        # Log metrics
        self.log('train_loss', loss, on_step=True, on_epoch=True, prog_bar=True)
        self.log('train_mae', mae, on_step=False, on_epoch=True)
        
        # Store for epoch end
        self.training_step_outputs.append({
            'loss': loss.detach(),
            'mae': mae.detach(),
            'predictions': predictions.detach(),
            'targets': targets.detach()
        })
        
        return loss
    
    def validation_step(self, batch: Tuple[torch.Tensor, ...], batch_idx: int) -> torch.Tensor:
        """Validation step"""
        team_features, player_features_dict, targets = batch
        
        predictions = self.forward(team_features, player_features_dict)
        loss = self.loss_fn(predictions, targets)
        
        # Calculate metrics
        mae = F.l1_loss(predictions, targets)
        
        # Log metrics
        self.log('val_loss', loss, on_step=False, on_epoch=True, prog_bar=True)
        self.log('val_mae', mae, on_step=False, on_epoch=True, prog_bar=True)
        
        # Store for epoch end
        self.validation_step_outputs.append({
            'loss': loss.detach(),
            'mae': mae.detach(),
            'predictions': predictions.detach(),
            'targets': targets.detach()
        })
        
        return loss
    
    def test_step(self, batch: Tuple[torch.Tensor, ...], batch_idx: int) -> torch.Tensor:
        """Test step"""
        team_features, player_features_dict, targets = batch
        
        predictions = self.forward(team_features, player_features_dict)
        loss = self.loss_fn(predictions, targets)
        
        # Calculate metrics
        mae = F.l1_loss(predictions, targets)
        
        # Log metrics
        self.log('test_loss', loss, on_step=False, on_epoch=True)
        self.log('test_mae', mae, on_step=False, on_epoch=True)
        
        return loss
    
    def on_train_epoch_end(self) -> None:
        """Called at the end of training epoch"""
        if self.training_step_outputs:
            # Calculate epoch metrics
            avg_loss = torch.stack([x['loss'] for x in self.training_step_outputs]).mean()
            avg_mae = torch.stack([x['mae'] for x in self.training_step_outputs]).mean()
            
            # Log epoch metrics
            self.log('train_epoch_loss', avg_loss)
            self.log('train_epoch_mae', avg_mae)
            
            # Clear outputs
            self.training_step_outputs.clear()
    
    def on_validation_epoch_end(self) -> None:
        """Called at the end of validation epoch"""
        if self.validation_step_outputs:
            # Calculate epoch metrics
            avg_loss = torch.stack([x['loss'] for x in self.validation_step_outputs]).mean()
            avg_mae = torch.stack([x['mae'] for x in self.validation_step_outputs]).mean()
            
            # Log epoch metrics
            self.log('val_epoch_loss', avg_loss)
            self.log('val_epoch_mae', avg_mae)
            
            # Clear outputs
            self.validation_step_outputs.clear()
    
    def configure_optimizers(self) -> Dict[str, Any]:
        """Configure optimizers and schedulers"""
        optimizer = torch.optim.AdamW(
            self.parameters(),
            lr=self.hparams.learning_rate,
            weight_decay=1e-4
        )
        
        scheduler = ReduceLROnPlateau(
            optimizer,
            mode='min',
            factor=0.5,
            patience=8
        )
        
        return {
            'optimizer': optimizer,
            'lr_scheduler': {
                'scheduler': scheduler,
                'monitor': 'val_loss',
                'frequency': 1
            }
        }
    
    def validate_hierarchical_consistency(self, 
                                        dataloader,
                                        tolerance: float = 5.0) -> Dict[str, float]:
        """
        Validate that player predictions and game totals are consistent
        
        Args:
            dataloader: DataLoader for validation
            tolerance: Acceptable difference between direct sum and game prediction
            
        Returns:
            Consistency metrics
        """
        self.eval()
        
        total_mae = 0
        consistency_violations = 0
        total_samples = 0
        
        with torch.no_grad():
            for batch in dataloader:
                team_features, player_features_dict, targets = batch
                batch_size = targets.shape[0]
                
                # Get game prediction
                game_pred = self.forward(team_features, player_features_dict)
                
                # Get direct player sum
                direct_sum = torch.zeros(batch_size)
                for team_id, features in player_features_dict.items():
                    batch_size_check, n_players, feature_dim = features.shape
                    features_flat = features.view(-1, feature_dim)
                    player_preds = self.player_model(features_flat)
                    player_preds = player_preds.view(batch_size_check, n_players)
                    direct_sum += player_preds.sum(dim=1)
                
                # Check consistency
                diff = (direct_sum - game_pred).abs()
                consistency_violations += (diff > tolerance).sum().item()
                
                # Calculate MAE
                total_mae += (game_pred - targets).abs().sum().item()
                total_samples += batch_size
        
        consistency_rate = 1 - (consistency_violations / total_samples)
        avg_mae = total_mae / total_samples
        
        return {
            'game_total_mae': avg_mae,
            'consistency_rate': consistency_rate,
            'consistency_violations': consistency_violations,
            'total_samples': total_samples
        }

if __name__ == "__main__":
    # Test model creation
    from player_points_model import PlayerPointsModel
    
    # Create dummy player model
    player_model = PlayerPointsModel(input_dim=20)
    
    # Create game totals model
    game_model = GameTotalsModel(
        player_points_model=player_model,
        team_features_dim=28
    )
    
    print(f"Game Totals Model created:")
    print(f"  Team network parameters: {sum(p.numel() for p in game_model.team_net.parameters()):,}")
    print(f"  Total parameters: {sum(p.numel() for p in game_model.parameters()):,}")
    print(f"  Player model frozen: ✅")
    
    # Test forward pass
    batch_size = 4
    team_features = torch.randn(batch_size, 28)
    player_features_dict = {
        'home': torch.randn(batch_size, 8, 20),  # 8 players per team, 20 features
        'away': torch.randn(batch_size, 8, 20)
    }
    
    with torch.no_grad():
        predictions = game_model(team_features, player_features_dict)
        print(f"  Test predictions shape: {predictions.shape}")
        print(f"  Sample predictions: {predictions[:3].tolist()}")
    
    print("✅ Game Totals Model test completed successfully!")
