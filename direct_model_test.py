#!/usr/bin/env python3
"""
🏀 DIRECT MODEL TEST - LAST 7 DAYS
=================================

Direct test of our trained models on recent WNBA games
without going through the server (to avoid feature mismatch issues).
"""

import pandas as pd
import numpy as np
import torch
import json
from datetime import datetime, timed<PERSON>ta
from pathlib import Path
import sys
import warnings
warnings.filterwarnings('ignore')

# Add src to path
sys.path.append('src')
sys.path.append('src/models')

from models.modern_player_points_model import (
    PlayerPointsModel, 
    MultiTaskPlayerModel,
    BayesianPlayerModel
)

class DirectModelTester:
    """Direct test of trained models"""
    
    def __init__(self):
        """Initialize the tester"""
        self.models = {}
        self.test_results = {}
        
        print("🏀 DIRECT MODEL TESTER - LAST 7 DAYS")
        print("=" * 45)
        print(f"📅 Testing period: July 5-12, 2025")
        print(f"🎯 Target: Real game performance")
        
    def load_test_data(self):
        """Load and prepare test data"""
        try:
            # Load master dataset
            data_path = "data/master/wnba_definitive_master_dataset_FIXED.csv"
            if not Path(data_path).exists():
                print(f"❌ Master dataset not found: {data_path}")
                return None
            
            print("📊 Loading master dataset...")
            df = pd.read_csv(data_path)
            
            # Convert game_date to datetime
            df['game_date'] = pd.to_datetime(df['game_date'])
            
            print(f"✅ Loaded {len(df)} records")
            print(f"📅 Date range: {df['game_date'].min()} to {df['game_date'].max()}")
            
            # Filter last 7 days
            end_date = datetime(2025, 7, 12)
            start_date = end_date - timedelta(days=7)
            
            recent_games = df[
                (df['game_date'] >= start_date) & 
                (df['game_date'] <= end_date)
            ].copy()
            
            if len(recent_games) == 0:
                print("⚠️ No games found in the last 7 days, using most recent games")
                recent_games = df.nlargest(50, 'game_date').copy()
            
            print(f"✅ Found {len(recent_games)} games to test")
            
            # Prepare features and targets
            exclude_cols = {
                'points', 'target', 'player_id', 'game_id', 'game_date', 
                'player_name', 'team', 'opponent', 'season'
            }
            
            feature_cols = [col for col in recent_games.columns if col not in exclude_cols]
            
            # Get features and targets
            X = recent_games[feature_cols].values
            y = recent_games['points'].values if 'points' in recent_games.columns else recent_games['target'].values
            
            # Handle missing values
            X = np.nan_to_num(X, nan=0.0)
            
            print(f"📊 Features: {X.shape[1]}")
            print(f"🎯 Samples: {X.shape[0]}")
            print(f"⚖️ Avg points: {np.mean(y):.1f}")
            
            return X, y, recent_games, feature_cols
            
        except Exception as e:
            print(f"❌ Error loading test data: {e}")
            return None
    
    def load_trained_models(self, feature_count):
        """Load the trained models directly"""
        print("\n📦 Loading trained models...")
        
        # Load training results to get model paths
        try:
            results_path = Path("models/comprehensive_system/comprehensive_training_results.json")
            with open(results_path, 'r') as f:
                training_results = json.load(f)
        except Exception as e:
            print(f"❌ Error loading training results: {e}")
            return False
        
        models_loaded = 0
        
        # Load Enhanced model (PlayerPointsModel)
        try:
            model_info = training_results.get('all_models', {}).get('enhanced_model', {})
            model_path = model_info.get('best_model_path')
            
            if model_path and Path(model_path).exists():
                # Load checkpoint to get the actual input dimension
                checkpoint = torch.load(model_path, map_location='cpu')
                
                # Get input dimension from the first layer
                first_layer_weight = checkpoint['state_dict']['feature_net.0.weight']
                actual_input_dim = first_layer_weight.shape[1]
                
                print(f"   Enhanced model expects {actual_input_dim} features")
                
                # Initialize model with correct dimensions
                model = PlayerPointsModel(
                    input_dim=actual_input_dim,
                    dropout=0.25,
                    learning_rate=0.001
                )
                
                model.load_state_dict(checkpoint['state_dict'])
                model.eval()
                
                self.models['enhanced'] = {
                    'model': model,
                    'input_dim': actual_input_dim,
                    'mae': model_info.get('best_val_mae', 0)
                }
                
                models_loaded += 1
                print(f"✅ Enhanced model loaded (MAE: {model_info.get('best_val_mae', 0):.3f})")
            
        except Exception as e:
            print(f"❌ Error loading enhanced model: {e}")
        
        # Load MultiTask model
        try:
            model_info = training_results.get('all_models', {}).get('multitask_model', {})
            model_path = model_info.get('best_model_path')
            
            if model_path and Path(model_path).exists():
                # Load checkpoint to get the actual input dimension
                checkpoint = torch.load(model_path, map_location='cpu')
                
                # Get input dimension from the first layer
                first_layer_weight = checkpoint['state_dict']['net.0.weight']
                actual_input_dim = first_layer_weight.shape[1]
                
                print(f"   MultiTask model expects {actual_input_dim} features")
                
                # Initialize model with correct dimensions
                model = MultiTaskPlayerModel(
                    input_dim=actual_input_dim,
                    dropout=0.25,
                    learning_rate=0.001
                )
                
                model.load_state_dict(checkpoint['state_dict'])
                model.eval()
                
                self.models['multitask'] = {
                    'model': model,
                    'input_dim': actual_input_dim,
                    'mae': model_info.get('best_val_mae', 0)
                }
                
                models_loaded += 1
                print(f"✅ MultiTask model loaded (MAE: {model_info.get('best_val_mae', 0):.3f})")
            
        except Exception as e:
            print(f"❌ Error loading multitask model: {e}")
        
        # Load Bayesian model
        try:
            model_info = training_results.get('all_models', {}).get('bayesian_model', {})
            model_path = model_info.get('best_model_path')
            
            if model_path and Path(model_path).exists():
                # Load checkpoint to get the actual input dimension
                checkpoint = torch.load(model_path, map_location='cpu')
                
                # Get input dimension from the first layer
                first_layer_weight = checkpoint['state_dict']['net.0.weight']
                actual_input_dim = first_layer_weight.shape[1]
                
                print(f"   Bayesian model expects {actual_input_dim} features")
                
                # Initialize model with correct dimensions
                model = BayesianPlayerModel(
                    input_dim=actual_input_dim,
                    dropout=0.25,
                    learning_rate=0.001
                )
                
                model.load_state_dict(checkpoint['state_dict'])
                model.eval()
                
                self.models['bayesian'] = {
                    'model': model,
                    'input_dim': actual_input_dim,
                    'mae': model_info.get('best_val_mae', 0)
                }
                
                models_loaded += 1
                print(f"✅ Bayesian model loaded (MAE: {model_info.get('best_val_mae', 0):.3f})")
            
        except Exception as e:
            print(f"❌ Error loading bayesian model: {e}")
        
        print(f"📊 Total models loaded: {models_loaded}")
        return models_loaded > 0
    
    def test_model(self, model_name, X, y):
        """Test a specific model"""
        if model_name not in self.models:
            return None
        
        model_info = self.models[model_name]
        model = model_info['model']
        expected_dim = model_info['input_dim']
        
        # Adjust features to match expected dimension
        if X.shape[1] != expected_dim:
            if X.shape[1] > expected_dim:
                # Truncate features
                X_adjusted = X[:, :expected_dim]
            else:
                # Pad features
                padding = np.zeros((X.shape[0], expected_dim - X.shape[1]))
                X_adjusted = np.concatenate([X, padding], axis=1)
        else:
            X_adjusted = X
        
        # Convert to tensor
        X_tensor = torch.FloatTensor(X_adjusted)
        
        # Get predictions
        predictions = []
        with torch.no_grad():
            for i in range(len(X_tensor)):
                try:
                    if model_name == 'multitask':
                        # MultiTask model returns dict
                        output = model(X_tensor[i:i+1])
                        if isinstance(output, dict):
                            pred = float(output['points'].item())
                        else:
                            pred = float(output.item())
                    else:
                        # Regular models
                        pred = float(model(X_tensor[i:i+1]).item())
                    
                    predictions.append(pred)
                except Exception as e:
                    print(f"⚠️ Prediction error for sample {i}: {e}")
                    predictions.append(np.mean(y))  # Use mean as fallback
        
        # Calculate metrics
        predictions = np.array(predictions)
        errors = np.abs(predictions - y)
        
        mae = np.mean(errors)
        rmse = np.sqrt(np.mean((predictions - y) ** 2))
        mape = np.mean(np.abs((y - predictions) / np.maximum(y, 1))) * 100
        
        return {
            'predictions': predictions,
            'actual': y,
            'mae': mae,
            'rmse': rmse,
            'mape': mape,
            'avg_actual': np.mean(y),
            'avg_predicted': np.mean(predictions)
        }
    
    def run_complete_test(self):
        """Run the complete test"""
        print("🚀 Starting direct model test...")
        
        # Load test data
        test_data = self.load_test_data()
        if test_data is None:
            return False
        
        X, y, games_df, feature_cols = test_data
        
        # Load models
        if not self.load_trained_models(X.shape[1]):
            return False
        
        # Test each model
        print("\n🧪 TESTING MODELS ON REAL DATA")
        print("-" * 40)
        
        results = {}
        for model_name in self.models.keys():
            print(f"\n🤖 Testing {model_name.upper()} model...")
            result = self.test_model(model_name, X, y)
            
            if result:
                results[model_name] = result
                
                print(f"   📊 Predictions: {len(result['predictions'])}")
                print(f"   🎯 MAE: {result['mae']:.3f} points")
                print(f"   📈 RMSE: {result['rmse']:.3f} points")
                print(f"   📊 MAPE: {result['mape']:.1f}%")
                print(f"   ⚖️ Avg Actual: {result['avg_actual']:.1f} points")
                print(f"   🔮 Avg Predicted: {result['avg_predicted']:.1f} points")
            else:
                print(f"   ❌ Failed to test {model_name}")
        
        # Save results
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_data = {
            'timestamp': datetime.now().isoformat(),
            'test_period': 'July 5-12, 2025',
            'test_samples': len(y),
            'results': {name: {
                'mae': result['mae'],
                'rmse': result['rmse'],
                'mape': result['mape'],
                'avg_actual': result['avg_actual'],
                'avg_predicted': result['avg_predicted'],
                'predictions_count': len(result['predictions'])
            } for name, result in results.items()}
        }
        
        output_path = f"direct_model_test_results_{timestamp}.json"
        with open(output_path, 'w') as f:
            json.dump(output_data, f, indent=2)
        
        print(f"\n💾 Results saved to: {output_path}")
        
        # Summary
        print("\n🏆 FINAL RESULTS SUMMARY")
        print("=" * 30)
        
        if results:
            best_model = min(results.keys(), key=lambda k: results[k]['mae'])
            print(f"🥇 Best Model: {best_model.upper()} (MAE: {results[best_model]['mae']:.3f})")
            
            for name, result in sorted(results.items(), key=lambda x: x[1]['mae']):
                print(f"   {name.upper()}: {result['mae']:.3f} MAE")
        else:
            print("❌ No successful model tests")
        
        return True


def main():
    """Main function"""
    tester = DirectModelTester()
    success = tester.run_complete_test()
    
    if success:
        print("\n✅ Direct model testing completed successfully!")
    else:
        print("\n❌ Testing failed. Check the logs above.")


if __name__ == "__main__":
    main()
