[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "wnba-federated-learning"
version = "1.0.0"
description = "WNBA Federated Learning with Flower and 13 teams including GSV"
authors = [
    {name = "WNBA Analytics Team"},
]
dependencies = [
    "flwr>=1.6.0",
    "torch>=2.0.0",
    "pytorch-lightning>=2.0.0",
    "pandas>=2.0.0",
    "numpy>=1.24.0",
    "scikit-learn>=1.3.0",
    "ray[default]>=2.8.0",
]

[project.optional-dependencies]
privacy = [
    "pysyft>=0.8.0",
    "opacus>=1.4.0",
]
dev = [
    "pytest>=7.0.0",
    "black>=23.0.0",
    "flake8>=6.0.0",
]

[tool.flwr.app]
publisher = "wnba-analytics"

[tool.flwr.app.components]
serverapp = "flower_server:get_strategy"
clientapp = "flower_client:create_client"

[tool.flwr.app.config]
num-server-rounds = 15
min-fit-clients = 2
min-evaluate-clients = 2
min-available-clients = 2

[tool.flwr.federations]
default = "localhost"

[tool.flwr.federations.localhost]
address = "127.0.0.1:9092"
insecure = true
