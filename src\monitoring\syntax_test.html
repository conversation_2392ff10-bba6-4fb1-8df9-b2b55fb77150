<!DOCTYPE html>
<html>
<head>
    <title>Syntax Test</title>
</head>
<body>
    <h1>JavaScript Syntax Test</h1>
    <div id="output"></div>
    
    <script>
        console.log('Testing basic JavaScript...');
        
        // Test the WNBA team colors object
        const WNBA_TEAM_COLORS = {
            "ATL": {"primary": "#E03A3E", "secondary": "#C1D32F"},
            "CHI": {"primary": "#418FDE", "secondary": "#FFCD00"}
        };
        
        console.log('Team colors loaded:', WNBA_TEAM_COLORS);
        
        // Test a simple function
        function testFunction() {
            console.log('Test function works');
            return true;
        }
        
        // Test template literal
        const testTemplate = `
            <div>
                <span>Test: ${testFunction()}</span>
            </div>
        `;
        
        console.log('Template literal works:', testTemplate);
        
        // Test object destructuring
        const {primary, secondary} = WNBA_TEAM_COLORS.ATL;
        console.log('Destructuring works:', primary, secondary);
        
        document.getElementById('output').innerHTML = 'All tests passed!';
        
    </script>
</body>
</html>
