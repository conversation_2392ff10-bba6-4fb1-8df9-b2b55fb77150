
# 🎯 UPDATED TO USE EXPERT DATASET
# This script now uses the consolidated expert dataset: data/master/wnba_expert_dataset.csv
# Updated on: 2025-07-12 20:00:10
# Expert dataset contains: 49,512 high-quality records with 840 features
# All duplicates removed, data quality validated

#!/usr/bin/env python3
"""
Step 1 Model Validation Script
Quick validation that the trained Player Points model works correctly
"""

import torch
import numpy as np
from pathlib import Path
from player_points_model import PlayerPointsModel
from real_temporal_data_module import RealWNBADataModule

def validate_step1_model():
    """Validate the Step 1 Player Points model"""
    
    print("🏀 STEP 1 MODEL VALIDATION")
    print("="*50)
    
    # Check if model exists
    model_dir = Path("models/star_enhanced_wnba")
    if not model_dir.exists():
        print("❌ Model directory not found. Please train the model first.")
        return False
    
    # Find the best checkpoint
    checkpoint_files = list(model_dir.glob("star-enhanced-*.ckpt"))
    if not checkpoint_files:
        print("❌ No model checkpoints found. Please train the model first.")
        return False
    
    best_checkpoint = checkpoint_files[0]  # Take the first one
    print(f"✅ Found model checkpoint: {best_checkpoint}")
    
    try:
        # Load data module
        data_path = "data/master/wnba_expert_dataset.csv"
        data_module = RealWNBADataModule(
            data_path=data_path,
            batch_size=32,
            train_years=list(range(2015, 2023)),
            val_years=[2023],
            test_years=[2024, 2025]
        )
        data_module.setup()
        print(f"✅ Data module loaded: {len(data_module.get_feature_names())} features")
        
        # Load trained model
        model = PlayerPointsModel.load_from_checkpoint(
            best_checkpoint,
            input_dim=len(data_module.get_feature_names())
        )
        model.eval()
        print(f"✅ Model loaded: {sum(p.numel() for p in model.parameters()):,} parameters")
        
        # Test prediction on a sample
        test_loader = data_module.test_dataloader()
        sample_batch = next(iter(test_loader))
        features, targets = sample_batch
        
        with torch.no_grad():
            predictions = model(features)
            
        # Calculate sample metrics
        mae = torch.mean(torch.abs(predictions - targets)).item()
        
        print(f"✅ Model inference successful")
        print(f"   Sample batch size: {len(features)}")
        print(f"   Sample MAE: {mae:.3f}")
        print(f"   Prediction range: {predictions.min().item():.2f} - {predictions.max().item():.2f}")
        print(f"   Target range: {targets.min().item():.2f} - {targets.max().item():.2f}")
        
        # Validate star player performance
        star_mask = targets >= 15.0
        if star_mask.sum() > 0:
            star_mae = torch.mean(torch.abs(predictions[star_mask] - targets[star_mask])).item()
            print(f"   Star player MAE: {star_mae:.3f} ({'✅' if star_mae <= 3.5 else '❌'})")
        
        print("\n🎉 STEP 1 MODEL VALIDATION PASSED!")
        print("   Ready for Step 2: Team Aggregation and Game Models")
        
        return True
        
    except Exception as e:
        print(f"❌ Validation failed: {e}")
        return False

if __name__ == "__main__":
    success = validate_step1_model()
    if not success:
        print("\n💥 Please run: python enhanced_star_player_training.py")
