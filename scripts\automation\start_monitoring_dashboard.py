#!/usr/bin/env python3
"""
🎯 Start WNBA Monitoring Dashboard

Launches the unified monitoring dashboard with both console and web interfaces.

Author: WNBA Analytics Team
Date: 2025-07-11
"""

import sys
import os
from pathlib import Path
import argparse
import threading
import time

# Add src to path
sys.path.append(str(Path(__file__).parent.parent.parent / "src"))

from monitoring.unified_monitoring_dashboard import UnifiedMonitoringDashboard
from monitoring.web_dashboard import start_web_dashboard

def start_console_monitoring(refresh_interval=30):
    """Start console-based monitoring with periodic refresh"""
    
    print("🖥️ Starting console monitoring...")
    print(f"🔄 Refreshing every {refresh_interval} seconds")
    print("⏹️ Press Ctrl+C to stop")
    print()
    
    dashboard = UnifiedMonitoringDashboard()
    
    try:
        while True:
            # Clear screen (works on both Windows and Unix)
            os.system('cls' if os.name == 'nt' else 'clear')
            
            # Generate and display dashboard
            dashboard_data = dashboard.generate_unified_dashboard()
            dashboard.print_dashboard_summary(dashboard_data)
            
            # Wait for next refresh
            time.sleep(refresh_interval)
            
    except KeyboardInterrupt:
        print(f"\n🛑 Console monitoring stopped")

def main():
    """Main function"""
    
    parser = argparse.ArgumentParser(description="🎯 WNBA Monitoring Dashboard")
    parser.add_argument('--mode', choices=['console', 'web', 'both'], default='both',
                       help='Monitoring mode (default: both)')
    parser.add_argument('--port', type=int, default=8080,
                       help='Web dashboard port (default: 8080)')
    parser.add_argument('--refresh', type=int, default=30,
                       help='Console refresh interval in seconds (default: 30)')
    
    args = parser.parse_args()
    
    print("🎯 WNBA UNIFIED MONITORING DASHBOARD")
    print("=" * 70)
    print(f"🎛️ Mode: {args.mode.upper()}")
    
    if args.mode == 'console':
        start_console_monitoring(args.refresh)
    
    elif args.mode == 'web':
        start_web_dashboard(args.port)
    
    elif args.mode == 'both':
        print(f"🌐 Starting web dashboard on port {args.port}")
        print(f"🖥️ Starting console monitoring (refresh: {args.refresh}s)")
        print()
        
        # Start web dashboard in a separate thread
        web_thread = threading.Thread(
            target=start_web_dashboard, 
            args=(args.port,),
            daemon=True
        )
        web_thread.start()
        
        # Give web server time to start
        time.sleep(2)
        
        # Start console monitoring in main thread
        start_console_monitoring(args.refresh)

if __name__ == "__main__":
    main()
