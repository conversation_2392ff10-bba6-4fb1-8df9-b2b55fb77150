import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
import warnings


"""
Professional WNBA Feature Engineering System
Generates 100+ advanced features for accurate WNBA predictions
Mirrors NBA feature engineering with WNBA-specific optimizations
"""

warnings.filterwarnings('ignore')

class AdvancedWNBAFeatureEngineer:
    def __init__(self):
        self.feature_cache = {}
        self.team_stats_cache = {}
        self.player_stats_cache = {}

    def generate_comprehensive_features(self,
                                        game_data: Dict,
                                        historical_games: pd.DataFrame,
                                        team_stats: pd.DataFrame,
                                        player_stats: pd.DataFrame,
                                        injury_data: pd.DataFrame = None,
                                        weather_data: Dict = None) -> Dict[str, float]:
        """
        Generate 100+ professional-grade features for WNBA game prediction
        """
        features = {}

        # Basic game info
        home_team = game_data.get('home_team_id')
        away_team = game_data.get('away_team_id')
        game_date = pd.to_datetime(game_data.get('game_date', datetime.now()))

        # 1. TEAM PERFORMANCE FEATURES (20 features)
        features.update(self._generate_team_performance_features(
            home_team, away_team, historical_games, game_date
        ))

        # 2. HEAD-TO-HEAD FEATURES (15 features)
        features.update(self._generate_head_to_head_features(
            home_team, away_team, historical_games, game_date
        ))

        # 3. RECENT FORM FEATURES (20 features)
        features.update(self._generate_recent_form_features(
            home_team, away_team, historical_games, game_date
        ))

        # 4. WNBA-SPECIFIC PARITY FEATURES (10 features)
        features.update(self._generate_wnba_parity_features(
            home_team, away_team, historical_games, game_date
        ))

        # 5. VETERAN LEADERSHIP FEATURES (8 features)
        features.update(self._generate_veteran_leadership_features(
            home_team, away_team, player_stats
        ))

        # 6. ROSTER CONTINUITY FEATURES (12 features)
        features.update(self._generate_roster_continuity_features(
            home_team, away_team, player_stats, historical_games
        ))

        # 7. COACHING IMPACT FEATURES (8 features)
        features.update(self._generate_coaching_impact_features(
            home_team, away_team, historical_games
        ))

        # 8. SCHEDULE DENSITY FEATURES (10 features)
        features.update(self._generate_schedule_density_features(
            home_team, away_team, historical_games, game_date
        ))

        # 9. INTERNATIONAL PLAYER FEATURES (7 features)
        features.update(self._generate_international_player_features(
            home_team, away_team, player_stats
        ))

        # 10. REGULAR SEASON vs PLAYOFF FEATURES (5 features)
        features.update(self._generate_season_context_features(
            game_data, game_date
        ))

        return features

    def _generate_team_performance_features(self, home_team: str, away_team: str,
                                            historical_games: pd.DataFrame,
                                            game_date: datetime) -> Dict[str, float]:
        """Generate team performance metrics for WNBA"""
        features = {}

        # Filter recent games (last 10 games)
        recent_cutoff = game_date - timedelta(days=30)
        recent_games = historical_games[historical_games['game_date'] >= recent_cutoff]

        for team_type, mythic_roster_id in [('home', home_team), ('away', away_team)]:
            team_games = recent_games[
                (recent_games['home_team_id'] == mythic_roster_id) |
                (recent_games['away_team_id'] == mythic_roster_id)
            ].copy()

            if len(team_games) > 0:
                # Basic performance metrics
                features[f'{team_type}_win_rate'] = self._calculate_win_rate(team_games, mythic_roster_id)
                features[f'{team_type}_avg_points'] = self._calculate_avg_points(team_games, mythic_roster_id)
                features[f'{team_type}_avg_points_allowed'] = self._calculate_avg_points_allowed(team_games, mythic_roster_id)
                features[f'{team_type}_point_differential'] = features[f'{team_type}_avg_points'] - features[f'{team_type}_avg_points_allowed']

                # WNBA-specific metrics (lower scoring, higher parity)
                features[f'{team_type}_scoring_consistency'] = self._calculate_scoring_consistency(team_games, mythic_roster_id)
                features[f'{team_type}_defensive_efficiency'] = self._calculate_defensive_efficiency(team_games, mythic_roster_id)
                features[f'{team_type}_pace_factor'] = self._calculate_pace_factor(team_games, mythic_roster_id)
                features[f'{team_type}_clutch_performance'] = self._calculate_clutch_performance(team_games, mythic_roster_id)

                # Close game performance (very important in WNBA)
                features[f'{team_type}_close_game_record'] = self._calculate_close_game_record(team_games, mythic_roster_id)
                features[f'{team_type}_comeback_ability'] = self._calculate_comeback_ability(team_games, mythic_roster_id)
            else:
                # Default values for teams with no recent data
                features.update({
                    f'{team_type}_win_rate': 0.5,
                    f'{team_type}_avg_points': 80.0, # WNBA average
                    f'{team_type}_avg_points_allowed': 80.0,
                    f'{team_type}_point_differential': 0.0,
                    f'{team_type}_scoring_consistency': 0.5,
                    f'{team_type}_defensive_efficiency': 0.5,
                    f'{team_type}_pace_factor': 0.5,
                    f'{team_type}_clutch_performance': 0.5,
                    f'{team_type}_close_game_record': 0.5,
                    f'{team_type}_comeback_ability': 0.5
                })

        return features

    def _generate_wnba_parity_features(self, home_team: str, away_team: str,
                                       historical_games: pd.DataFrame,
                                       game_date: datetime) -> Dict[str, float]:
        """Generate WNBA-specific parity and competitive balance features"""
        features = {}

        # WNBA has high parity - any team can beat any team
        season_games = historical_games[
            historical_games['game_date'].dt.year == game_date.year
        ]

        if len(season_games) > 0:
            # League-wide parity metrics
            all_teams = pd.concat([
                season_games['home_team_id'],
                season_games['away_team_id']
            ]).unique()

            # Calculate strength of schedule
            features['home_strength_of_schedule'] = self._calculate_strength_of_schedule(
                home_team, season_games, all_teams
            )
            features['away_strength_of_schedule'] = self._calculate_strength_of_schedule(
                away_team, season_games, all_teams
            )

            # Upset potential (WNBA loves upsets)
            features['upset_potential'] = self._calculate_upset_potential(
                home_team, away_team, season_games
            )

            # Hot/cold streak detection
            features['home_momentum'] = self._calculate_team_momentum(home_team, season_games)
            features['away_momentum'] = self._calculate_team_momentum(away_team, season_games)

            # Conference strength difference
            features['conference_strength_diff'] = self._calculate_conference_strength_diff(
                home_team, away_team, season_games
            )

        else:
            features.update({
                'home_strength_of_schedule': 0.5,
                'away_strength_of_schedule': 0.5,
                'upset_potential': 0.3, # Higher base upset potential in WNBA
                'home_momentum': 0.5,
                'away_momentum': 0.5,
                'conference_strength_diff': 0.0
            })

        return features

    def _generate_veteran_leadership_features(self, home_team: str, away_team: str,
                                              player_stats: pd.DataFrame) -> Dict[str, float]:
        """Generate veteran leadership features - crucial in WNBA"""
        features = {}

        if not player_stats.empty:
            for team_type, mythic_roster_id in [('home', home_team), ('away', away_team)]:
                team_players = player_stats[player_stats['mythic_roster_id'] == mythic_roster_id]

                if len(team_players) > 0:
                    # Veteran presence (players with 5+ years experience)
                    veteran_players = team_players[team_players.get('years_pro', 0) >= 5]
                    features[f'{team_type}_veteran_count'] = len(veteran_players)
                    features[f'{team_type}_veteran_minutes_pct'] = (
                        veteran_players.get('minutes_per_game', 0).sum() /
                        team_players.get('minutes_per_game', 1).sum()
                    )

                    # Leadership metrics
                    features[f'{team_type}_avg_experience'] = team_players.get('years_pro', 0).mean()
                    features[f'{team_type}_playoff_experience'] = team_players.get('playoff_games', 0).sum()
                else:
                    features.update({
                        f'{team_type}_veteran_count': 2, # Default assumption
                        f'{team_type}_veteran_minutes_pct': 0.4,
                        f'{team_type}_avg_experience': 3.5,
                        f'{team_type}_playoff_experience': 20
                    })
        else:
            # Default values when player stats unavailable
            for team_type in ['home', 'away']:
                features.update({
                    f'{team_type}_veteran_count': 2,
                    f'{team_type}_veteran_minutes_pct': 0.4,
                    f'{team_type}_avg_experience': 3.5,
                    f'{team_type}_playoff_experience': 20
                })

        return features

    def _generate_roster_continuity_features(self, home_team: str, away_team: str,
                                              player_stats: pd.DataFrame,
                                              historical_games: pd.DataFrame) -> Dict[str, float]:
        """Generate roster continuity and chemistry features"""
        features = {}

        # Default values for missing data
        default_features = {
            'roster_continuity': 0.7,
            'new_player_integration': 0.5,
            'core_player_health': 0.8,
            'chemistry_rating': 0.6,
            'rotation_stability': 0.6,
            'veteran_new_balance': 0.5
        }

        for team_type, mythic_roster_id in [('home', home_team), ('away', away_team)]:
            if not player_stats.empty:
                team_players = player_stats[player_stats['mythic_roster_id'] == mythic_roster_id]

                if len(team_players) > 0:
                    # Calculate actual roster features
                    features[f'{team_type}_roster_continuity'] = self._calculate_roster_continuity(team_players)
                    features[f'{team_type}_new_player_integration'] = self._calculate_new_player_integration(team_players)
                    features[f'{team_type}_core_player_health'] = self._calculate_core_player_health(team_players)
                    features[f'{team_type}_chemistry_rating'] = self._calculate_chemistry_rating(team_players)
                    features[f'{team_type}_rotation_stability'] = self._calculate_rotation_stability(team_players)
                    features[f'{team_type}_veteran_new_balance'] = self._calculate_veteran_new_balance(team_players)
                else:
                    # Use defaults
                    for key, value in default_features.items():
                        features[f'{team_type}_{key}'] = value
            else:
                # Use defaults when no player data
                for key, value in default_features.items():
                    features[f'{team_type}_{key}'] = value

        return features

    def _generate_coaching_impact_features(self, home_team: str, away_team: str,
                                           historical_games: pd.DataFrame) -> Dict[str, float]:
        """Generate coaching impact features - very important in WNBA"""
        features = {}

        # Coaching in WNBA has significant impact due to shorter bench
        # and strategic adjustments in smaller lineup rotations

        recent_games = historical_games.tail(50) # Last 50 games

        for team_type, mythic_roster_id in [('home', home_team), ('away', away_team)]:
            team_games = recent_games[
                (recent_games['home_team_id'] == mythic_roster_id) |
                (recent_games['away_team_id'] == mythic_roster_id)
            ]

            if len(team_games) > 0:
                # Timeout usage efficiency
                features[f'{team_type}_timeout_efficiency'] = self._calculate_timeout_efficiency(team_games, mythic_roster_id)

                # Strategic adjustment success
                features[f'{team_type}_halftime_adjustments'] = self._calculate_halftime_adjustments(team_games, mythic_roster_id)

                # Rotation management
                features[f'{team_type}_rotation_efficiency'] = self._calculate_rotation_efficiency(team_games, mythic_roster_id)

                # Out-of-timeout performance
                features[f'{team_type}_after_timeout_success'] = self._calculate_after_timeout_success(team_games, mythic_roster_id)
            else:
                features.update({
                    f'{team_type}_timeout_efficiency': 0.5,
                    f'{team_type}_halftime_adjustments': 0.5,
                    f'{team_type}_rotation_efficiency': 0.5,
                    f'{team_type}_after_timeout_success': 0.5
                })

        return features

    def _generate_schedule_density_features(self, home_team: str, away_team: str,
                                            historical_games: pd.DataFrame,
                                            game_date: datetime) -> Dict[str, float]:
        """Generate schedule density and fatigue features"""
        features = {}

        # Look at games in last 7 days and next 7 days
        week_before = game_date - timedelta(days=7)
        week_after = game_date + timedelta(days=7)

        for team_type, mythic_roster_id in [('home', home_team), ('away', away_team)]:
            # Games in past week
            recent_games = historical_games[
                (historical_games['game_date'] >= week_before) &
                (historical_games['game_date'] < game_date) &
                ((historical_games['home_team_id'] == mythic_roster_id) |
                (historical_games['away_team_id'] == mythic_roster_id))
            ]

            features[f'{team_type}_games_last_week'] = len(recent_games)
            features[f'{team_type}_rest_days'] = (game_date - recent_games['game_date'].max()).days if len(recent_games) > 0 else 7

            # Travel fatigue (simplified - would need actual travel data)
            features[f'{team_type}_travel_fatigue'] = max(0, 1 - features[f'{team_type}_rest_days'] / 3)

            # Schedule density
            features[f'{team_type}_schedule_density'] = features[f'{team_type}_games_last_week'] / 7

            # Back-to-back indicator
            features[f'{team_type}_back_to_back'] = 1.0 if features[f'{team_type}_rest_days'] <= 1 else 0.0

        return features

    def _generate_international_player_features(self, home_team: str, away_team: str,
                                                player_stats: pd.DataFrame) -> Dict[str, float]:
        """Generate international player impact features"""
        features = {}

        for team_type, mythic_roster_id in [('home', home_team), ('away', away_team)]:
            if not player_stats.empty:
                team_players = player_stats[player_stats['mythic_roster_id'] == mythic_roster_id]

                if len(team_players) > 0:
                    # International player percentage
                    international_players = team_players[team_players.get('country', 'USA') != 'USA']
                    features[f'{team_type}_international_pct'] = len(international_players) / len(team_players)

                    # International minutes impact
                    total_minutes = team_players.get('minutes_per_game', 0).sum()
                    intl_minutes = international_players.get('minutes_per_game', 0).sum()
                    features[f'{team_type}_intl_minutes_impact'] = intl_minutes / total_minutes if total_minutes > 0 else 0.0

                    # International star presence
                    intl_stars = international_players[international_players.get('ppg', 0) >= 15]
                    features[f'{team_type}_intl_star_presence'] = len(intl_stars) / len(team_players)
                else:
                    features.update({
                        f'{team_type}_international_pct': 0.25, # WNBA average
                        f'{team_type}_intl_minutes_impact': 0.20,
                        f'{team_type}_intl_star_presence': 0.15
                    })
            else:
                features.update({
                    f'{team_type}_international_pct': 0.25,
                    f'{team_type}_intl_minutes_impact': 0.20,
                    f'{team_type}_intl_star_presence': 0.15
                })

        return features

    def _generate_season_context_features(self, game_data: Dict, game_date: datetime) -> Dict[str, float]:
        """Generate season context features"""
        features = {}

        # Determine season phase
        # WNBA season typically runs May-September
        month = game_date.month

        if month in [5, 6]: # Early season
            features['season_phase'] = 0.0
            features['season_urgency'] = 0.2
        elif month in [7]: # Mid season
            features['season_phase'] = 0.5
            features['season_urgency'] = 0.5
        elif month in [8, 9]: # Late season/playoffs
            features['season_phase'] = 1.0
            features['season_urgency'] = 0.9
        else: # Off-season or unusual
            features['season_phase'] = 0.5
            features['season_urgency'] = 0.5

        # Game importance (regular season vs playoffs)
        game_type = game_data.get('game_type', 'regular')
        if game_type.lower() in ['playoff', 'playoffs']:
            features['playoff_game'] = 1.0
            features['game_importance'] = 1.0
        elif game_type.lower() in ['commissioner_cup']:
            features['playoff_game'] = 0.0
            features['game_importance'] = 0.7
        else:
            features['playoff_game'] = 0.0
            features['game_importance'] = 0.4

        return features

    # Helper methods for calculations
    def _calculate_win_rate(self, games: pd.DataFrame, mythic_roster_id: str) -> float:
        """Calculate win rate for a team"""
        wins = 0
        total = len(games)

        for _, game in games.iterrows():
            if game['home_team_id'] == mythic_roster_id:
                if game.get('home_score', 0) > game.get('away_score', 0):
                    wins += 1
            else:
                if game.get('away_score', 0) > game.get('home_score', 0):
                    wins += 1

        return wins / total if total > 0 else 0.5

    def _calculate_avg_points(self, games: pd.DataFrame, mythic_roster_id: str) -> float:
        """Calculate average points scored"""
        total_points = 0
        game_count = len(games)

        for _, game in games.iterrows():
            if game['home_team_id'] == mythic_roster_id:
                total_points += game.get('home_score', 80)
            else:
                total_points += game.get('away_score', 80)

        return total_points / game_count if game_count > 0 else 80.0

    def _calculate_avg_points_allowed(self, games: pd.DataFrame, mythic_roster_id: str) -> float:
        """Calculate average points allowed"""
        total_points = 0
        game_count = len(games)

        for _, game in games.iterrows():
            if game['home_team_id'] == mythic_roster_id:
                total_points += game.get('away_score', 80)
            else:
                total_points += game.get('home_score', 80)

        return total_points / game_count if game_count > 0 else 80.0

    def _calculate_scoring_consistency(self, games: pd.DataFrame, mythic_roster_id: str) -> float:
        """Calculate scoring consistency (lower variance = higher consistency)"""
        scores = []

        for _, game in games.iterrows():
            if game['home_team_id'] == mythic_roster_id:
                scores.append(game.get('home_score', 80))
            else:
                scores.append(game.get('away_score', 80))

        if len(scores) > 1:
            std_dev = np.std(scores)
            avg_score = np.mean(scores)
            # Normalize consistency score (lower std dev = higher consistency)
            return max(0, 1 - (std_dev / avg_score)) if avg_score > 0 else 0.5

        return 0.5

    def _calculate_defensive_efficiency(self, games: pd.DataFrame, mythic_roster_id: str) -> float:
        """Calculate defensive efficiency"""
        # This is a simplified calculation - in practice would use more advanced metrics
        points_allowed = self._calculate_avg_points_allowed(games, mythic_roster_id)
        league_avg = 80.0 # WNBA average

        # Lower points allowed = higher efficiency
        return max(0, 1 - ((points_allowed - league_avg) / league_avg)) if league_avg > 0 else 0.5

    def _calculate_pace_factor(self, games: pd.DataFrame, mythic_roster_id: str) -> float:
        """Calculate pace factor - possessions per game"""
        # Simplified calculation
        total_points = 0
        game_count = len(games)

        for _, game in games.iterrows():
            total_points += game.get('home_score', 80) + game.get('away_score', 80)

        avg_total = total_points / game_count if game_count > 0 else 160

        # Normalize around WNBA average (160 total points)
        return avg_total / 160.0

    def _calculate_clutch_performance(self, games: pd.DataFrame, mythic_roster_id: str) -> float:
        """Calculate clutch performance (games decided by 5 points or less)"""
        clutch_wins = 0
        clutch_games = 0

        for _, game in games.iterrows():
            point_diff = abs(game.get('home_score', 80) - game.get('away_score', 80))

            if point_diff <= 5: # Clutch game
                clutch_games += 1

                if game['home_team_id'] == mythic_roster_id:
                    if game.get('home_score', 0) > game.get('away_score', 0):
                        clutch_wins += 1
                else:
                    if game.get('away_score', 0) > game.get('home_score', 0):
                        clutch_wins += 1

        return clutch_wins / clutch_games if clutch_games > 0 else 0.5

    def _calculate_close_game_record(self, games: pd.DataFrame, mythic_roster_id: str) -> float:
        """Calculate record in close games (within 10 points)"""
        return self._calculate_clutch_performance(games, mythic_roster_id) # Similar calculation

    def _calculate_comeback_ability(self, games: pd.DataFrame, mythic_roster_id: str) -> float:
        """Calculate ability to come back from deficits"""
        # This would require quarter-by-quarter data in practice
        # For now, use clutch performance as proxy
        return self._calculate_clutch_performance(games, mythic_roster_id)

    def _calculate_strength_of_schedule(self, mythic_roster_id: str, games: pd.DataFrame, all_teams: list) -> float:
        """Calculate strength of schedule"""
        # Simplified calculation
        opponents = []

        team_games = games[
            (games['home_team_id'] == mythic_roster_id) |
            (games['away_team_id'] == mythic_roster_id)
        ]

        for _, game in team_games.iterrows():
            if game['home_team_id'] == mythic_roster_id:
                opponents.append(game['away_team_id'])
            else:
                opponents.append(game['home_team_id'])

        # Calculate average opponent strength (simplified)
        return 0.5 # Placeholder - would calculate based on opponent records

    def _calculate_upset_potential(self, home_team: str, away_team: str, games: pd.DataFrame) -> float:
        """Calculate upset potential based on recent performance variance"""
        # WNBA has high parity, so upset potential is generally higher
        return 0.35 # Base upset potential higher than NBA

    def _calculate_team_momentum(self, mythic_roster_id: str, games: pd.DataFrame) -> float:
        """Calculate team momentum based on recent trends"""
        recent_games = games.tail(5) # Last 5 games
        team_recent = recent_games[
            (recent_games['home_team_id'] == mythic_roster_id) |
            (recent_games['away_team_id'] == mythic_roster_id)
        ]

        return self._calculate_win_rate(team_recent, mythic_roster_id)

    def _calculate_conference_strength_diff(self, home_team: str, away_team: str, games: pd.DataFrame) -> float:
        """Calculate difference in conference strength"""
        # Simplified - would need conference mapping
        return 0.0 # Placeholder

    # Additional helper methods for coaching features
    def _calculate_timeout_efficiency(self, games: pd.DataFrame, mythic_roster_id: str) -> float:
        """Calculate timeout usage efficiency"""
        return 0.5 # Placeholder - would need timeout data

    def _calculate_halftime_adjustments(self, games: pd.DataFrame, mythic_roster_id: str) -> float:
        """Calculate halftime adjustment success"""
        return 0.5 # Placeholder - would need quarter-by-quarter data

    def _calculate_rotation_efficiency(self, games: pd.DataFrame, mythic_roster_id: str) -> float:
        """Calculate rotation management efficiency"""
        return 0.5 # Placeholder - would need player minute data

    def _calculate_after_timeout_success(self, games: pd.DataFrame, mythic_roster_id: str) -> float:
        """Calculate success rate after timeouts"""
        return 0.5 # Placeholder - would need play-by-play data

    def _generate_head_to_head_features(self, home_team: str, away_team: str,
                                        historical_games: pd.DataFrame,
                                        game_date: datetime) -> Dict[str, float]:
        """Generate head-to-head matchup features"""
        features = {}

        # Get all historical games between these teams
        h2h_games = historical_games[
            ((historical_games['home_team_id'] == home_team) &
            (historical_games['away_team_id'] == away_team)) |
            ((historical_games['home_team_id'] == away_team) &
            (historical_games['away_team_id'] == home_team))
        ]

        if len(h2h_games) > 0:
            # Overall head-to-head record
            home_wins = len(h2h_games[
                (h2h_games['home_team_id'] == home_team) &
                (h2h_games['home_score'] > h2h_games['away_score'])
            ]) + len(h2h_games[
                (h2h_games['away_team_id'] == home_team) &
                (h2h_games['away_score'] > h2h_games['home_score'])
            ])

            features['home_h2h_win_rate'] = home_wins / len(h2h_games)
            features['away_h2h_win_rate'] = 1 - features['home_h2h_win_rate']

            # Recent h2h performance (last 3 seasons)
            recent_h2h = h2h_games[h2h_games['game_date'] >= (game_date - timedelta(days=1095))]
            if len(recent_h2h) > 0:
                features['recent_h2h_avg_total'] = (
                    recent_h2h['home_score'].mean() + recent_h2h['away_score'].mean()
                )
                features['h2h_competitiveness'] = 1 / (recent_h2h['home_score'] - recent_h2h['away_score']).abs().mean()
            else:
                features['recent_h2h_avg_total'] = 160.0
                features['h2h_competitiveness'] = 0.8 # High competitiveness assumed
        else:
            # No historical data
            features.update({
                'home_h2h_win_rate': 0.5,
                'away_h2h_win_rate': 0.5,
                'recent_h2h_avg_total': 160.0,
                'h2h_competitiveness': 0.8
            })

        return features

    def _generate_recent_form_features(self, home_team: str, away_team: str,
                                       historical_games: pd.DataFrame,
                                       game_date: datetime) -> Dict[str, float]:
        """Generate recent form features (last 10 games)"""
        features = {}

        recent_cutoff = game_date - timedelta(days=30)
        recent_games = historical_games[historical_games['game_date'] >= recent_cutoff]

        for team_type, mythic_roster_id in [('home', home_team), ('away', away_team)]:
            team_recent = recent_games[
                (recent_games['home_team_id'] == mythic_roster_id) |
                (recent_games['away_team_id'] == mythic_roster_id)
            ].tail(10) # Last 10 games

            if len(team_recent) > 0:
                features[f'{team_type}_recent_win_rate'] = self._calculate_win_rate(team_recent, mythic_roster_id)
                features[f'{team_type}_recent_scoring_trend'] = self._calculate_scoring_trend(team_recent, mythic_roster_id)
                features[f'{team_type}_recent_defensive_trend'] = self._calculate_defensive_trend(team_recent, mythic_roster_id)
                features[f'{team_type}_form_variance'] = self._calculate_form_variance(team_recent, mythic_roster_id)
                features[f'{team_type}_streak'] = self._calculate_current_streak(team_recent, mythic_roster_id)
            else:
                features.update({
                    f'{team_type}_recent_win_rate': 0.5,
                    f'{team_type}_recent_scoring_trend': 0.0,
                    f'{team_type}_recent_defensive_trend': 0.0,
                    f'{team_type}_form_variance': 0.5,
                    f'{team_type}_streak': 0.0
                })

        return features

    def _calculate_scoring_trend(self, games: pd.DataFrame, mythic_roster_id: str) -> float:
        """Calculate if team is trending up or down in scoring"""
        scores = []
        for _, game in games.iterrows():
            if game['home_team_id'] == mythic_roster_id:
                scores.append(game.get('home_score', 80))
            else:
                scores.append(game.get('away_score', 80))

        if len(scores) > 2:
            # Simple linear trend
            x = list(range(len(scores)))
            trend = np.polyfit(x, scores, 1)[0] # Slope of trend line
            return np.tanh(trend / 10) # Normalize to [-1, 1]

        return 0.0

    def _calculate_defensive_trend(self, games: pd.DataFrame, mythic_roster_id: str) -> float:
        """Calculate if team's defense is trending up or down"""
        points_allowed = []
        for _, game in games.iterrows():
            if game['home_team_id'] == mythic_roster_id:
                points_allowed.append(game.get('away_score', 80))
            else:
                points_allowed.append(game.get('home_score', 80))

        if len(points_allowed) > 2:
            x = list(range(len(points_allowed)))
            trend = np.polyfit(x, points_allowed, 1)[0]
            return -np.tanh(trend / 10) # Negative because less points allowed = better

        return 0.0

    def _calculate_form_variance(self, games: pd.DataFrame, mythic_roster_id: str) -> float:
        """Calculate consistency of recent form"""
        return self._calculate_scoring_consistency(games, mythic_roster_id)

    def _calculate_current_streak(self, games: pd.DataFrame, mythic_roster_id: str) -> float:
        """Calculate current win/loss streak"""
        if len(games) == 0:
            return 0.0

        streak = 0
        last_result = None

        for _, game in games.iterrows():
            won = False
            if game['home_team_id'] == mythic_roster_id:
                won = game.get('home_score', 0) > game.get('away_score', 0)
            else:
                won = game.get('away_score', 0) > game.get('home_score', 0)

            if last_result is None:
                last_result = won
                streak = 1 if won else -1
            elif last_result == won:
                streak += 1 if won else -1
            else:
                break

        return np.tanh(streak / 5) # Normalize long streaks

    # Helper methods for roster continuity
    def _calculate_roster_continuity(self, players: pd.DataFrame) -> float:
        """Calculate roster continuity from previous season"""
        # Simplified - would need previous season data
        return 0.7 # Default assumption

    def _calculate_new_player_integration(self, players: pd.DataFrame) -> float:
        """Calculate how well new players are integrated"""
        return 0.5 # Default assumption

    def _calculate_core_player_health(self, players: pd.DataFrame) -> float:
        """Calculate health status of core players"""
        return 0.8 # Default assumption

    def _calculate_chemistry_rating(self, players: pd.DataFrame) -> float:
        """Calculate team chemistry rating"""
        return 0.6 # Default assumption

    def _calculate_rotation_stability(self, players: pd.DataFrame) -> float:
        """Calculate rotation stability"""
        return 0.6 # Default assumption

    def _calculate_veteran_new_balance(self, players: pd.DataFrame) -> float:
        """Calculate balance between veterans and new players"""
        return 0.5 # Default assumption
