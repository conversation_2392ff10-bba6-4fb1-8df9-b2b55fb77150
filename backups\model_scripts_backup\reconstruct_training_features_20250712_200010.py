#!/usr/bin/env python3
"""
🔍 RECONSTRUCT TRAINING FEATURES
===============================

Reconstruct the exact 188 features used during training by analyzing
the checkpoint and dataset.
"""

import pandas as pd
import numpy as np
import torch
from pathlib import Path
import json

def reconstruct_training_features():
    """Reconstruct the exact 188 features used during training"""
    
    print("🔍 RECONSTRUCTING TRAINING FEATURES")
    print("=" * 45)
    
    # Load a checkpoint to confirm input dimension
    checkpoint_path = "models/enhanced_model/enhanced_epoch=01_val_loss=0.7678.ckpt"
    if Path(checkpoint_path).exists():
        checkpoint = torch.load(checkpoint_path, map_location='cpu')
        
        # Find the input layer
        for key, tensor in checkpoint['state_dict'].items():
            if 'feature_net.0.weight' in key:
                input_dim = tensor.shape[1]
                print(f"✅ Confirmed: Models expect {input_dim} input features")
                break
    else:
        print("❌ Checkpoint not found")
        return None
    
    # Load the full dataset
    print("\n📊 Loading full dataset to reconstruct features...")
    df = pd.read_csv('data/master/wnba_definitive_master_dataset_FIXED.csv', low_memory=False)
    
    print(f"   Dataset shape: {df.shape}")
    print(f"   Columns: {len(df.columns)}")
    
    # Apply the same exclusions as training
    exclude_cols = {
        'points', 'target', 'player_id', 'game_id', 'game_date', 
        'player_name', 'team', 'opponent', 'season', 'Unnamed: 0'
    }
    
    potential_features = [col for col in df.columns if col not in exclude_cols]
    print(f"   Potential features: {len(potential_features)}")
    
    # Convert to numeric and filter (same as training)
    numeric_features = []
    for col in potential_features:
        try:
            # Convert to numeric
            df[col] = pd.to_numeric(df[col], errors='coerce')
            
            # Check if it's numeric and has some variance
            if df[col].dtype in ['float64', 'int64']:
                # Check for variance (not all same value)
                if df[col].nunique() > 1:
                    # Check missing data percentage
                    missing_pct = df[col].isnull().sum() / len(df)
                    if missing_pct < 0.9:  # Less than 90% missing
                        numeric_features.append(col)
                        
                        if len(numeric_features) >= input_dim:
                            break
        except:
            continue
    
    print(f"   Numeric features found: {len(numeric_features)}")
    
    # Take exactly the number needed
    training_features = numeric_features[:input_dim]
    
    print(f"✅ Reconstructed {len(training_features)} training features")
    
    # Show first 10 features
    print("\n📝 First 10 training features:")
    for i, feature in enumerate(training_features[:10]):
        print(f"   {i+1:2d}. {feature}")
    
    if len(training_features) > 10:
        print(f"   ... and {len(training_features) - 10} more")
    
    # Save the reconstructed features
    output_path = "reconstructed_training_features.json"
    with open(output_path, 'w') as f:
        json.dump(training_features, f, indent=2)
    
    print(f"\n💾 Saved reconstructed features to: {output_path}")
    
    # Test feature extraction on a sample
    print("\n🧪 Testing feature extraction...")
    
    # Get a small sample
    sample_df = df.head(10).copy()
    
    # Extract features
    feature_data = {}
    for col in training_features:
        if col in sample_df.columns:
            series = pd.to_numeric(sample_df[col], errors='coerce')
            # Fill missing with median
            if series.notna().sum() > 0:
                fill_value = series.median()
                if pd.isna(fill_value):
                    fill_value = 0.0
            else:
                fill_value = 0.0
            feature_data[col] = series.fillna(fill_value).values
        else:
            feature_data[col] = np.zeros(len(sample_df))
    
    # Create feature matrix
    X_sample = np.column_stack([feature_data[col] for col in training_features])
    
    print(f"   Sample feature matrix shape: {X_sample.shape}")
    print(f"   Feature range: {X_sample.min():.3f} to {X_sample.max():.3f}")
    print(f"   Non-zero features: {np.count_nonzero(X_sample)} / {X_sample.size}")
    
    return training_features

def test_with_reconstructed_features():
    """Test models with reconstructed features"""
    
    # Reconstruct features
    training_features = reconstruct_training_features()
    if training_features is None:
        return False
    
    print(f"\n🎯 TESTING WITH RECONSTRUCTED FEATURES")
    print("-" * 45)
    
    # Load test data
    df = pd.read_csv('data/master/wnba_definitive_master_dataset_FIXED.csv', low_memory=False)
    df['game_date'] = pd.to_datetime(df['game_date'], errors='coerce')
    
    # Get recent games
    from datetime import datetime, timedelta
    end_date = datetime(2025, 7, 12)
    start_date = end_date - timedelta(days=7)
    
    recent_games = df[
        (df['game_date'] >= start_date) & 
        (df['game_date'] <= end_date)
    ].copy()
    
    if len(recent_games) == 0:
        recent_games = df.nlargest(50, 'game_date').copy()
    
    # Get targets
    if 'points' in recent_games.columns:
        target_col = 'points'
    elif 'target' in recent_games.columns:
        target_col = 'target'
    else:
        print("❌ No target column")
        return False
    
    recent_games = recent_games[recent_games[target_col].notna()].copy()
    y = recent_games[target_col].values
    
    print(f"   Test samples: {len(recent_games)}")
    print(f"   Target stats: mean={np.mean(y):.1f}, std={np.std(y):.1f}")
    
    # Extract features
    feature_data = {}
    for col in training_features:
        if col in recent_games.columns:
            series = pd.to_numeric(recent_games[col], errors='coerce')
            if series.notna().sum() > 0:
                fill_value = series.median()
                if pd.isna(fill_value):
                    fill_value = 0.0
            else:
                fill_value = 0.0
            feature_data[col] = series.fillna(fill_value).values
        else:
            feature_data[col] = np.zeros(len(recent_games))
    
    X = np.column_stack([feature_data[col] for col in training_features])
    X = X.astype(np.float32)
    
    print(f"   Feature matrix: {X.shape}")
    print(f"   Feature range: {X.min():.3f} to {X.max():.3f}")
    
    # Test loading a model
    print("\n🤖 Testing model loading...")
    
    try:
        import sys
        sys.path.append('src')
        sys.path.append('src/models')
        from models.modern_player_points_model import PlayerPointsModel
        
        # Create model with correct input dimension
        model = PlayerPointsModel(
            input_dim=len(training_features),
            dropout=0.25,
            learning_rate=0.001
        )
        
        # Load checkpoint
        checkpoint_path = "models/enhanced_model/enhanced_epoch=01_val_loss=0.7678.ckpt"
        checkpoint = torch.load(checkpoint_path, map_location='cpu')
        
        # Try to load state dict
        model.load_state_dict(checkpoint['state_dict'])
        model.eval()
        
        print("   ✅ Model loaded successfully!")
        
        # Test prediction
        X_tensor = torch.FloatTensor(X[:5])  # Test on first 5 samples
        
        with torch.no_grad():
            predictions = model(X_tensor)
            predictions = predictions.cpu().numpy()
        
        print(f"   🔮 Sample predictions: {predictions}")
        print(f"   📊 Actual values: {y[:5]}")
        
        # Calculate MAE on sample
        mae = np.mean(np.abs(predictions - y[:5]))
        print(f"   🎯 Sample MAE: {mae:.3f}")
        
        if mae < 10:
            print("   ✅ Model appears to be working!")
            return True
        else:
            print("   ⚠️ High MAE - model may need calibration")
            return True
        
    except Exception as e:
        print(f"   ❌ Model loading failed: {e}")
        return False

if __name__ == "__main__":
    success = test_with_reconstructed_features()
    
    if success:
        print("\n✅ FEATURE RECONSTRUCTION SUCCESSFUL!")
        print("🎯 Ready to test real models with correct features")
    else:
        print("\n❌ Feature reconstruction failed")
