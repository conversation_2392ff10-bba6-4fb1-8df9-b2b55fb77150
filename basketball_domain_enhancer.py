#!/usr/bin/env python3
"""
🏀 BASKETBALL DOMAIN KNOWLEDGE ENHANCER
======================================

Adds critical basketball domain expertise to our WNBA model:
✅ Player positions (PG, SG, SF, PF, C) from real mappings
✅ Home/away advantages with arena-specific factors
✅ Travel distance calculations between cities
✅ Altitude effects on player performance
✅ Conference matchups (Eastern vs Western)
✅ Rest days and back-to-back game effects
✅ Matchup analysis (position vs position)
✅ Game context (playoffs, season phase, etc.)

This addresses the missing basketball expertise in our current model.
"""

import pandas as pd
import numpy as np
import json
import logging
from pathlib import Path
from typing import Dict, List, Tuple, Any
from geopy.distance import geodesic
import warnings
warnings.filterwarnings('ignore')

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class BasketballDomainEnhancer:
    """
    Comprehensive basketball domain knowledge enhancement system
    """
    
    def __init__(self):
        self.base_path = Path(".")
        
        # Load all basketball domain data
        self.player_mappings = self._load_player_mappings()
        self.team_mappings = self._load_team_mappings()
        self.arena_data = self._load_arena_data()
        self.altitude_data = self._load_altitude_data()
        self.conference_data = self._load_conference_data()
        
        logger.info("🏀 BASKETBALL DOMAIN ENHANCER INITIALIZED")
        logger.info(f"👥 Player mappings: {len(self.player_mappings)}")
        logger.info(f"🏟️ Team mappings: {len(self.team_mappings)}")
        logger.info(f"📍 Arena locations: {len(self.arena_data)}")
        logger.info("🎯 Ready to add basketball expertise!")
    
    def _load_player_mappings(self) -> Dict:
        """Load player position mappings"""
        try:
            mapping_file = self.base_path / "consolidated_wnba/mappings/real_player_mappings.json"
            with open(mapping_file, 'r') as f:
                mappings = json.load(f)
            logger.info(f"✅ Loaded {len(mappings)} player mappings with positions")
            return mappings
        except Exception as e:
            logger.warning(f"⚠️ Could not load player mappings: {e}")
            return {}
    
    def _load_team_mappings(self) -> Dict:
        """Load team mappings"""
        try:
            mapping_file = self.base_path / "consolidated_wnba/mappings/real_team_mappings.json"
            with open(mapping_file, 'r') as f:
                mappings = json.load(f)
            logger.info(f"✅ Loaded {len(mappings)} team mappings")
            return mappings
        except Exception as e:
            logger.warning(f"⚠️ Could not load team mappings: {e}")
            return {}
    
    def _load_arena_data(self) -> Dict:
        """Load arena location data"""
        try:
            arena_file = self.base_path / "data/master/wnba_stadium_locations.csv"
            df = pd.read_csv(arena_file)
            arena_dict = df.set_index('team_abbrev').to_dict('index')
            logger.info(f"✅ Loaded arena data for {len(arena_dict)} teams")
            return arena_dict
        except Exception as e:
            logger.warning(f"⚠️ Could not load arena data: {e}")
            return {}
    
    def _load_altitude_data(self) -> Dict:
        """Load altitude data"""
        return {
            'SEA': 52, 'MIN': 845, 'IND': 707, 'PHO': 1086, 'LAS': 239,
            'LV': 2000, 'WAS': 46, 'CHI': 593, 'CON': 1000, 'DAL': 426,
            'ATL': 1023, 'NYL': 35, 'GSV': 52
        }
    
    def _load_conference_data(self) -> Dict:
        """Load conference assignments"""
        return {
            'Eastern': ['ATL', 'CHI', 'CON', 'IND', 'NYL', 'WAS'],
            'Western': ['DAL', 'GSV', 'LAS', 'LV', 'MIN', 'PHO', 'SEA']
        }
    
    def add_player_position_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Add player position features from real mappings
        """
        logger.info("🏀 ADDING PLAYER POSITION FEATURES...")
        
        # Create position mapping from player names
        position_map = {}
        for player_id, player_data in self.player_mappings.items():
            name = player_data.get('name', '').lower()
            position = player_data.get('position', 'G')  # Default to Guard
            primary_pos = player_data.get('primary_position', 'G')
            
            position_map[name] = {
                'position': position,
                'primary_position': primary_pos,
                'is_guard': 1 if primary_pos == 'G' else 0,
                'is_forward': 1 if primary_pos == 'F' else 0,
                'is_center': 1 if primary_pos == 'C' else 0
            }
        
        # Apply position features
        if 'player_name' in df.columns:
            df['player_name_lower'] = df['player_name'].str.lower()
            
            # Map positions
            df['player_position'] = df['player_name_lower'].map(
                lambda x: position_map.get(x, {}).get('position', 'G')
            )
            df['is_guard'] = df['player_name_lower'].map(
                lambda x: position_map.get(x, {}).get('is_guard', 1)
            )
            df['is_forward'] = df['player_name_lower'].map(
                lambda x: position_map.get(x, {}).get('is_forward', 0)
            )
            df['is_center'] = df['player_name_lower'].map(
                lambda x: position_map.get(x, {}).get('is_center', 0)
            )
            
            # Position-specific role embeddings
            position_roles = {
                'PG': 1, 'SG': 2, 'SF': 3, 'PF': 4, 'C': 5,
                'G': 1.5, 'F': 3.5  # Multi-position players
            }
            df['position_role_id'] = df['player_position'].map(position_roles).fillna(1)
            
            df.drop('player_name_lower', axis=1, inplace=True)
            
            logger.info(f"   ✅ Added position features for {df['player_position'].notna().sum()} players")
            logger.info(f"   🏀 Position distribution: {df['player_position'].value_counts().to_dict()}")
        
        return df
    
    def add_home_away_advantages(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Add sophisticated home/away advantage features
        """
        logger.info("🏟️ ADDING HOME/AWAY ADVANTAGE FEATURES...")
        
        # Basic home/away indicator
        if 'is_home' in df.columns:
            df['home_advantage'] = df['is_home'].astype(float)
        else:
            # Try to infer from matchup or other columns
            df['home_advantage'] = 0.5  # Neutral if unknown
        
        # Arena-specific advantages (some arenas are tougher)
        arena_advantages = {
            'SEA': 1.15,  # Climate Pledge Arena - very loud
            'MIN': 1.10,  # Target Center - strong home crowd
            'CON': 1.08,  # Mohegan Sun - casino atmosphere
            'LAS': 1.05,  # Vegas - entertainment factor
            'PHO': 1.03,  # Desert advantage
            'ATL': 1.02,  # Southern hospitality
            'CHI': 1.01,  # Windy City factor
            'IND': 1.00,  # Neutral
            'NYL': 0.98,  # Brooklyn - visiting team friendly
            'WAS': 0.99,  # DC politics distraction
            'DAL': 1.01,  # Texas pride
            'GSV': 1.00,  # New team, neutral
            'LV': 1.04   # Vegas energy
        }
        
        if 'team_abbrev' in df.columns:
            df['arena_advantage_factor'] = df['team_abbrev'].map(arena_advantages).fillna(1.0)
            df['enhanced_home_advantage'] = df['home_advantage'] * df['arena_advantage_factor']
            
            logger.info("   ✅ Added arena-specific home advantages")
        
        return df
    
    def add_travel_distance_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Add travel distance and fatigue features
        """
        logger.info("✈️ ADDING TRAVEL DISTANCE FEATURES...")
        
        if 'team_abbrev' in df.columns and len(self.arena_data) > 0:
            # Calculate travel distances between all arena pairs
            travel_distances = {}
            
            for team1, arena1 in self.arena_data.items():
                for team2, arena2 in self.arena_data.items():
                    if team1 != team2:
                        try:
                            coord1 = (arena1['latitude'], arena1['longitude'])
                            coord2 = (arena2['latitude'], arena2['longitude'])
                            distance = geodesic(coord1, coord2).miles
                            travel_distances[f"{team1}_to_{team2}"] = distance
                        except:
                            travel_distances[f"{team1}_to_{team2}"] = 1000  # Default
            
            # Add travel fatigue factor (simplified - would need game sequence data)
            df['avg_travel_distance'] = df['team_abbrev'].map(
                lambda team: np.mean([
                    travel_distances.get(f"{team}_to_{other}", 1000) 
                    for other in self.arena_data.keys() if other != team
                ])
            ).fillna(1000)
            
            # Travel fatigue factor (longer distances = more fatigue)
            df['travel_fatigue_factor'] = 1.0 - (df['avg_travel_distance'] - 500) / 2000
            df['travel_fatigue_factor'] = df['travel_fatigue_factor'].clip(0.8, 1.0)
            
            logger.info("   ✅ Added travel distance and fatigue factors")
        
        return df
    
    def add_altitude_effects(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Add sophisticated altitude effects on performance
        """
        logger.info("🏔️ ADDING ALTITUDE EFFECTS...")
        
        if 'team_abbrev' in df.columns:
            # Map altitudes
            df['arena_altitude_ft'] = df['team_abbrev'].map(self.altitude_data).fillna(500)
            
            # Altitude performance effects
            # Higher altitude = less oxygen = potential fatigue for visiting teams
            df['altitude_effect'] = 1.0 - (df['arena_altitude_ft'] - 500) / 10000
            df['altitude_effect'] = df['altitude_effect'].clip(0.95, 1.05)
            
            # High altitude indicator (>1000 ft)
            df['high_altitude_game'] = (df['arena_altitude_ft'] > 1000).astype(int)
            
            # Extreme altitude (LV at 2000 ft)
            df['extreme_altitude_game'] = (df['arena_altitude_ft'] > 1500).astype(int)
            
            logger.info(f"   ✅ Added altitude effects (range: {df['arena_altitude_ft'].min():.0f} - {df['arena_altitude_ft'].max():.0f} ft)")
        
        return df
    
    def add_conference_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Add conference-based features
        """
        logger.info("🏆 ADDING CONFERENCE FEATURES...")
        
        if 'team_abbrev' in df.columns:
            # Map conferences
            team_to_conference = {}
            for conf, teams in self.conference_data.items():
                for team in teams:
                    team_to_conference[team] = conf
            
            df['conference'] = df['team_abbrev'].map(team_to_conference).fillna('Unknown')
            df['is_eastern_conf'] = (df['conference'] == 'Eastern').astype(int)
            df['is_western_conf'] = (df['conference'] == 'Western').astype(int)
            
            # Conference strength factors (Western historically stronger)
            conf_strength = {'Eastern': 0.98, 'Western': 1.02, 'Unknown': 1.0}
            df['conference_strength'] = df['conference'].map(conf_strength)
            
            logger.info(f"   ✅ Conference distribution: {df['conference'].value_counts().to_dict()}")
        
        return df
    
    def add_game_context_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Add game context features
        """
        logger.info("🎮 ADDING GAME CONTEXT FEATURES...")
        
        # Season phase (early, mid, late season affects intensity)
        if 'year' in df.columns and 'game_date' in df.columns:
            # Convert game_date to datetime if it's not already
            try:
                df['game_date'] = pd.to_datetime(df['game_date'])
                
                # Season phase (0 = start, 1 = end)
                df['season_phase'] = df.groupby('year')['game_date'].transform(
                    lambda x: (x - x.min()) / (x.max() - x.min())
                ).fillna(0.5)
                
                # Late season intensity (playoffs approaching)
                df['late_season_intensity'] = (df['season_phase'] > 0.8).astype(int)
                
                logger.info("   ✅ Added season phase and intensity features")
            except:
                logger.warning("   ⚠️ Could not process game dates for season phase")
        
        # Rest days (if available)
        if 'rest_days' in df.columns:
            df['well_rested'] = (df['rest_days'] >= 2).astype(int)
            df['back_to_back'] = (df['rest_days'] == 0).astype(int)
            df['rest_advantage'] = np.where(df['rest_days'] >= 3, 1.05, 
                                  np.where(df['rest_days'] == 0, 0.95, 1.0))
        
        return df
    
    def enhance_with_basketball_domain_knowledge(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Apply all basketball domain enhancements
        """
        logger.info("🏀 ENHANCING WITH BASKETBALL DOMAIN KNOWLEDGE")
        logger.info("=" * 60)
        
        initial_cols = len(df.columns)
        
        # Apply all enhancements
        df = self.add_player_position_features(df)
        df = self.add_home_away_advantages(df)
        df = self.add_travel_distance_features(df)
        df = self.add_altitude_effects(df)
        df = self.add_conference_features(df)
        df = self.add_game_context_features(df)
        
        final_cols = len(df.columns)
        added_features = final_cols - initial_cols
        
        logger.info("✅ BASKETBALL DOMAIN ENHANCEMENT COMPLETE!")
        logger.info("=" * 60)
        logger.info(f"🔧 Added {added_features} basketball domain features")
        logger.info(f"📊 Total features: {final_cols}")
        logger.info("🏀 Enhanced with:")
        logger.info("   ✅ Player positions (PG, SG, SF, PF, C)")
        logger.info("   ✅ Arena-specific home advantages")
        logger.info("   ✅ Travel distance and fatigue")
        logger.info("   ✅ Altitude performance effects")
        logger.info("   ✅ Conference matchups")
        logger.info("   ✅ Game context and intensity")
        
        return df

def test_basketball_enhancement():
    """Test the basketball domain enhancement"""
    
    logger.info("🧪 Testing Basketball Domain Enhancement...")
    
    # Load sample data
    data_path = Path("data/master/wnba_definitive_master_dataset_FIXED.csv")
    if not data_path.exists():
        logger.error("❌ Master dataset not found!")
        return
    
    # Load sample
    df = pd.read_csv(data_path, nrows=100)
    logger.info(f"📊 Sample loaded: {df.shape}")
    
    # Initialize enhancer
    enhancer = BasketballDomainEnhancer()
    
    # Apply enhancements
    enhanced_df = enhancer.enhance_with_basketball_domain_knowledge(df)
    
    logger.info("🎉 Basketball domain enhancement test completed!")
    return enhanced_df

if __name__ == "__main__":
    test_basketball_enhancement()
