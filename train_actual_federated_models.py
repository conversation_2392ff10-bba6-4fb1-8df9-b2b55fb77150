
# 🎯 UPDATED TO USE EXPERT DATASET
# This script now uses the consolidated expert dataset: data/master/wnba_expert_dataset.csv
# Updated on: 2025-07-12 20:00:10
# Expert dataset contains: 49,512 high-quality records with 840 features
# All duplicates removed, data quality validated

#!/usr/bin/env python3
"""
🏋️ ACTUAL FEDERATED MODEL TRAINING
=================================

This version ACTUALLY trains the neural network models:
✅ Real PyTorch model training with gradients
✅ Actual forward/backward passes
✅ Real loss computation and optimization
✅ Saved model weights for deployment
✅ Real predictions and evaluation

Building on our federated multiverse system but with REAL training.
"""

import pandas as pd
import numpy as np
import json
import logging
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Tuple, Any
import warnings
warnings.filterwarnings('ignore')

# PyTorch and Lightning
import torch
import torch.nn as nn
import pytorch_lightning as pl
from pytorch_lightning.callbacks import EarlyStopping, ModelCheckpoint
from pytorch_lightning.loggers import TensorBoardLogger
from pytorch_lightning import seed_everything
from torch.utils.data import DataLoader, TensorDataset

# Our model
from src.models.modern_player_points_model import PlayerPointsModel, WNBADataModule

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# ACTUAL TRAINING CONFIGURATION
RANDOM_SEED = 42
TARGET_FEATURES = 150
ACTUAL_EPOCHS = 20  # Real training epochs
BATCH_SIZE = 128
LEARNING_RATE = 0.001

class ActualFederatedTrainer:
    """
    Trainer that ACTUALLY trains the models with real gradients
    """
    
    def __init__(self):
        self.random_seed = RANDOM_SEED
        seed_everything(self.random_seed, workers=True)
        
        logger.info("🏋️ ACTUAL FEDERATED TRAINER INITIALIZED")
        logger.info("🎯 This will ACTUALLY train neural networks with real gradients!")
    
    def prepare_clean_data_for_training(self) -> Tuple[pd.DataFrame, pd.DataFrame, pd.DataFrame, List[str]]:
        """Prepare clean data for actual training"""
        
        logger.info("🧹 PREPARING CLEAN DATA FOR ACTUAL TRAINING")
        
        # Load master dataset
        data_path = Path("data/master/wnba_expert_dataset.csv")
        df = pd.read_csv(data_path)
        logger.info(f"📊 Master dataset: {df.shape}")
        
        # Apply noise reduction
        if 'target' in df.columns:
            clean_mask = (df['target'] >= 0) & (df['target'] <= 40)
            df = df[clean_mask].copy()
            logger.info(f"   🧹 Removed extreme values: {df.shape}")
        
        # Add domain features
        if 'team_abbrev' in df.columns:
            altitude_map = {
                'SEA': 52, 'MIN': 845, 'IND': 707, 'PHO': 1086, 'LAS': 239,
                'LV': 2000, 'WAS': 46, 'CHI': 593, 'CON': 1000, 'DAL': 426,
                'ATL': 1023, 'NYL': 35, 'GSV': 52
            }
            df['arena_altitude'] = df['team_abbrev'].map(altitude_map).fillna(500)
            df['altitude_effect'] = 1.0 - (df['arena_altitude'] - 500) / 10000
            
            eastern_teams = ['ATL', 'CHI', 'CON', 'IND', 'NYL', 'WAS']
            df['is_eastern'] = df['team_abbrev'].isin(eastern_teams).astype(int)
        
        # Select features
        exclude_cols = [
            'target', 'player_name', 'team_abbrev', 'game_id', 'game_date', 
            'year', 'player_id', 'team_id', 'season', 'SEASON_ID', 'GAME_DATE',
            'collection_date'
        ]
        feature_cols = [col for col in df.columns if col not in exclude_cols]
        
        # Handle missing data and select top features
        for col in feature_cols:
            if col in df.columns:
                if df[col].dtype in ['object', 'string']:
                    df[col] = pd.to_numeric(df[col], errors='coerce')
                df[col] = df[col].fillna(df[col].median())
        
        # Select top features by variance
        numeric_features = df[feature_cols].select_dtypes(include=[np.number]).columns
        feature_variance = df[numeric_features].var().sort_values(ascending=False)
        best_features = feature_variance.head(TARGET_FEATURES).index.tolist()
        
        # Temporal splits
        if 'year' in df.columns:
            train_mask = df['year'] <= 2022
            val_mask = df['year'] == 2023
            test_mask = df['year'] >= 2024
            
            train_df = df[train_mask].copy()
            val_df = df[val_mask].copy()
            test_df = df[test_mask].copy()
        
        logger.info(f"✅ Clean data prepared:")
        logger.info(f"   📈 Train: {len(train_df):,} samples")
        logger.info(f"   📊 Val: {len(val_df):,} samples")
        logger.info(f"   🧪 Test: {len(test_df):,} samples")
        logger.info(f"   🔧 Features: {len(best_features)}")
        
        return train_df, val_df, test_df, best_features
    
    def create_data_loaders(self, train_df: pd.DataFrame, val_df: pd.DataFrame, 
                           test_df: pd.DataFrame, features: List[str]) -> Tuple[DataLoader, DataLoader, DataLoader]:
        """Create PyTorch data loaders for actual training"""
        
        logger.info("📦 CREATING PYTORCH DATA LOADERS")
        
        def df_to_tensors(df, features):
            X = torch.FloatTensor(df[features].values)
            y = torch.FloatTensor(df['target'].values)
            return X, y
        
        # Convert to tensors
        X_train, y_train = df_to_tensors(train_df, features)
        X_val, y_val = df_to_tensors(val_df, features)
        X_test, y_test = df_to_tensors(test_df, features)
        
        # Create datasets
        train_dataset = TensorDataset(X_train, y_train)
        val_dataset = TensorDataset(X_val, y_val)
        test_dataset = TensorDataset(X_test, y_test)
        
        # Create data loaders
        train_loader = DataLoader(train_dataset, batch_size=BATCH_SIZE, shuffle=True, num_workers=0)
        val_loader = DataLoader(val_dataset, batch_size=BATCH_SIZE, shuffle=False, num_workers=0)
        test_loader = DataLoader(test_dataset, batch_size=BATCH_SIZE, shuffle=False, num_workers=0)
        
        logger.info(f"✅ Data loaders created:")
        logger.info(f"   📈 Train batches: {len(train_loader)}")
        logger.info(f"   📊 Val batches: {len(val_loader)}")
        logger.info(f"   🧪 Test batches: {len(test_loader)}")
        
        return train_loader, val_loader, test_loader
    
    def train_actual_model(self) -> Dict[str, Any]:
        """Train an actual PyTorch model with real gradients"""
        
        logger.info("🏋️ STARTING ACTUAL MODEL TRAINING")
        logger.info("=" * 50)
        
        training_start = datetime.now()
        
        # Prepare data
        train_df, val_df, test_df, features = self.prepare_clean_data_for_training()
        
        # Create data module (Lightning approach)
        data_module = WNBADataModule(
            train_df=train_df,
            val_df=val_df,
            test_df=test_df,
            feature_cols=features,
            target_col='target',
            batch_size=BATCH_SIZE,
            num_workers=0  # Set to 0 to avoid multiprocessing issues
        )
        
        # Create actual model
        model = PlayerPointsModel(
            input_dim=len(features),
            dropout=0.2,
            learning_rate=LEARNING_RATE,
            use_role_embedding=False  # Simplified for actual training
        )
        
        logger.info(f"🤖 Model created:")
        logger.info(f"   🔧 Input dim: {len(features)}")
        logger.info(f"   📉 Learning rate: {LEARNING_RATE}")
        logger.info(f"   🛡️ Dropout: 0.2")
        
        # Callbacks for actual training
        callbacks = [
            EarlyStopping(
                monitor='val_loss',
                patience=5,
                mode='min',
                verbose=True,
                min_delta=0.001
            ),
            ModelCheckpoint(
                dirpath='models/actual_trained',
                filename='actual_model_{epoch:02d}_{val_loss:.4f}',
                monitor='val_loss',
                mode='min',
                save_top_k=3,
                verbose=True
            )
        ]
        
        # Trainer for actual training
        trainer = pl.Trainer(
            max_epochs=ACTUAL_EPOCHS,
            callbacks=callbacks,
            logger=TensorBoardLogger('models/actual_trained/logs', name='actual_training'),
            accelerator='auto',
            devices='auto',
            precision='16-mixed',
            gradient_clip_val=1.0,
            deterministic=True,
            enable_progress_bar=True,
            log_every_n_steps=10
        )
        
        logger.info("🚀 Starting ACTUAL training with real gradients...")
        logger.info(f"📊 Max epochs: {ACTUAL_EPOCHS}")
        logger.info(f"⚡ Using device: {trainer.accelerator}")
        
        # ACTUAL TRAINING HAPPENS HERE
        trainer.fit(model, data_module)
        
        # Training completed
        training_end = datetime.now()
        duration = training_end - training_start
        
        # Get results
        best_path = trainer.checkpoint_callback.best_model_path
        best_score = trainer.checkpoint_callback.best_model_score
        
        logger.info("✅ ACTUAL TRAINING COMPLETE!")
        logger.info(f"🏆 Best model saved: {best_path}")
        logger.info(f"📊 Best val_loss: {best_score:.4f}")
        logger.info(f"⏱️ Training duration: {duration}")
        
        # Test the trained model
        logger.info("🧪 Testing trained model...")
        test_results = trainer.test(model, data_module)
        
        # Save comprehensive results
        results = {
            'timestamp': training_start.isoformat(),
            'duration_seconds': duration.total_seconds(),
            'model_actually_trained': True,
            'training_config': {
                'epochs': ACTUAL_EPOCHS,
                'batch_size': BATCH_SIZE,
                'learning_rate': LEARNING_RATE,
                'features': len(features)
            },
            'data_info': {
                'train_samples': len(train_df),
                'val_samples': len(val_df),
                'test_samples': len(test_df),
                'features_selected': TARGET_FEATURES
            },
            'training_results': {
                'best_model_path': best_path,
                'best_val_loss': float(best_score),
                'test_results': test_results
            },
            'noise_reduction_applied': True,
            'domain_knowledge_added': True,
            'real_gradients_used': True
        }
        
        # Save results
        results_path = Path('models/actual_trained/actual_training_results.json')
        results_path.parent.mkdir(parents=True, exist_ok=True)
        with open(results_path, 'w') as f:
            json.dump(results, f, indent=2)
        
        logger.info(f"📋 Results saved: {results_path}")
        logger.info("🎯 ACTUAL MODEL READY FOR PREDICTIONS!")
        
        return results

def main():
    """Main function for actual training"""
    
    logger.info("🏋️ STARTING ACTUAL WNBA MODEL TRAINING")
    logger.info("This will train a real neural network with actual gradients!")
    
    trainer = ActualFederatedTrainer()
    results = trainer.train_actual_model()
    
    logger.info("🎉 ACTUAL TRAINING COMPLETE!")
    logger.info("✅ Real neural network trained with:")
    logger.info("   🧹 Clean data (noise reduced)")
    logger.info("   🏀 Basketball domain knowledge")
    logger.info("   ⚡ Real PyTorch gradients")
    logger.info("   💾 Saved model weights")
    logger.info("   🎯 Ready for live predictions!")
    
    return results

if __name__ == "__main__":
    main()
