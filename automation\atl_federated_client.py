#!/usr/bin/env python3
"""
🏀 ATL FEDERATED MULTIVERSE CLIENT
===================================

Federated learning client for ATL team.
Trains multiverse ensemble models on private team data.

Usage: python atl_federated_client.py
"""

import sys
from pathlib import Path

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))

from federated_multiverse_integration import FederatedMultiverseClient
import flwr as fl
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def main():
    """Launch ATL federated multiverse client"""

    logger.info(f"🏀 Starting ATL Federated Multiverse Client")

    # Initialize client with REAL data
    client = FederatedMultiverseClient(
        team_id="ATL",
        data_path=None  # Will auto-load from real WNBA master dataset
    )

    # Connect to federated server
    try:
        fl.client.start_numpy_client(
            server_address="localhost:8080",  # Adjust server address as needed
            client=client
        )
    except Exception as e:
        logger.error(f"❌ Failed to connect to federated server: {e}")
        return False

    logger.info(f"✅ ATL client session completed")
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        exit(1)
